
/* ************************************************************************************************ */
/* Fluid Padding Spacing - Useful for section paddings e.g, <section class="container-fluid-md"> */
.container-fluid-xs {
    padding-block: var(--mico-size-fluid-xs) !important;
    padding-inline: var(--mico-size-fluid-lg) !important;
    margin-inline: auto !important;
  }
  
  .container-fluid-sm {
    padding-block: var(--mico-size-fluid-sm) !important;
    padding-inline: var(--mico-size-fluid-lg) !important;
    margin-inline: auto !important;
  }
  
  .container-fluid-md {
    padding-block: var(--mico-size-fluid-md) !important;
    padding-inline: var(--mico-size-fluid-lg) !important;
    margin-inline: auto !important;
  }
  .container-fluid-lg {
    padding-block: var(--mico-size-fluid-lg) !important;
    padding-inline: var(--mico-size-fluid-lg) !important;
    margin-inline: auto !important;
  }
  
  .container-fluid-xl {
    padding-block: var(--mico-size-fluid-xl) !important;
    padding-inline: var(--mico-size-fluid-xl) !important;
    margin-inline: auto !important;
  }
  .container-fluid-2xl {
    padding-block: var(--mico-size-fluid-2xl) !important;
    padding-inline: var(--mico-size-fluid-2xl) !important;
    margin-inline: auto !important;
  }

/* Fluid Padding Spacing - Useful for individual sides, x-axis or y-axis paddings e.g, <div class="container-fluid-md"> */
.px-fluid-xs { padding-inline: var(--mico-size-fluid-xs) !important; }
.py-fluid-xs { padding-block: var(--mico-size-fluid-xs) !important; }

.px-fluid-sm { padding-inline: var(--mico-size-fluid-sm) !important; }
.py-fluid-sm { padding-block: var(--mico-size-fluid-sm) !important; }

.px-fluid-md { padding-inline: var(--mico-size-fluid-md) !important; }
.py-fluid-md { padding-block: var(--mico-size-fluid-md) !important; }

.px-fluid-lg { padding-inline: var(--mico-size-fluid-lg) !important; }
.py-fluid-lg { padding-block: var(--mico-size-fluid-lg) !important; }

.px-fluid-xl { padding-inline: var(--mico-size-fluid-xl) !important; }
.py-fluid-xl { padding-block: var(--mico-size-fluid-xl) !important; }

.px-fluid-2xl { padding-inline: var(--mico-size-fluid-2xl) !important; }
.py-fluid-2xl { padding-block: var(--mico-size-fluid-2xl) !important; }

.p-fluid-sm { padding: var(--mico-size-fluid-sm) !important; }
.p-fluid-md { padding: var(--mico-size-fluid-md) !important; }
.p-fluid-lg { padding: var(--mico-size-fluid-lg) !important; }
.p-fluid-xl { padding: var(--mico-size-fluid-xl) !important; }
.p-fluid-2xl { padding: var(--mico-size-fluid-2xl) !important; }

.pt-fluid-sm{ padding-top: var(--mico-size-fluid-sm) !important; }
.pt-fluid-md{ padding-top: var(--mico-size-fluid-md) !important; }
.pt-fluid-lg{ padding-top: var(--mico-size-fluid-lg) !important; }
.pt-fluid-xl{ padding-top: var(--mico-size-fluid-xl) !important; }
.pt-fluid-2xl{ padding-top: var(--mico-size-fluid-2xl) !important; }

.pl-fluid-sm{ padding-left: var(--mico-size-fluid-sm) !important; }
.pl-fluid-md{ padding-left: var(--mico-size-fluid-md) !important; }
.pl-fluid-lg{ padding-left: var(--mico-size-fluid-lg) !important; }
.pl-fluid-xl{ padding-left: var(--mico-size-fluid-xl) !important; }
.pl-fluid-2xl{ padding-left: var(--mico-size-fluid-2xl) !important; }

.pb-fluid-sm{ padding-bottom: var(--mico-size-fluid-sm) !important; }
.pb-fluid-md{ padding-bottom: var(--mico-size-fluid-md) !important; }
.pb-fluid-lg{ padding-bottom: var(--mico-size-fluid-lg) !important; }
.pb-fluid-xl{ padding-bottom: var(--mico-size-fluid-xl) !important; }
.pb-fluid-2xl{ padding-bottom: var(--mico-size-fluid-2xl) !important; }

.pr-fluid-sm{ padding-right: var(--mico-size-fluid-sm) !important; }
.pr-fluid-md{ padding-right: var(--mico-size-fluid-md) !important; }
.pr-fluid-lg{ padding-right: var(--mico-size-fluid-lg) !important; }
.pr-fluid-xl{ padding-right: var(--mico-size-fluid-xl) !important; }
.pr-fluid-2xl{ padding-right: var(--mico-size-fluid-2xl) !important; }


/* Margin */
.m-0 { margin: 0 !important; }
.mt-0 { margin-top: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }
.mr-0 { margin-right: 0 !important; }

.m-auto { margin: auto !important; }
.mt-auto { margin-top: auto !important; }
.mb-auto { margin-bottom: auto !important; }
.ml-auto { margin-left: auto !important; }
.mr-auto { margin-right: auto !important; }

.mx-auto { margin-inline: auto !important; }
.my-auto { margin-block: auto !important; }

/* Padding */
.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }
.pr-0 { padding-right: 0 !important; }
.px-0 { padding-inline: 0 !important; }
.py-0 { padding-block: 0 !important; }
.p-auto { padding: auto !important; }
.pt-auto { padding-top: auto !important; }
.pb-auto { padding-bottom: auto !important; }
.pl-auto { padding-left: auto !important; }
.pr-auto { padding-right: auto !important; }
.px-auto { padding-inline: auto !important; }
.py-auto { padding-block: auto !important; }

/* Margin classes from 4 to 100 */
.m-4 { margin: var(--mico-size-4) !important; }
.m-8 { margin: var(--mico-size-8) !important; }
.m-12 { margin: var(--mico-size-12) !important; }
.m-16 { margin: var(--mico-size-16) !important; }
.m-20 { margin: var(--mico-size-20) !important; }
.m-24 { margin: var(--mico-size-24) !important; }
.m-28 { margin: var(--mico-size-28) !important; }
.m-32 { margin: var(--mico-size-32) !important; }
.m-36 { margin: var(--mico-size-36) !important; }
.m-40 { margin: var(--mico-size-40) !important; }
.m-44 { margin: var(--mico-size-44) !important; }
.m-48 { margin: var(--mico-size-48) !important; }
.m-52 { margin: var(--mico-size-52) !important; }
.m-56 { margin: var(--mico-size-56) !important; }
.m-60 { margin: var(--mico-size-60) !important; }
.m-64 { margin: var(--mico-size-64) !important; }
.m-68 { margin: var(--mico-size-68) !important; }
.m-72 { margin: var(--mico-size-72) !important; }
.m-76 { margin: var(--mico-size-76) !important; }
.m-80 { margin: var(--mico-size-80) !important; }
.m-84 { margin: var(--mico-size-84) !important; }
.m-88 { margin: var(--mico-size-88) !important; }
.m-92 { margin: var(--mico-size-92) !important; }
.m-96 { margin: var(--mico-size-96) !important; }
.m-100 { margin: var(--mico-size-100) !important; }
.m-104 { margin: var(--mico-size-104) !important; }
.m-108 { margin: var(--mico-size-108) !important; }
.m-112 { margin: var(--mico-size-112) !important; }
.m-116 { margin: var(--mico-size-116) !important; }
.m-120 { margin: var(--mico-size-120) !important; }
.m-124 { margin: var(--mico-size-124) !important; }
.m-128 { margin: var(--mico-size-128) !important; }
.m-132 { margin: var(--mico-size-132) !important; }
.m-136 { margin: var(--mico-size-136) !important; }
.m-140 { margin: var(--mico-size-140) !important; }
.m-144 { margin: var(--mico-size-144) !important; }
.m-148 { margin: var(--mico-size-148) !important; }
.m-152 { margin: var(--mico-size-152) !important; }
.m-156 { margin: var(--mico-size-156) !important; }
.m-160 { margin: var(--mico-size-160) !important; }
.m-164 { margin: var(--mico-size-164) !important; }
.m-168 { margin: var(--mico-size-168) !important; }
.m-172 { margin: var(--mico-size-172) !important; }
.m-176 { margin: var(--mico-size-176) !important; }
.m-180 { margin: var(--mico-size-180) !important; }
.m-184 { margin: var(--mico-size-184) !important; }
.m-188 { margin: var(--mico-size-188) !important; }
.m-192 { margin: var(--mico-size-192) !important; }
.m-196 { margin: var(--mico-size-196) !important; }
.m-200 { margin: var(--mico-size-200) !important; }
.m-204 { margin: var(--mico-size-204) !important; }
.m-208 { margin: var(--mico-size-208) !important; }
.m-212 { margin: var(--mico-size-212) !important; }
.m-216 { margin: var(--mico-size-216) !important; }
.m-220 { margin: var(--mico-size-220) !important; }
.m-224 { margin: var(--mico-size-224) !important; }
.m-228 { margin: var(--mico-size-228) !important; }
.m-232 { margin: var(--mico-size-232) !important; }
.m-236 { margin: var(--mico-size-236) !important; }
.m-240 { margin: var(--mico-size-240) !important; }
.m-244 { margin: var(--mico-size-244) !important; }
.m-248 { margin: var(--mico-size-248) !important; }
.m-252 { margin: var(--mico-size-252) !important; }
.m-256 { margin: var(--mico-size-256) !important; }
.m-260 { margin: var(--mico-size-260) !important; }
.m-264 { margin: var(--mico-size-264) !important; }
.m-268 { margin: var(--mico-size-268) !important; }
.m-272 { margin: var(--mico-size-272) !important; }
.m-276 { margin: var(--mico-size-276) !important; }
.m-280 { margin: var(--mico-size-280) !important; }
.m-284 { margin: var(--mico-size-284) !important; }
.m-288 { margin: var(--mico-size-288) !important; }
.m-292 { margin: var(--mico-size-292) !important; }
.m-296 { margin: var(--mico-size-296) !important; }
.m-300 { margin: var(--mico-size-300) !important; }
.m-304 { margin: var(--mico-size-304) !important; }
.m-308 { margin: var(--mico-size-308) !important; }
.m-312 { margin: var(--mico-size-312) !important; }
.m-316 { margin: var(--mico-size-316) !important; }
.m-320 { margin: var(--mico-size-320) !important; }
.m-324 { margin: var(--mico-size-324) !important; }
.m-328 { margin: var(--mico-size-328) !important; }
.m-332 { margin: var(--mico-size-332) !important; }
.m-336 { margin: var(--mico-size-336) !important; }
.m-340 { margin: var(--mico-size-340) !important; }
.m-344 { margin: var(--mico-size-344) !important; }
.m-348 { margin: var(--mico-size-348) !important; }
.m-352 { margin: var(--mico-size-352) !important; }
.m-356 { margin: var(--mico-size-356) !important; }
.m-360 { margin: var(--mico-size-360) !important; }
.m-364 { margin: var(--mico-size-364) !important; }
.m-368 { margin: var(--mico-size-368) !important; }
.m-372 { margin: var(--mico-size-372) !important; }
.m-376 { margin: var(--mico-size-376) !important; }
.m-380 { margin: var(--mico-size-380) !important; }
.m-384 { margin: var(--mico-size-384) !important; }
.m-388 { margin: var(--mico-size-388) !important; }
.m-392 { margin: var(--mico-size-392) !important; }
.m-396 { margin: var(--mico-size-396) !important; }
.m-400 { margin: var(--mico-size-400) !important; }


/* Margin-top classes from 4 to 100 */
.mt-4 { margin-top: var(--mico-size-4) !important; }
.mt-8 { margin-top: var(--mico-size-8) !important; }
.mt-12 { margin-top: var(--mico-size-12) !important; }
.mt-16 { margin-top: var(--mico-size-16) !important; }
.mt-20 { margin-top: var(--mico-size-20) !important; }
.mt-24 { margin-top: var(--mico-size-24) !important; }
.mt-28 { margin-top: var(--mico-size-28) !important; }
.mt-32 { margin-top: var(--mico-size-32) !important; }
.mt-36 { margin-top: var(--mico-size-36) !important; }
.mt-40 { margin-top: var(--mico-size-40) !important; }
.mt-44 { margin-top: var(--mico-size-44) !important; }
.mt-48 { margin-top: var(--mico-size-48) !important; }
.mt-52 { margin-top: var(--mico-size-52) !important; }
.mt-56 { margin-top: var(--mico-size-56) !important; }
.mt-60 { margin-top: var(--mico-size-60) !important; }
.mt-64 { margin-top: var(--mico-size-64) !important; }
.mt-68 { margin-top: var(--mico-size-68) !important; }
.mt-72 { margin-top: var(--mico-size-72) !important; }
.mt-76 { margin-top: var(--mico-size-76) !important; }
.mt-80 { margin-top: var(--mico-size-80) !important; }
.mt-84 { margin-top: var(--mico-size-84) !important; }
.mt-88 { margin-top: var(--mico-size-88) !important; }
.mt-92 { margin-top: var(--mico-size-92) !important; }
.mt-96 { margin-top: var(--mico-size-96) !important; }
.mt-100 { margin-top: var(--mico-size-100) !important; }
.mt-104 { margin-top: var(--mico-size-104) !important; }
.mt-108 { margin-top: var(--mico-size-108) !important; }
.mt-112 { margin-top: var(--mico-size-112) !important; }
.mt-116 { margin-top: var(--mico-size-116) !important; }
.mt-120 { margin-top: var(--mico-size-120) !important; }
.mt-124 { margin-top: var(--mico-size-124) !important; }
.mt-128 { margin-top: var(--mico-size-128) !important; }
.mt-132 { margin-top: var(--mico-size-132) !important; }
.mt-136 { margin-top: var(--mico-size-136) !important; }
.mt-140 { margin-top: var(--mico-size-140) !important; }
.mt-144 { margin-top: var(--mico-size-144) !important; }
.mt-148 { margin-top: var(--mico-size-148) !important; }
.mt-152 { margin-top: var(--mico-size-152) !important; }
.mt-156 { margin-top: var(--mico-size-156) !important; }
.mt-160 { margin-top: var(--mico-size-160) !important; }
.mt-164 { margin-top: var(--mico-size-164) !important; }
.mt-168 { margin-top: var(--mico-size-168) !important; }
.mt-172 { margin-top: var(--mico-size-172) !important; }
.mt-176 { margin-top: var(--mico-size-176) !important; }
.mt-180 { margin-top: var(--mico-size-180) !important; }
.mt-184 { margin-top: var(--mico-size-184) !important; }
.mt-188 { margin-top: var(--mico-size-188) !important; }
.mt-192 { margin-top: var(--mico-size-192) !important; }
.mt-196 { margin-top: var(--mico-size-196) !important; }
.mt-200 { margin-top: var(--mico-size-200) !important; }
.mt-204 { margin-top: var(--mico-size-204) !important; }
.mt-208 { margin-top: var(--mico-size-208) !important; }
.mt-212 { margin-top: var(--mico-size-212) !important; }
.mt-216 { margin-top: var(--mico-size-216) !important; }
.mt-220 { margin-top: var(--mico-size-220) !important; }
.mt-224 { margin-top: var(--mico-size-224) !important; }
.mt-228 { margin-top: var(--mico-size-228) !important; }
.mt-232 { margin-top: var(--mico-size-232) !important; }
.mt-236 { margin-top: var(--mico-size-236) !important; }
.mt-240 { margin-top: var(--mico-size-240) !important; }
.mt-244 { margin-top: var(--mico-size-244) !important; }
.mt-248 { margin-top: var(--mico-size-248) !important; }
.mt-252 { margin-top: var(--mico-size-252) !important; }
.mt-256 { margin-top: var(--mico-size-256) !important; }
.mt-260 { margin-top: var(--mico-size-260) !important; }
.mt-264 { margin-top: var(--mico-size-264) !important; }
.mt-268 { margin-top: var(--mico-size-268) !important; }
.mt-272 { margin-top: var(--mico-size-272) !important; }
.mt-276 { margin-top: var(--mico-size-276) !important; }
.mt-280 { margin-top: var(--mico-size-280) !important; }
.mt-284 { margin-top: var(--mico-size-284) !important; }
.mt-288 { margin-top: var(--mico-size-288) !important; }
.mt-292 { margin-top: var(--mico-size-292) !important; }
.mt-296 { margin-top: var(--mico-size-296) !important; }
.mt-300 { margin-top: var(--mico-size-300) !important; }
.mt-304 { margin-top: var(--mico-size-304) !important; }
.mt-308 { margin-top: var(--mico-size-308) !important; }
.mt-312 { margin-top: var(--mico-size-312) !important; }
.mt-316 { margin-top: var(--mico-size-316) !important; }
.mt-320 { margin-top: var(--mico-size-320) !important; }
.mt-324 { margin-top: var(--mico-size-324) !important; }
.mt-328 { margin-top: var(--mico-size-328) !important; }
.mt-332 { margin-top: var(--mico-size-332) !important; }
.mt-336 { margin-top: var(--mico-size-336) !important; }
.mt-340 { margin-top: var(--mico-size-340) !important; }
.mt-344 { margin-top: var(--mico-size-344) !important; }
.mt-348 { margin-top: var(--mico-size-348) !important; }
.mt-352 { margin-top: var(--mico-size-352) !important; }
.mt-356 { margin-top: var(--mico-size-356) !important; }
.mt-360 { margin-top: var(--mico-size-360) !important; }
.mt-364 { margin-top: var(--mico-size-364) !important; }
.mt-368 { margin-top: var(--mico-size-368) !important; }
.mt-372 { margin-top: var(--mico-size-372) !important; }
.mt-376 { margin-top: var(--mico-size-376) !important; }
.mt-380 { margin-top: var(--mico-size-380) !important; }
.mt-384 { margin-top: var(--mico-size-384) !important; }
.mt-388 { margin-top: var(--mico-size-388) !important; }
.mt-392 { margin-top: var(--mico-size-392) !important; }
.mt-396 { margin-top: var(--mico-size-396) !important; }
.mt-400 { margin-top: var(--mico-size-400) !important; }


/* Margin-bottom classes from 4 to 100 */
.mb-4 { margin-bottom: var(--mico-size-4) !important; }
.mb-8 { margin-bottom: var(--mico-size-8) !important; }
.mb-12 { margin-bottom: var(--mico-size-12) !important; }
.mb-16 { margin-bottom: var(--mico-size-16) !important; }
.mb-20 { margin-bottom: var(--mico-size-20) !important; }
.mb-24 { margin-bottom: var(--mico-size-24) !important; }
.mb-28 { margin-bottom: var(--mico-size-28) !important; }
.mb-32 { margin-bottom: var(--mico-size-32) !important; }
.mb-36 { margin-bottom: var(--mico-size-36) !important; }
.mb-40 { margin-bottom: var(--mico-size-40) !important; }
.mb-44 { margin-bottom: var(--mico-size-44) !important; }
.mb-48 { margin-bottom: var(--mico-size-48) !important; }
.mb-52 { margin-bottom: var(--mico-size-52) !important; }
.mb-56 { margin-bottom: var(--mico-size-56) !important; }
.mb-60 { margin-bottom: var(--mico-size-60) !important; }
.mb-64 { margin-bottom: var(--mico-size-64) !important; }
.mb-68 { margin-bottom: var(--mico-size-68) !important; }
.mb-72 { margin-bottom: var(--mico-size-72) !important; }
.mb-76 { margin-bottom: var(--mico-size-76) !important; }
.mb-80 { margin-bottom: var(--mico-size-80) !important; }
.mb-84 { margin-bottom: var(--mico-size-84) !important; }
.mb-88 { margin-bottom: var(--mico-size-88) !important; }
.mb-92 { margin-bottom: var(--mico-size-92) !important; }
.mb-96 { margin-bottom: var(--mico-size-96) !important; }
.mb-100 { margin-bottom: var(--mico-size-100) !important; }
.mb-104 { margin-bottom: var(--mico-size-104) !important; }
.mb-108 { margin-bottom: var(--mico-size-108) !important; }
.mb-112 { margin-bottom: var(--mico-size-112) !important; }
.mb-116 { margin-bottom: var(--mico-size-116) !important; }
.mb-120 { margin-bottom: var(--mico-size-120) !important; }
.mb-124 { margin-bottom: var(--mico-size-124) !important; }
.mb-128 { margin-bottom: var(--mico-size-128) !important; }
.mb-132 { margin-bottom: var(--mico-size-132) !important; }
.mb-136 { margin-bottom: var(--mico-size-136) !important; }
.mb-140 { margin-bottom: var(--mico-size-140) !important; }
.mb-144 { margin-bottom: var(--mico-size-144) !important; }
.mb-148 { margin-bottom: var(--mico-size-148) !important; }
.mb-152 { margin-bottom: var(--mico-size-152) !important; }
.mb-156 { margin-bottom: var(--mico-size-156) !important; }
.mb-160 { margin-bottom: var(--mico-size-160) !important; }
.mb-164 { margin-bottom: var(--mico-size-164) !important; }
.mb-168 { margin-bottom: var(--mico-size-168) !important; }
.mb-172 { margin-bottom: var(--mico-size-172) !important; }
.mb-176 { margin-bottom: var(--mico-size-176) !important; }
.mb-180 { margin-bottom: var(--mico-size-180) !important; }
.mb-184 { margin-bottom: var(--mico-size-184) !important; }
.mb-188 { margin-bottom: var(--mico-size-188) !important; }
.mb-192 { margin-bottom: var(--mico-size-192) !important; }
.mb-196 { margin-bottom: var(--mico-size-196) !important; }
.mb-200 { margin-bottom: var(--mico-size-200) !important; }
.mb-204 { margin-bottom: var(--mico-size-204) !important; }
.mb-208 { margin-bottom: var(--mico-size-208) !important; }
.mb-212 { margin-bottom: var(--mico-size-212) !important; }
.mb-216 { margin-bottom: var(--mico-size-216) !important; }
.mb-220 { margin-bottom: var(--mico-size-220) !important; }
.mb-224 { margin-bottom: var(--mico-size-224) !important; }
.mb-228 { margin-bottom: var(--mico-size-228) !important; }
.mb-232 { margin-bottom: var(--mico-size-232) !important; }
.mb-236 { margin-bottom: var(--mico-size-236) !important; }
.mb-240 { margin-bottom: var(--mico-size-240) !important; }
.mb-244 { margin-bottom: var(--mico-size-244) !important; }
.mb-248 { margin-bottom: var(--mico-size-248) !important; }
.mb-252 { margin-bottom: var(--mico-size-252) !important; }
.mb-256 { margin-bottom: var(--mico-size-256) !important; }
.mb-260 { margin-bottom: var(--mico-size-260) !important; }
.mb-264 { margin-bottom: var(--mico-size-264) !important; }
.mb-268 { margin-bottom: var(--mico-size-268) !important; }
.mb-272 { margin-bottom: var(--mico-size-272) !important; }
.mb-276 { margin-bottom: var(--mico-size-276) !important; }
.mb-280 { margin-bottom: var(--mico-size-280) !important; }
.mb-284 { margin-bottom: var(--mico-size-284) !important; }
.mb-288 { margin-bottom: var(--mico-size-288) !important; }
.mb-292 { margin-bottom: var(--mico-size-292) !important; }
.mb-296 { margin-bottom: var(--mico-size-296) !important; }
.mb-300 { margin-bottom: var(--mico-size-300) !important; }
.mb-304 { margin-bottom: var(--mico-size-304) !important; }
.mb-308 { margin-bottom: var(--mico-size-308) !important; }
.mb-312 { margin-bottom: var(--mico-size-312) !important; }
.mb-316 { margin-bottom: var(--mico-size-316) !important; }
.mb-320 { margin-bottom: var(--mico-size-320) !important; }
.mb-324 { margin-bottom: var(--mico-size-324) !important; }
.mb-328 { margin-bottom: var(--mico-size-328) !important; }
.mb-332 { margin-bottom: var(--mico-size-332) !important; }
.mb-336 { margin-bottom: var(--mico-size-336) !important; }
.mb-340 { margin-bottom: var(--mico-size-340) !important; }
.mb-344 { margin-bottom: var(--mico-size-344) !important; }
.mb-348 { margin-bottom: var(--mico-size-348) !important; }
.mb-352 { margin-bottom: var(--mico-size-352) !important; }
.mb-356 { margin-bottom: var(--mico-size-356) !important; }
.mb-360 { margin-bottom: var(--mico-size-360) !important; }
.mb-364 { margin-bottom: var(--mico-size-364) !important; }
.mb-368 { margin-bottom: var(--mico-size-368) !important; }
.mb-372 { margin-bottom: var(--mico-size-372) !important; }
.mb-376 { margin-bottom: var(--mico-size-376) !important; }
.mb-380 { margin-bottom: var(--mico-size-380) !important; }
.mb-384 { margin-bottom: var(--mico-size-384) !important; }
.mb-388 { margin-bottom: var(--mico-size-388) !important; }
.mb-392 { margin-bottom: var(--mico-size-392) !important; }
.mb-396 { margin-bottom: var(--mico-size-396) !important; }
.mb-400 { margin-bottom: var(--mico-size-400) !important; }


/* Margin-left classes from 4 to 100 */
.ml-4 { margin-left: var(--mico-size-4) !important; }
.ml-8 { margin-left: var(--mico-size-8) !important; }
.ml-12 { margin-left: var(--mico-size-12) !important; }
.ml-16 { margin-left: var(--mico-size-16) !important; }
.ml-20 { margin-left: var(--mico-size-20) !important; }
.ml-24 { margin-left: var(--mico-size-24) !important; }
.ml-28 { margin-left: var(--mico-size-28) !important; }
.ml-32 { margin-left: var(--mico-size-32) !important; }
.ml-36 { margin-left: var(--mico-size-36) !important; }
.ml-40 { margin-left: var(--mico-size-40) !important; }
.ml-44 { margin-left: var(--mico-size-44) !important; }
.ml-48 { margin-left: var(--mico-size-48) !important; }
.ml-52 { margin-left: var(--mico-size-52) !important; }
.ml-56 { margin-left: var(--mico-size-56) !important; }
.ml-60 { margin-left: var(--mico-size-60) !important; }
.ml-64 { margin-left: var(--mico-size-64) !important; }
.ml-68 { margin-left: var(--mico-size-68) !important; }
.ml-72 { margin-left: var(--mico-size-72) !important; }
.ml-76 { margin-left: var(--mico-size-76) !important; }
.ml-80 { margin-left: var(--mico-size-80) !important; }
.ml-84 { margin-left: var(--mico-size-84) !important; }
.ml-88 { margin-left: var(--mico-size-88) !important; }
.ml-92 { margin-left: var(--mico-size-92) !important; }
.ml-96 { margin-left: var(--mico-size-96) !important; }
.ml-100 { margin-left: var(--mico-size-100) !important; }
.ml-104 { margin-left: var(--mico-size-104) !important; }
.ml-108 { margin-left: var(--mico-size-108) !important; }
.ml-112 { margin-left: var(--mico-size-112) !important; }
.ml-116 { margin-left: var(--mico-size-116) !important; }
.ml-120 { margin-left: var(--mico-size-120) !important; }
.ml-124 { margin-left: var(--mico-size-124) !important; }
.ml-128 { margin-left: var(--mico-size-128) !important; }
.ml-132 { margin-left: var(--mico-size-132) !important; }
.ml-136 { margin-left: var(--mico-size-136) !important; }
.ml-140 { margin-left: var(--mico-size-140) !important; }
.ml-144 { margin-left: var(--mico-size-144) !important; }
.ml-148 { margin-left: var(--mico-size-148) !important; }
.ml-152 { margin-left: var(--mico-size-152) !important; }
.ml-156 { margin-left: var(--mico-size-156) !important; }
.ml-160 { margin-left: var(--mico-size-160) !important; }
.ml-164 { margin-left: var(--mico-size-164) !important; }
.ml-168 { margin-left: var(--mico-size-168) !important; }
.ml-172 { margin-left: var(--mico-size-172) !important; }
.ml-176 { margin-left: var(--mico-size-176) !important; }
.ml-180 { margin-left: var(--mico-size-180) !important; }
.ml-184 { margin-left: var(--mico-size-184) !important; }
.ml-188 { margin-left: var(--mico-size-188) !important; }
.ml-192 { margin-left: var(--mico-size-192) !important; }
.ml-196 { margin-left: var(--mico-size-196) !important; }
.ml-200 { margin-left: var(--mico-size-200) !important; }
.ml-204 { margin-left: var(--mico-size-204) !important; }
.ml-208 { margin-left: var(--mico-size-208) !important; }
.ml-212 { margin-left: var(--mico-size-212) !important; }
.ml-216 { margin-left: var(--mico-size-216) !important; }
.ml-220 { margin-left: var(--mico-size-220) !important; }
.ml-224 { margin-left: var(--mico-size-224) !important; }
.ml-228 { margin-left: var(--mico-size-228) !important; }
.ml-232 { margin-left: var(--mico-size-232) !important; }
.ml-236 { margin-left: var(--mico-size-236) !important; }
.ml-240 { margin-left: var(--mico-size-240) !important; }
.ml-244 { margin-left: var(--mico-size-244) !important; }
.ml-248 { margin-left: var(--mico-size-248) !important; }
.ml-252 { margin-left: var(--mico-size-252) !important; }
.ml-256 { margin-left: var(--mico-size-256) !important; }
.ml-260 { margin-left: var(--mico-size-260) !important; }
.ml-264 { margin-left: var(--mico-size-264) !important; }
.ml-268 { margin-left: var(--mico-size-268) !important; }
.ml-272 { margin-left: var(--mico-size-272) !important; }
.ml-276 { margin-left: var(--mico-size-276) !important; }
.ml-280 { margin-left: var(--mico-size-280) !important; }
.ml-284 { margin-left: var(--mico-size-284) !important; }
.ml-288 { margin-left: var(--mico-size-288) !important; }
.ml-292 { margin-left: var(--mico-size-292) !important; }
.ml-296 { margin-left: var(--mico-size-296) !important; }
.ml-300 { margin-left: var(--mico-size-300) !important; }
.ml-304 { margin-left: var(--mico-size-304) !important; }
.ml-308 { margin-left: var(--mico-size-308) !important; }
.ml-312 { margin-left: var(--mico-size-312) !important; }
.ml-316 { margin-left: var(--mico-size-316) !important; }
.ml-320 { margin-left: var(--mico-size-320) !important; }
.ml-324 { margin-left: var(--mico-size-324) !important; }
.ml-328 { margin-left: var(--mico-size-328) !important; }
.ml-332 { margin-left: var(--mico-size-332) !important; }
.ml-336 { margin-left: var(--mico-size-336) !important; }
.ml-340 { margin-left: var(--mico-size-340) !important; }
.ml-344 { margin-left: var(--mico-size-344) !important; }
.ml-348 { margin-left: var(--mico-size-348) !important; }
.ml-352 { margin-left: var(--mico-size-352) !important; }
.ml-356 { margin-left: var(--mico-size-356) !important; }
.ml-360 { margin-left: var(--mico-size-360) !important; }
.ml-364 { margin-left: var(--mico-size-364) !important; }
.ml-368 { margin-left: var(--mico-size-368) !important; }
.ml-372 { margin-left: var(--mico-size-372) !important; }
.ml-376 { margin-left: var(--mico-size-376) !important; }
.ml-380 { margin-left: var(--mico-size-380) !important; }
.ml-384 { margin-left: var(--mico-size-384) !important; }
.ml-388 { margin-left: var(--mico-size-388) !important; }
.ml-392 { margin-left: var(--mico-size-392) !important; }
.ml-396 { margin-left: var(--mico-size-396) !important; }
.ml-400 { margin-left: var(--mico-size-400) !important; }


/* Margin-right classes from 4 to 100 */
.mr-4 { margin-right: var(--mico-size-4) !important; }
.mr-8 { margin-right: var(--mico-size-8) !important; }
.mr-12 { margin-right: var(--mico-size-12) !important; }
.mr-16 { margin-right: var(--mico-size-16) !important; }
.mr-20 { margin-right: var(--mico-size-20) !important; }
.mr-24 { margin-right: var(--mico-size-24) !important; }
.mr-28 { margin-right: var(--mico-size-28) !important; }
.mr-32 { margin-right: var(--mico-size-32) !important; }
.mr-36 { margin-right: var(--mico-size-36) !important; }
.mr-40 { margin-right: var(--mico-size-40) !important; }
.mr-44 { margin-right: var(--mico-size-44) !important; }
.mr-48 { margin-right: var(--mico-size-48) !important; }
.mr-52 { margin-right: var(--mico-size-52) !important; }
.mr-56 { margin-right: var(--mico-size-56) !important; }
.mr-60 { margin-right: var(--mico-size-60) !important; }
.mr-64 { margin-right: var(--mico-size-64) !important; }
.mr-68 { margin-right: var(--mico-size-68) !important; }
.mr-72 { margin-right: var(--mico-size-72) !important; }
.mr-76 { margin-right: var(--mico-size-76) !important; }
.mr-80 { margin-right: var(--mico-size-80) !important; }
.mr-84 { margin-right: var(--mico-size-84) !important; }
.mr-88 { margin-right: var(--mico-size-88) !important; }
.mr-92 { margin-right: var(--mico-size-92) !important; }
.mr-96 { margin-right: var(--mico-size-96) !important; }
.mr-100 { margin-right: var(--mico-size-100) !important; }
.mr-104 { margin-right: var(--mico-size-104) !important; }
.mr-108 { margin-right: var(--mico-size-108) !important; }
.mr-112 { margin-right: var(--mico-size-112) !important; }
.mr-116 { margin-right: var(--mico-size-116) !important; }
.mr-120 { margin-right: var(--mico-size-120) !important; }
.mr-124 { margin-right: var(--mico-size-124) !important; }
.mr-128 { margin-right: var(--mico-size-128) !important; }
.mr-132 { margin-right: var(--mico-size-132) !important; }
.mr-136 { margin-right: var(--mico-size-136) !important; }
.mr-140 { margin-right: var(--mico-size-140) !important; }
.mr-144 { margin-right: var(--mico-size-144) !important; }
.mr-148 { margin-right: var(--mico-size-148) !important; }
.mr-152 { margin-right: var(--mico-size-152) !important; }
.mr-156 { margin-right: var(--mico-size-156) !important; }
.mr-160 { margin-right: var(--mico-size-160) !important; }
.mr-164 { margin-right: var(--mico-size-164) !important; }
.mr-168 { margin-right: var(--mico-size-168) !important; }
.mr-172 { margin-right: var(--mico-size-172) !important; }
.mr-176 { margin-right: var(--mico-size-176) !important; }
.mr-180 { margin-right: var(--mico-size-180) !important; }
.mr-184 { margin-right: var(--mico-size-184) !important; }
.mr-188 { margin-right: var(--mico-size-188) !important; }
.mr-192 { margin-right: var(--mico-size-192) !important; }
.mr-196 { margin-right: var(--mico-size-196) !important; }
.mr-200 { margin-right: var(--mico-size-200) !important; }
.mr-204 { margin-right: var(--mico-size-204) !important; }
.mr-208 { margin-right: var(--mico-size-208) !important; }
.mr-212 { margin-right: var(--mico-size-212) !important; }
.mr-216 { margin-right: var(--mico-size-216) !important; }
.mr-220 { margin-right: var(--mico-size-220) !important; }
.mr-224 { margin-right: var(--mico-size-224) !important; }
.mr-228 { margin-right: var(--mico-size-228) !important; }
.mr-232 { margin-right: var(--mico-size-232) !important; }
.mr-236 { margin-right: var(--mico-size-236) !important; }
.mr-240 { margin-right: var(--mico-size-240) !important; }
.mr-244 { margin-right: var(--mico-size-244) !important; }
.mr-248 { margin-right: var(--mico-size-248) !important; }
.mr-252 { margin-right: var(--mico-size-252) !important; }
.mr-256 { margin-right: var(--mico-size-256) !important; }
.mr-260 { margin-right: var(--mico-size-260) !important; }
.mr-264 { margin-right: var(--mico-size-264) !important; }
.mr-268 { margin-right: var(--mico-size-268) !important; }
.mr-272 { margin-right: var(--mico-size-272) !important; }
.mr-276 { margin-right: var(--mico-size-276) !important; }
.mr-280 { margin-right: var(--mico-size-280) !important; }
.mr-284 { margin-right: var(--mico-size-284) !important; }
.mr-288 { margin-right: var(--mico-size-288) !important; }
.mr-292 { margin-right: var(--mico-size-292) !important; }
.mr-296 { margin-right: var(--mico-size-296) !important; }
.mr-300 { margin-right: var(--mico-size-300) !important; }
.mr-304 { margin-right: var(--mico-size-304) !important; }
.mr-308 { margin-right: var(--mico-size-308) !important; }
.mr-312 { margin-right: var(--mico-size-312) !important; }
.mr-316 { margin-right: var(--mico-size-316) !important; }
.mr-320 { margin-right: var(--mico-size-320) !important; }
.mr-324 { margin-right: var(--mico-size-324) !important; }
.mr-328 { margin-right: var(--mico-size-328) !important; }
.mr-332 { margin-right: var(--mico-size-332) !important; }
.mr-336 { margin-right: var(--mico-size-336) !important; }
.mr-340 { margin-right: var(--mico-size-340) !important; }
.mr-344 { margin-right: var(--mico-size-344) !important; }
.mr-348 { margin-right: var(--mico-size-348) !important; }
.mr-352 { margin-right: var(--mico-size-352) !important; }
.mr-356 { margin-right: var(--mico-size-356) !important; }
.mr-360 { margin-right: var(--mico-size-360) !important; }
.mr-364 { margin-right: var(--mico-size-364) !important; }
.mr-368 { margin-right: var(--mico-size-368) !important; }
.mr-372 { margin-right: var(--mico-size-372) !important; }
.mr-376 { margin-right: var(--mico-size-376) !important; }
.mr-380 { margin-right: var(--mico-size-380) !important; }
.mr-384 { margin-right: var(--mico-size-384) !important; }
.mr-388 { margin-right: var(--mico-size-388) !important; }
.mr-392 { margin-right: var(--mico-size-392) !important; }
.mr-396 { margin-right: var(--mico-size-396) !important; }
.mr-400 { margin-right: var(--mico-size-400) !important; }


/* Margin-inline classes from 4 to 100 */
.mx-4 { margin-inline: var(--mico-size-4) !important; }
.mx-8 { margin-inline: var(--mico-size-8) !important; }
.mx-12 { margin-inline: var(--mico-size-12) !important; }
.mx-16 { margin-inline: var(--mico-size-16) !important; }
.mx-20 { margin-inline: var(--mico-size-20) !important; }
.mx-24 { margin-inline: var(--mico-size-24) !important; }
.mx-28 { margin-inline: var(--mico-size-28) !important; }
.mx-32 { margin-inline: var(--mico-size-32) !important; }
.mx-36 { margin-inline: var(--mico-size-36) !important; }
.mx-40 { margin-inline: var(--mico-size-40) !important; }
.mx-44 { margin-inline: var(--mico-size-44) !important; }
.mx-48 { margin-inline: var(--mico-size-48) !important; }
.mx-52 { margin-inline: var(--mico-size-52) !important; }
.mx-56 { margin-inline: var(--mico-size-56) !important; }
.mx-60 { margin-inline: var(--mico-size-60) !important; }
.mx-64 { margin-inline: var(--mico-size-64) !important; }
.mx-68 { margin-inline: var(--mico-size-68) !important; }
.mx-72 { margin-inline: var(--mico-size-72) !important; }
.mx-76 { margin-inline: var(--mico-size-76) !important; }
.mx-80 { margin-inline: var(--mico-size-80) !important; }
.mx-84 { margin-inline: var(--mico-size-84) !important; }
.mx-88 { margin-inline: var(--mico-size-88) !important; }
.mx-92 { margin-inline: var(--mico-size-92) !important; }
.mx-96 { margin-inline: var(--mico-size-96) !important; }
.mx-100 { margin-inline: var(--mico-size-100) !important; }
.mx-104 { margin-inline: var(--mico-size-104) !important; }
.mx-108 { margin-inline: var(--mico-size-108) !important; }
.mx-112 { margin-inline: var(--mico-size-112) !important; }
.mx-116 { margin-inline: var(--mico-size-116) !important; }
.mx-120 { margin-inline: var(--mico-size-120) !important; }
.mx-124 { margin-inline: var(--mico-size-124) !important; }
.mx-128 { margin-inline: var(--mico-size-128) !important; }
.mx-132 { margin-inline: var(--mico-size-132) !important; }
.mx-136 { margin-inline: var(--mico-size-136) !important; }
.mx-140 { margin-inline: var(--mico-size-140) !important; }
.mx-144 { margin-inline: var(--mico-size-144) !important; }
.mx-148 { margin-inline: var(--mico-size-148) !important; }
.mx-152 { margin-inline: var(--mico-size-152) !important; }
.mx-156 { margin-inline: var(--mico-size-156) !important; }
.mx-160 { margin-inline: var(--mico-size-160) !important; }
.mx-164 { margin-inline: var(--mico-size-164) !important; }
.mx-168 { margin-inline: var(--mico-size-168) !important; }
.mx-172 { margin-inline: var(--mico-size-172) !important; }
.mx-176 { margin-inline: var(--mico-size-176) !important; }
.mx-180 { margin-inline: var(--mico-size-180) !important; }
.mx-184 { margin-inline: var(--mico-size-184) !important; }
.mx-188 { margin-inline: var(--mico-size-188) !important; }
.mx-192 { margin-inline: var(--mico-size-192) !important; }
.mx-196 { margin-inline: var(--mico-size-196) !important; }
.mx-200 { margin-inline: var(--mico-size-200) !important; }
.mx-204 { margin-inline: var(--mico-size-204) !important; }
.mx-208 { margin-inline: var(--mico-size-208) !important; }
.mx-212 { margin-inline: var(--mico-size-212) !important; }
.mx-216 { margin-inline: var(--mico-size-216) !important; }
.mx-220 { margin-inline: var(--mico-size-220) !important; }
.mx-224 { margin-inline: var(--mico-size-224) !important; }
.mx-228 { margin-inline: var(--mico-size-228) !important; }
.mx-232 { margin-inline: var(--mico-size-232) !important; }
.mx-236 { margin-inline: var(--mico-size-236) !important; }
.mx-240 { margin-inline: var(--mico-size-240) !important; }
.mx-244 { margin-inline: var(--mico-size-244) !important; }
.mx-248 { margin-inline: var(--mico-size-248) !important; }
.mx-252 { margin-inline: var(--mico-size-252) !important; }
.mx-256 { margin-inline: var(--mico-size-256) !important; }
.mx-260 { margin-inline: var(--mico-size-260) !important; }
.mx-264 { margin-inline: var(--mico-size-264) !important; }
.mx-268 { margin-inline: var(--mico-size-268) !important; }
.mx-272 { margin-inline: var(--mico-size-272) !important; }
.mx-276 { margin-inline: var(--mico-size-276) !important; }
.mx-280 { margin-inline: var(--mico-size-280) !important; }
.mx-284 { margin-inline: var(--mico-size-284) !important; }
.mx-288 { margin-inline: var(--mico-size-288) !important; }
.mx-292 { margin-inline: var(--mico-size-292) !important; }
.mx-296 { margin-inline: var(--mico-size-296) !important; }
.mx-300 { margin-inline: var(--mico-size-300) !important; }
.mx-304 { margin-inline: var(--mico-size-304) !important; }
.mx-308 { margin-inline: var(--mico-size-308) !important; }
.mx-312 { margin-inline: var(--mico-size-312) !important; }
.mx-316 { margin-inline: var(--mico-size-316) !important; }
.mx-320 { margin-inline: var(--mico-size-320) !important; }
.mx-324 { margin-inline: var(--mico-size-324) !important; }
.mx-328 { margin-inline: var(--mico-size-328) !important; }
.mx-332 { margin-inline: var(--mico-size-332) !important; }
.mx-336 { margin-inline: var(--mico-size-336) !important; }
.mx-340 { margin-inline: var(--mico-size-340) !important; }
.mx-344 { margin-inline: var(--mico-size-344) !important; }
.mx-348 { margin-inline: var(--mico-size-348) !important; }
.mx-352 { margin-inline: var(--mico-size-352) !important; }
.mx-356 { margin-inline: var(--mico-size-356) !important; }
.mx-360 { margin-inline: var(--mico-size-360) !important; }
.mx-364 { margin-inline: var(--mico-size-364) !important; }
.mx-368 { margin-inline: var(--mico-size-368) !important; }
.mx-372 { margin-inline: var(--mico-size-372) !important; }
.mx-376 { margin-inline: var(--mico-size-376) !important; }
.mx-380 { margin-inline: var(--mico-size-380) !important; }
.mx-384 { margin-inline: var(--mico-size-384) !important; }
.mx-388 { margin-inline: var(--mico-size-388) !important; }
.mx-392 { margin-inline: var(--mico-size-392) !important; }
.mx-396 { margin-inline: var(--mico-size-396) !important; }
.mx-400 { margin-inline: var(--mico-size-400) !important; }


/* Margin-block classes from 4 to 100 */
.my-4 { margin-block: var(--mico-size-4) !important; }
.my-8 { margin-block: var(--mico-size-8) !important; }
.my-12 { margin-block: var(--mico-size-12) !important; }
.my-16 { margin-block: var(--mico-size-16) !important; }
.my-20 { margin-block: var(--mico-size-20) !important; }
.my-24 { margin-block: var(--mico-size-24) !important; }
.my-28 { margin-block: var(--mico-size-28) !important; }
.my-32 { margin-block: var(--mico-size-32) !important; }
.my-36 { margin-block: var(--mico-size-36) !important; }
.my-40 { margin-block: var(--mico-size-40) !important; }
.my-44 { margin-block: var(--mico-size-44) !important; }
.my-48 { margin-block: var(--mico-size-48) !important; }
.my-52 { margin-block: var(--mico-size-52) !important; }
.my-56 { margin-block: var(--mico-size-56) !important; }
.my-60 { margin-block: var(--mico-size-60) !important; }
.my-64 { margin-block: var(--mico-size-64) !important; }
.my-68 { margin-block: var(--mico-size-68) !important; }
.my-72 { margin-block: var(--mico-size-72) !important; }
.my-76 { margin-block: var(--mico-size-76) !important; }
.my-80 { margin-block: var(--mico-size-80) !important; }
.my-84 { margin-block: var(--mico-size-84) !important; }
.my-88 { margin-block: var(--mico-size-88) !important; }
.my-92 { margin-block: var(--mico-size-92) !important; }
.my-96 { margin-block: var(--mico-size-96) !important; }
.my-100 { margin-block: var(--mico-size-100) !important; }
.my-104 { margin-block: var(--mico-size-104) !important; }
.my-108 { margin-block: var(--mico-size-108) !important; }
.my-112 { margin-block: var(--mico-size-112) !important; }
.my-116 { margin-block: var(--mico-size-116) !important; }
.my-120 { margin-block: var(--mico-size-120) !important; }
.my-124 { margin-block: var(--mico-size-124) !important; }
.my-128 { margin-block: var(--mico-size-128) !important; }
.my-132 { margin-block: var(--mico-size-132) !important; }
.my-136 { margin-block: var(--mico-size-136) !important; }
.my-140 { margin-block: var(--mico-size-140) !important; }
.my-144 { margin-block: var(--mico-size-144) !important; }
.my-148 { margin-block: var(--mico-size-148) !important; }
.my-152 { margin-block: var(--mico-size-152) !important; }
.my-156 { margin-block: var(--mico-size-156) !important; }
.my-160 { margin-block: var(--mico-size-160) !important; }
.my-164 { margin-block: var(--mico-size-164) !important; }
.my-168 { margin-block: var(--mico-size-168) !important; }
.my-172 { margin-block: var(--mico-size-172) !important; }
.my-176 { margin-block: var(--mico-size-176) !important; }
.my-180 { margin-block: var(--mico-size-180) !important; }
.my-184 { margin-block: var(--mico-size-184) !important; }
.my-188 { margin-block: var(--mico-size-188) !important; }
.my-192 { margin-block: var(--mico-size-192) !important; }
.my-196 { margin-block: var(--mico-size-196) !important; }
.my-200 { margin-block: var(--mico-size-200) !important; }
.my-204 { margin-block: var(--mico-size-204) !important; }
.my-208 { margin-block: var(--mico-size-208) !important; }
.my-212 { margin-block: var(--mico-size-212) !important; }
.my-216 { margin-block: var(--mico-size-216) !important; }
.my-220 { margin-block: var(--mico-size-220) !important; }
.my-224 { margin-block: var(--mico-size-224) !important; }
.my-228 { margin-block: var(--mico-size-228) !important; }
.my-232 { margin-block: var(--mico-size-232) !important; }
.my-236 { margin-block: var(--mico-size-236) !important; }
.my-240 { margin-block: var(--mico-size-240) !important; }
.my-244 { margin-block: var(--mico-size-244) !important; }
.my-248 { margin-block: var(--mico-size-248) !important; }
.my-252 { margin-block: var(--mico-size-252) !important; }
.my-256 { margin-block: var(--mico-size-256) !important; }
.my-260 { margin-block: var(--mico-size-260) !important; }
.my-264 { margin-block: var(--mico-size-264) !important; }
.my-268 { margin-block: var(--mico-size-268) !important; }
.my-272 { margin-block: var(--mico-size-272) !important; }
.my-276 { margin-block: var(--mico-size-276) !important; }
.my-280 { margin-block: var(--mico-size-280) !important; }
.my-284 { margin-block: var(--mico-size-284) !important; }
.my-288 { margin-block: var(--mico-size-288) !important; }
.my-292 { margin-block: var(--mico-size-292) !important; }
.my-296 { margin-block: var(--mico-size-296) !important; }
.my-300 { margin-block: var(--mico-size-300) !important; }
.my-304 { margin-block: var(--mico-size-304) !important; }
.my-308 { margin-block: var(--mico-size-308) !important; }
.my-312 { margin-block: var(--mico-size-312) !important; }
.my-316 { margin-block: var(--mico-size-316) !important; }
.my-320 { margin-block: var(--mico-size-320) !important; }
.my-324 { margin-block: var(--mico-size-324) !important; }
.my-328 { margin-block: var(--mico-size-328) !important; }
.my-332 { margin-block: var(--mico-size-332) !important; }
.my-336 { margin-block: var(--mico-size-336) !important; }
.my-340 { margin-block: var(--mico-size-340) !important; }
.my-344 { margin-block: var(--mico-size-344) !important; }
.my-348 { margin-block: var(--mico-size-348) !important; }
.my-352 { margin-block: var(--mico-size-352) !important; }
.my-356 { margin-block: var(--mico-size-356) !important; }
.my-360 { margin-block: var(--mico-size-360) !important; }
.my-364 { margin-block: var(--mico-size-364) !important; }
.my-368 { margin-block: var(--mico-size-368) !important; }
.my-372 { margin-block: var(--mico-size-372) !important; }
.my-376 { margin-block: var(--mico-size-376) !important; }
.my-380 { margin-block: var(--mico-size-380) !important; }
.my-384 { margin-block: var(--mico-size-384) !important; }
.my-388 { margin-block: var(--mico-size-388) !important; }
.my-392 { margin-block: var(--mico-size-392) !important; }
.my-396 { margin-block: var(--mico-size-396) !important; }
.my-400 { margin-block: var(--mico-size-400) !important; }


/* Padding classes from 4 to 100 */
.p-4 { padding: var(--mico-size-4) !important; }
.p-8 { padding: var(--mico-size-8) !important; }
.p-12 { padding: var(--mico-size-12) !important; }
.p-16 { padding: var(--mico-size-16) !important; }
.p-20 { padding: var(--mico-size-20) !important; }
.p-24 { padding: var(--mico-size-24) !important; }
.p-28 { padding: var(--mico-size-28) !important; }
.p-32 { padding: var(--mico-size-32) !important; }
.p-36 { padding: var(--mico-size-36) !important; }
.p-40 { padding: var(--mico-size-40) !important; }
.p-44 { padding: var(--mico-size-44) !important; }
.p-48 { padding: var(--mico-size-48) !important; }
.p-52 { padding: var(--mico-size-52) !important; }
.p-56 { padding: var(--mico-size-56) !important; }
.p-60 { padding: var(--mico-size-60) !important; }
.p-64 { padding: var(--mico-size-64) !important; }
.p-68 { padding: var(--mico-size-68) !important; }
.p-72 { padding: var(--mico-size-72) !important; }
.p-76 { padding: var(--mico-size-76) !important; }
.p-80 { padding: var(--mico-size-80) !important; }
.p-84 { padding: var(--mico-size-84) !important; }
.p-88 { padding: var(--mico-size-88) !important; }
.p-92 { padding: var(--mico-size-92) !important; }
.p-96 { padding: var(--mico-size-96) !important; }
.p-100 { padding: var(--mico-size-100) !important; }
.p-104 { padding: var(--mico-size-104) !important; }
.p-108 { padding: var(--mico-size-108) !important; }
.p-112 { padding: var(--mico-size-112) !important; }
.p-116 { padding: var(--mico-size-116) !important; }
.p-120 { padding: var(--mico-size-120) !important; }
.p-124 { padding: var(--mico-size-124) !important; }
.p-128 { padding: var(--mico-size-128) !important; }
.p-132 { padding: var(--mico-size-132) !important; }
.p-136 { padding: var(--mico-size-136) !important; }
.p-140 { padding: var(--mico-size-140) !important; }
.p-144 { padding: var(--mico-size-144) !important; }
.p-148 { padding: var(--mico-size-148) !important; }
.p-152 { padding: var(--mico-size-152) !important; }
.p-156 { padding: var(--mico-size-156) !important; }
.p-160 { padding: var(--mico-size-160) !important; }
.p-164 { padding: var(--mico-size-164) !important; }
.p-168 { padding: var(--mico-size-168) !important; }
.p-172 { padding: var(--mico-size-172) !important; }
.p-176 { padding: var(--mico-size-176) !important; }
.p-180 { padding: var(--mico-size-180) !important; }
.p-184 { padding: var(--mico-size-184) !important; }
.p-188 { padding: var(--mico-size-188) !important; }
.p-192 { padding: var(--mico-size-192) !important; }
.p-196 { padding: var(--mico-size-196) !important; }
.p-200 { padding: var(--mico-size-200) !important; }
.p-204 { padding: var(--mico-size-204) !important; }
.p-208 { padding: var(--mico-size-208) !important; }
.p-212 { padding: var(--mico-size-212) !important; }
.p-216 { padding: var(--mico-size-216) !important; }
.p-220 { padding: var(--mico-size-220) !important; }
.p-224 { padding: var(--mico-size-224) !important; }
.p-228 { padding: var(--mico-size-228) !important; }
.p-232 { padding: var(--mico-size-232) !important; }
.p-236 { padding: var(--mico-size-236) !important; }
.p-240 { padding: var(--mico-size-240) !important; }
.p-244 { padding: var(--mico-size-244) !important; }
.p-248 { padding: var(--mico-size-248) !important; }
.p-252 { padding: var(--mico-size-252) !important; }
.p-256 { padding: var(--mico-size-256) !important; }
.p-260 { padding: var(--mico-size-260) !important; }
.p-264 { padding: var(--mico-size-264) !important; }
.p-268 { padding: var(--mico-size-268) !important; }
.p-272 { padding: var(--mico-size-272) !important; }
.p-276 { padding: var(--mico-size-276) !important; }
.p-280 { padding: var(--mico-size-280) !important; }
.p-284 { padding: var(--mico-size-284) !important; }
.p-288 { padding: var(--mico-size-288) !important; }
.p-292 { padding: var(--mico-size-292) !important; }
.p-296 { padding: var(--mico-size-296) !important; }
.p-300 { padding: var(--mico-size-300) !important; }
.p-304 { padding: var(--mico-size-304) !important; }
.p-308 { padding: var(--mico-size-308) !important; }
.p-312 { padding: var(--mico-size-312) !important; }
.p-316 { padding: var(--mico-size-316) !important; }
.p-320 { padding: var(--mico-size-320) !important; }
.p-324 { padding: var(--mico-size-324) !important; }
.p-328 { padding: var(--mico-size-328) !important; }
.p-332 { padding: var(--mico-size-332) !important; }
.p-336 { padding: var(--mico-size-336) !important; }
.p-340 { padding: var(--mico-size-340) !important; }
.p-344 { padding: var(--mico-size-344) !important; }
.p-348 { padding: var(--mico-size-348) !important; }
.p-352 { padding: var(--mico-size-352) !important; }
.p-356 { padding: var(--mico-size-356) !important; }
.p-360 { padding: var(--mico-size-360) !important; }
.p-364 { padding: var(--mico-size-364) !important; }
.p-368 { padding: var(--mico-size-368) !important; }
.p-372 { padding: var(--mico-size-372) !important; }
.p-376 { padding: var(--mico-size-376) !important; }
.p-380 { padding: var(--mico-size-380) !important; }
.p-384 { padding: var(--mico-size-384) !important; }
.p-388 { padding: var(--mico-size-388) !important; }
.p-392 { padding: var(--mico-size-392) !important; }
.p-396 { padding: var(--mico-size-396) !important; }
.p-400 { padding: var(--mico-size-400) !important; }


/* Padding-top classes from 4 to 100 */
.pt-4 { padding-top: var(--mico-size-4) !important; }
.pt-8 { padding-top: var(--mico-size-8) !important; }
.pt-12 { padding-top: var(--mico-size-12) !important; }
.pt-16 { padding-top: var(--mico-size-16) !important; }
.pt-20 { padding-top: var(--mico-size-20) !important; }
.pt-24 { padding-top: var(--mico-size-24) !important; }
.pt-28 { padding-top: var(--mico-size-28) !important; }
.pt-32 { padding-top: var(--mico-size-32) !important; }
.pt-36 { padding-top: var(--mico-size-36) !important; }
.pt-40 { padding-top: var(--mico-size-40) !important; }
.pt-44 { padding-top: var(--mico-size-44) !important; }
.pt-48 { padding-top: var(--mico-size-48) !important; }
.pt-52 { padding-top: var(--mico-size-52) !important; }
.pt-56 { padding-top: var(--mico-size-56) !important; }
.pt-60 { padding-top: var(--mico-size-60) !important; }
.pt-64 { padding-top: var(--mico-size-64) !important; }
.pt-68 { padding-top: var(--mico-size-68) !important; }
.pt-72 { padding-top: var(--mico-size-72) !important; }
.pt-76 { padding-top: var(--mico-size-76) !important; }
.pt-80 { padding-top: var(--mico-size-80) !important; }
.pt-84 { padding-top: var(--mico-size-84) !important; }
.pt-88 { padding-top: var(--mico-size-88) !important; }
.pt-92 { padding-top: var(--mico-size-92) !important; }
.pt-96 { padding-top: var(--mico-size-96) !important; }
.pt-100 { padding-top: var(--mico-size-100) !important; }
.pt-104 { padding-top: var(--mico-size-104) !important; }
.pt-108 { padding-top: var(--mico-size-108) !important; }
.pt-112 { padding-top: var(--mico-size-112) !important; }
.pt-116 { padding-top: var(--mico-size-116) !important; }
.pt-120 { padding-top: var(--mico-size-120) !important; }
.pt-124 { padding-top: var(--mico-size-124) !important; }
.pt-128 { padding-top: var(--mico-size-128) !important; }
.pt-132 { padding-top: var(--mico-size-132) !important; }
.pt-136 { padding-top: var(--mico-size-136) !important; }
.pt-140 { padding-top: var(--mico-size-140) !important; }
.pt-144 { padding-top: var(--mico-size-144) !important; }
.pt-148 { padding-top: var(--mico-size-148) !important; }
.pt-152 { padding-top: var(--mico-size-152) !important; }
.pt-156 { padding-top: var(--mico-size-156) !important; }
.pt-160 { padding-top: var(--mico-size-160) !important; }
.pt-164 { padding-top: var(--mico-size-164) !important; }
.pt-168 { padding-top: var(--mico-size-168) !important; }
.pt-172 { padding-top: var(--mico-size-172) !important; }
.pt-176 { padding-top: var(--mico-size-176) !important; }
.pt-180 { padding-top: var(--mico-size-180) !important; }
.pt-184 { padding-top: var(--mico-size-184) !important; }
.pt-188 { padding-top: var(--mico-size-188) !important; }
.pt-192 { padding-top: var(--mico-size-192) !important; }
.pt-196 { padding-top: var(--mico-size-196) !important; }
.pt-200 { padding-top: var(--mico-size-200) !important; }
.pt-204 { padding-top: var(--mico-size-204) !important; }
.pt-208 { padding-top: var(--mico-size-208) !important; }
.pt-212 { padding-top: var(--mico-size-212) !important; }
.pt-216 { padding-top: var(--mico-size-216) !important; }
.pt-220 { padding-top: var(--mico-size-220) !important; }
.pt-224 { padding-top: var(--mico-size-224) !important; }
.pt-228 { padding-top: var(--mico-size-228) !important; }
.pt-232 { padding-top: var(--mico-size-232) !important; }
.pt-236 { padding-top: var(--mico-size-236) !important; }
.pt-240 { padding-top: var(--mico-size-240) !important; }
.pt-244 { padding-top: var(--mico-size-244) !important; }
.pt-248 { padding-top: var(--mico-size-248) !important; }
.pt-252 { padding-top: var(--mico-size-252) !important; }
.pt-256 { padding-top: var(--mico-size-256) !important; }
.pt-260 { padding-top: var(--mico-size-260) !important; }
.pt-264 { padding-top: var(--mico-size-264) !important; }
.pt-268 { padding-top: var(--mico-size-268) !important; }
.pt-272 { padding-top: var(--mico-size-272) !important; }
.pt-276 { padding-top: var(--mico-size-276) !important; }
.pt-280 { padding-top: var(--mico-size-280) !important; }
.pt-284 { padding-top: var(--mico-size-284) !important; }
.pt-288 { padding-top: var(--mico-size-288) !important; }
.pt-292 { padding-top: var(--mico-size-292) !important; }
.pt-296 { padding-top: var(--mico-size-296) !important; }
.pt-300 { padding-top: var(--mico-size-300) !important; }
.pt-304 { padding-top: var(--mico-size-304) !important; }
.pt-308 { padding-top: var(--mico-size-308) !important; }
.pt-312 { padding-top: var(--mico-size-312) !important; }
.pt-316 { padding-top: var(--mico-size-316) !important; }
.pt-320 { padding-top: var(--mico-size-320) !important; }
.pt-324 { padding-top: var(--mico-size-324) !important; }
.pt-328 { padding-top: var(--mico-size-328) !important; }
.pt-332 { padding-top: var(--mico-size-332) !important; }
.pt-336 { padding-top: var(--mico-size-336) !important; }
.pt-340 { padding-top: var(--mico-size-340) !important; }
.pt-344 { padding-top: var(--mico-size-344) !important; }
.pt-348 { padding-top: var(--mico-size-348) !important; }
.pt-352 { padding-top: var(--mico-size-352) !important; }
.pt-356 { padding-top: var(--mico-size-356) !important; }
.pt-360 { padding-top: var(--mico-size-360) !important; }
.pt-364 { padding-top: var(--mico-size-364) !important; }
.pt-368 { padding-top: var(--mico-size-368) !important; }
.pt-372 { padding-top: var(--mico-size-372) !important; }
.pt-376 { padding-top: var(--mico-size-376) !important; }
.pt-380 { padding-top: var(--mico-size-380) !important; }
.pt-384 { padding-top: var(--mico-size-384) !important; }
.pt-388 { padding-top: var(--mico-size-388) !important; }
.pt-392 { padding-top: var(--mico-size-392) !important; }
.pt-396 { padding-top: var(--mico-size-396) !important; }
.pt-400 { padding-top: var(--mico-size-400) !important; }


/* Padding-bottom classes from 4 to 100 */
.pb-4 { padding-bottom: var(--mico-size-4) !important; }
.pb-8 { padding-bottom: var(--mico-size-8) !important; }
.pb-12 { padding-bottom: var(--mico-size-12) !important; }
.pb-16 { padding-bottom: var(--mico-size-16) !important; }
.pb-20 { padding-bottom: var(--mico-size-20) !important; }
.pb-24 { padding-bottom: var(--mico-size-24) !important; }
.pb-28 { padding-bottom: var(--mico-size-28) !important; }
.pb-32 { padding-bottom: var(--mico-size-32) !important; }
.pb-36 { padding-bottom: var(--mico-size-36) !important; }
.pb-40 { padding-bottom: var(--mico-size-40) !important; }
.pb-44 { padding-bottom: var(--mico-size-44) !important; }
.pb-48 { padding-bottom: var(--mico-size-48) !important; }
.pb-52 { padding-bottom: var(--mico-size-52) !important; }
.pb-56 { padding-bottom: var(--mico-size-56) !important; }
.pb-60 { padding-bottom: var(--mico-size-60) !important; }
.pb-64 { padding-bottom: var(--mico-size-64) !important; }
.pb-68 { padding-bottom: var(--mico-size-68) !important; }
.pb-72 { padding-bottom: var(--mico-size-72) !important; }
.pb-76 { padding-bottom: var(--mico-size-76) !important; }
.pb-80 { padding-bottom: var(--mico-size-80) !important; }
.pb-84 { padding-bottom: var(--mico-size-84) !important; }
.pb-88 { padding-bottom: var(--mico-size-88) !important; }
.pb-92 { padding-bottom: var(--mico-size-92) !important; }
.pb-96 { padding-bottom: var(--mico-size-96) !important; }
.pb-100 { padding-bottom: var(--mico-size-100) !important; }
.pb-104 { padding-bottom: var(--mico-size-104) !important; }
.pb-108 { padding-bottom: var(--mico-size-108) !important; }
.pb-112 { padding-bottom: var(--mico-size-112) !important; }
.pb-116 { padding-bottom: var(--mico-size-116) !important; }
.pb-120 { padding-bottom: var(--mico-size-120) !important; }
.pb-124 { padding-bottom: var(--mico-size-124) !important; }
.pb-128 { padding-bottom: var(--mico-size-128) !important; }
.pb-132 { padding-bottom: var(--mico-size-132) !important; }
.pb-136 { padding-bottom: var(--mico-size-136) !important; }
.pb-140 { padding-bottom: var(--mico-size-140) !important; }
.pb-144 { padding-bottom: var(--mico-size-144) !important; }
.pb-148 { padding-bottom: var(--mico-size-148) !important; }
.pb-152 { padding-bottom: var(--mico-size-152) !important; }
.pb-156 { padding-bottom: var(--mico-size-156) !important; }
.pb-160 { padding-bottom: var(--mico-size-160) !important; }
.pb-164 { padding-bottom: var(--mico-size-164) !important; }
.pb-168 { padding-bottom: var(--mico-size-168) !important; }
.pb-172 { padding-bottom: var(--mico-size-172) !important; }
.pb-176 { padding-bottom: var(--mico-size-176) !important; }
.pb-180 { padding-bottom: var(--mico-size-180) !important; }
.pb-184 { padding-bottom: var(--mico-size-184) !important; }
.pb-188 { padding-bottom: var(--mico-size-188) !important; }
.pb-192 { padding-bottom: var(--mico-size-192) !important; }
.pb-196 { padding-bottom: var(--mico-size-196) !important; }
.pb-200 { padding-bottom: var(--mico-size-200) !important; }
.pb-204 { padding-bottom: var(--mico-size-204) !important; }
.pb-208 { padding-bottom: var(--mico-size-208) !important; }
.pb-212 { padding-bottom: var(--mico-size-212) !important; }
.pb-216 { padding-bottom: var(--mico-size-216) !important; }
.pb-220 { padding-bottom: var(--mico-size-220) !important; }
.pb-224 { padding-bottom: var(--mico-size-224) !important; }
.pb-228 { padding-bottom: var(--mico-size-228) !important; }
.pb-232 { padding-bottom: var(--mico-size-232) !important; }
.pb-236 { padding-bottom: var(--mico-size-236) !important; }
.pb-240 { padding-bottom: var(--mico-size-240) !important; }
.pb-244 { padding-bottom: var(--mico-size-244) !important; }
.pb-248 { padding-bottom: var(--mico-size-248) !important; }
.pb-252 { padding-bottom: var(--mico-size-252) !important; }
.pb-256 { padding-bottom: var(--mico-size-256) !important; }
.pb-260 { padding-bottom: var(--mico-size-260) !important; }
.pb-264 { padding-bottom: var(--mico-size-264) !important; }
.pb-268 { padding-bottom: var(--mico-size-268) !important; }
.pb-272 { padding-bottom: var(--mico-size-272) !important; }
.pb-276 { padding-bottom: var(--mico-size-276) !important; }
.pb-280 { padding-bottom: var(--mico-size-280) !important; }
.pb-284 { padding-bottom: var(--mico-size-284) !important; }
.pb-288 { padding-bottom: var(--mico-size-288) !important; }
.pb-292 { padding-bottom: var(--mico-size-292) !important; }
.pb-296 { padding-bottom: var(--mico-size-296) !important; }
.pb-300 { padding-bottom: var(--mico-size-300) !important; }
.pb-304 { padding-bottom: var(--mico-size-304) !important; }
.pb-308 { padding-bottom: var(--mico-size-308) !important; }
.pb-312 { padding-bottom: var(--mico-size-312) !important; }
.pb-316 { padding-bottom: var(--mico-size-316) !important; }
.pb-320 { padding-bottom: var(--mico-size-320) !important; }
.pb-324 { padding-bottom: var(--mico-size-324) !important; }
.pb-328 { padding-bottom: var(--mico-size-328) !important; }
.pb-332 { padding-bottom: var(--mico-size-332) !important; }
.pb-336 { padding-bottom: var(--mico-size-336) !important; }
.pb-340 { padding-bottom: var(--mico-size-340) !important; }
.pb-344 { padding-bottom: var(--mico-size-344) !important; }
.pb-348 { padding-bottom: var(--mico-size-348) !important; }
.pb-352 { padding-bottom: var(--mico-size-352) !important; }
.pb-356 { padding-bottom: var(--mico-size-356) !important; }
.pb-360 { padding-bottom: var(--mico-size-360) !important; }
.pb-364 { padding-bottom: var(--mico-size-364) !important; }
.pb-368 { padding-bottom: var(--mico-size-368) !important; }
.pb-372 { padding-bottom: var(--mico-size-372) !important; }
.pb-376 { padding-bottom: var(--mico-size-376) !important; }
.pb-380 { padding-bottom: var(--mico-size-380) !important; }
.pb-384 { padding-bottom: var(--mico-size-384) !important; }
.pb-388 { padding-bottom: var(--mico-size-388) !important; }
.pb-392 { padding-bottom: var(--mico-size-392) !important; }
.pb-396 { padding-bottom: var(--mico-size-396) !important; }
.pb-400 { padding-bottom: var(--mico-size-400) !important; }


/* Padding-left classes from 4 to 100 */
.pl-4 { padding-left: var(--mico-size-4) !important; }
.pl-8 { padding-left: var(--mico-size-8) !important; }
.pl-12 { padding-left: var(--mico-size-12) !important; }
.pl-16 { padding-left: var(--mico-size-16) !important; }
.pl-20 { padding-left: var(--mico-size-20) !important; }
.pl-24 { padding-left: var(--mico-size-24) !important; }
.pl-28 { padding-left: var(--mico-size-28) !important; }
.pl-32 { padding-left: var(--mico-size-32) !important; }
.pl-36 { padding-left: var(--mico-size-36) !important; }
.pl-40 { padding-left: var(--mico-size-40) !important; }
.pl-44 { padding-left: var(--mico-size-44) !important; }
.pl-48 { padding-left: var(--mico-size-48) !important; }
.pl-52 { padding-left: var(--mico-size-52) !important; }
.pl-56 { padding-left: var(--mico-size-56) !important; }
.pl-60 { padding-left: var(--mico-size-60) !important; }
.pl-64 { padding-left: var(--mico-size-64) !important; }
.pl-68 { padding-left: var(--mico-size-68) !important; }
.pl-72 { padding-left: var(--mico-size-72) !important; }
.pl-76 { padding-left: var(--mico-size-) !important; }
.pl-72 { padding-left: var(--mico-size-72) !important; }
.pl-76 { padding-left: var(--mico-size-76) !important; }
.pl-80 { padding-left: var(--mico-size-80) !important; }
.pl-84 { padding-left: var(--mico-size-84) !important; }
.pl-88 { padding-left: var(--mico-size-88) !important; }
.pl-92 { padding-left: var(--mico-size-92) !important; }
.pl-96 { padding-left: var(--mico-size-96) !important; }
.pl-100 { padding-left: var(--mico-size-100) !important; }
.pl-104 { padding-left: var(--mico-size-104) !important; }
.pl-108 { padding-left: var(--mico-size-108) !important; }
.pl-112 { padding-left: var(--mico-size-112) !important; }
.pl-116 { padding-left: var(--mico-size-116) !important; }
.pl-120 { padding-left: var(--mico-size-120) !important; }
.pl-124 { padding-left: var(--mico-size-124) !important; }
.pl-128 { padding-left: var(--mico-size-128) !important; }
.pl-132 { padding-left: var(--mico-size-132) !important; }
.pl-136 { padding-left: var(--mico-size-136) !important; }
.pl-140 { padding-left: var(--mico-size-140) !important; }
.pl-144 { padding-left: var(--mico-size-144) !important; }
.pl-148 { padding-left: var(--mico-size-148) !important; }
.pl-152 { padding-left: var(--mico-size-152) !important; }
.pl-156 { padding-left: var(--mico-size-156) !important; }
.pl-160 { padding-left: var(--mico-size-160) !important; }
.pl-164 { padding-left: var(--mico-size-164) !important; }
.pl-168 { padding-left: var(--mico-size-168) !important; }
.pl-172 { padding-left: var(--mico-size-172) !important; }
.pl-176 { padding-left: var(--mico-size-176) !important; }
.pl-180 { padding-left: var(--mico-size-180) !important; }
.pl-184 { padding-left: var(--mico-size-184) !important; }
.pl-188 { padding-left: var(--mico-size-188) !important; }
.pl-192 { padding-left: var(--mico-size-192) !important; }
.pl-196 { padding-left: var(--mico-size-196) !important; }
.pl-200 { padding-left: var(--mico-size-200) !important; }
.pl-204 { padding-left: var(--mico-size-204) !important; }
.pl-208 { padding-left: var(--mico-size-208) !important; }
.pl-212 { padding-left: var(--mico-size-212) !important; }
.pl-216 { padding-left: var(--mico-size-216) !important; }
.pl-220 { padding-left: var(--mico-size-220) !important; }
.pl-224 { padding-left: var(--mico-size-224) !important; }
.pl-228 { padding-left: var(--mico-size-228) !important; }
.pl-232 { padding-left: var(--mico-size-232) !important; }
.pl-236 { padding-left: var(--mico-size-236) !important; }
.pl-240 { padding-left: var(--mico-size-240) !important; }
.pl-244 { padding-left: var(--mico-size-244) !important; }
.pl-248 { padding-left: var(--mico-size-248) !important; }
.pl-252 { padding-left: var(--mico-size-252) !important; }
.pl-256 { padding-left: var(--mico-size-256) !important; }
.pl-260 { padding-left: var(--mico-size-260) !important; }
.pl-264 { padding-left: var(--mico-size-264) !important; }
.pl-268 { padding-left: var(--mico-size-268) !important; }
.pl-272 { padding-left: var(--mico-size-272) !important; }
.pl-276 { padding-left: var(--mico-size-276) !important; }
.pl-280 { padding-left: var(--mico-size-280) !important; }
.pl-284 { padding-left: var(--mico-size-284) !important; }
.pl-288 { padding-left: var(--mico-size-288) !important; }
.pl-292 { padding-left: var(--mico-size-292) !important; }
.pl-296 { padding-left: var(--mico-size-296) !important; }
.pl-300 { padding-left: var(--mico-size-300) !important; }
.pl-304 { padding-left: var(--mico-size-304) !important; }
.pl-308 { padding-left: var(--mico-size-308) !important; }
.pl-312 { padding-left: var(--mico-size-312) !important; }
.pl-316 { padding-left: var(--mico-size-316) !important; }
.pl-320 { padding-left: var(--mico-size-320) !important; }
.pl-324 { padding-left: var(--mico-size-324) !important; }
.pl-328 { padding-left: var(--mico-size-328) !important; }
.pl-332 { padding-left: var(--mico-size-332) !important; }
.pl-336 { padding-left: var(--mico-size-336) !important; }
.pl-340 { padding-left: var(--mico-size-340) !important; }
.pl-344 { padding-left: var(--mico-size-344) !important; }
.pl-348 { padding-left: var(--mico-size-348) !important; }
.pl-352 { padding-left: var(--mico-size-352) !important; }
.pl-356 { padding-left: var(--mico-size-356) !important; }
.pl-360 { padding-left: var(--mico-size-360) !important; }
.pl-364 { padding-left: var(--mico-size-364) !important; }
.pl-368 { padding-left: var(--mico-size-368) !important; }
.pl-372 { padding-left: var(--mico-size-372) !important; }
.pl-376 { padding-left: var(--mico-size-376) !important; }
.pl-380 { padding-left: var(--mico-size-380) !important; }
.pl-384 { padding-left: var(--mico-size-384) !important; }
.pl-388 { padding-left: var(--mico-size-388) !important; }
.pl-392 { padding-left: var(--mico-size-392) !important; }
.pl-396 { padding-left: var(--mico-size-396) !important; }
.pl-400 { padding-left: var(--mico-size-400) !important; }


/* Padding-right classes from 4 to 100 */
.pr-4 { padding-right: var(--mico-size-4) !important; }
.pr-8 { padding-right: var(--mico-size-8) !important; }
.pr-12 { padding-right: var(--mico-size-12) !important; }
.pr-16 { padding-right: var(--mico-size-16) !important; }
.pr-20 { padding-right: var(--mico-size-20) !important; }
.pr-24 { padding-right: var(--mico-size-24) !important; }
.pr-28 { padding-right: var(--mico-size-28) !important; }
.pr-32 { padding-right: var(--mico-size-32) !important; }
.pr-36 { padding-right: var(--mico-size-36) !important; }
.pr-40 { padding-right: var(--mico-size-40) !important; }
.pr-44 { padding-right: var(--mico-size-44) !important; }
.pr-48 { padding-right: var(--mico-size-48) !important; }
.pr-52 { padding-right: var(--mico-size-52) !important; }
.pr-56 { padding-right: var(--mico-size-56) !important; }
.pr-60 { padding-right: var(--mico-size-60) !important; }
.pr-64 { padding-right: var(--mico-size-64) !important; }
.pr-68 { padding-right: var(--mico-size-68) !important; }
.pr-72 { padding-right: var(--mico-size-72) !important; }
.pr-76 { padding-right: var(--mico-size-76) !important; }
.pr-80 { padding-right: var(--mico-size-80) !important; }
.pr-84 { padding-right: var(--mico-size-84) !important; }
.pr-88 { padding-right: var(--mico-size-88) !important; }
.pr-92 { padding-right: var(--mico-size-92) !important; }
.pr-96 { padding-right: var(--mico-size-96) !important; }
.pr-100 { padding-right: var(--mico-size-100) !important; }
.pr-104 { padding-right: var(--mico-size-104) !important; }
.pr-108 { padding-right: var(--mico-size-108) !important; }
.pr-112 { padding-right: var(--mico-size-112) !important; }
.pr-116 { padding-right: var(--mico-size-116) !important; }
.pr-120 { padding-right: var(--mico-size-120) !important; }
.pr-124 { padding-right: var(--mico-size-124) !important; }
.pr-128 { padding-right: var(--mico-size-128) !important; }
.pr-132 { padding-right: var(--mico-size-132) !important; }
.pr-136 { padding-right: var(--mico-size-136) !important; }
.pr-140 { padding-right: var(--mico-size-140) !important; }
.pr-144 { padding-right: var(--mico-size-144) !important; }
.pr-148 { padding-right: var(--mico-size-148) !important; }
.pr-152 { padding-right: var(--mico-size-152) !important; }
.pr-156 { padding-right: var(--mico-size-156) !important; }
.pr-160 { padding-right: var(--mico-size-160) !important; }
.pr-164 { padding-right: var(--mico-size-164) !important; }
.pr-168 { padding-right: var(--mico-size-168) !important; }
.pr-172 { padding-right: var(--mico-size-172) !important; }
.pr-176 { padding-right: var(--mico-size-176) !important; }
.pr-180 { padding-right: var(--mico-size-180) !important; }
.pr-184 { padding-right: var(--mico-size-184) !important; }
.pr-188 { padding-right: var(--mico-size-188) !important; }
.pr-192 { padding-right: var(--mico-size-192) !important; }
.pr-196 { padding-right: var(--mico-size-196) !important; }
.pr-200 { padding-right: var(--mico-size-200) !important; }
.pr-204 { padding-right: var(--mico-size-204) !important; }
.pr-208 { padding-right: var(--mico-size-208) !important; }
.pr-212 { padding-right: var(--mico-size-212) !important; }
.pr-216 { padding-right: var(--mico-size-216) !important; }
.pr-220 { padding-right: var(--mico-size-220) !important; }
.pr-224 { padding-right: var(--mico-size-224) !important; }
.pr-228 { padding-right: var(--mico-size-228) !important; }
.pr-232 { padding-right: var(--mico-size-232) !important; }
.pr-236 { padding-right: var(--mico-size-236) !important; }
.pr-240 { padding-right: var(--mico-size-240) !important; }
.pr-244 { padding-right: var(--mico-size-244) !important; }
.pr-248 { padding-right: var(--mico-size-248) !important; }
.pr-252 { padding-right: var(--mico-size-252) !important; }
.pr-256 { padding-right: var(--mico-size-256) !important; }
.pr-260 { padding-right: var(--mico-size-260) !important; }
.pr-264 { padding-right: var(--mico-size-264) !important; }
.pr-268 { padding-right: var(--mico-size-268) !important; }
.pr-272 { padding-right: var(--mico-size-272) !important; }
.pr-276 { padding-right: var(--mico-size-276) !important; }
.pr-280 { padding-right: var(--mico-size-280) !important; }
.pr-284 { padding-right: var(--mico-size-284) !important; }
.pr-288 { padding-right: var(--mico-size-288) !important; }
.pr-292 { padding-right: var(--mico-size-292) !important; }
.pr-296 { padding-right: var(--mico-size-296) !important; }
.pr-300 { padding-right: var(--mico-size-300) !important; }
.pr-304 { padding-right: var(--mico-size-304) !important; }
.pr-308 { padding-right: var(--mico-size-308) !important; }
.pr-312 { padding-right: var(--mico-size-312) !important; }
.pr-316 { padding-right: var(--mico-size-316) !important; }
.pr-320 { padding-right: var(--mico-size-320) !important; }
.pr-324 { padding-right: var(--mico-size-324) !important; }
.pr-328 { padding-right: var(--mico-size-328) !important; }
.pr-332 { padding-right: var(--mico-size-332) !important; }
.pr-336 { padding-right: var(--mico-size-336) !important; }
.pr-340 { padding-right: var(--mico-size-340) !important; }
.pr-344 { padding-right: var(--mico-size-344) !important; }
.pr-348 { padding-right: var(--mico-size-348) !important; }
.pr-352 { padding-right: var(--mico-size-352) !important; }
.pr-356 { padding-right: var(--mico-size-356) !important; }
.pr-360 { padding-right: var(--mico-size-360) !important; }
.pr-364 { padding-right: var(--mico-size-364) !important; }
.pr-368 { padding-right: var(--mico-size-368) !important; }
.pr-372 { padding-right: var(--mico-size-372) !important; }
.pr-376 { padding-right: var(--mico-size-376) !important; }
.pr-380 { padding-right: var(--mico-size-380) !important; }
.pr-384 { padding-right: var(--mico-size-384) !important; }
.pr-388 { padding-right: var(--mico-size-388) !important; }
.pr-392 { padding-right: var(--mico-size-392) !important; }
.pr-396 { padding-right: var(--mico-size-396) !important; }
.pr-400 { padding-right: var(--mico-size-400) !important; }


/* Padding-inline classes from 4 to 100 */
.px-4 { padding-inline: var(--mico-size-4) !important; }
.px-8 { padding-inline: var(--mico-size-8) !important; }
.px-12 { padding-inline: var(--mico-size-12) !important; }
.px-16 { padding-inline: var(--mico-size-16) !important; }
.px-20 { padding-inline: var(--mico-size-20) !important; }
.px-24 { padding-inline: var(--mico-size-24) !important; }
.px-28 { padding-inline: var(--mico-size-28) !important; }
.px-32 { padding-inline: var(--mico-size-32) !important; }
.px-36 { padding-inline: var(--mico-size-36) !important; }
.px-40 { padding-inline: var(--mico-size-40) !important; }
.px-44 { padding-inline: var(--mico-size-44) !important; }
.px-48 { padding-inline: var(--mico-size-48) !important; }
.px-52 { padding-inline: var(--mico-size-52) !important; }
.px-56 { padding-inline: var(--mico-size-56) !important; }
.px-60 { padding-inline: var(--mico-size-60) !important; }
.px-64 { padding-inline: var(--mico-size-64) !important; }
.px-68 { padding-inline: var(--mico-size-68) !important; }
.px-72 { padding-inline: var(--mico-size-72) !important; }
.px-76 { padding-inline: var(--mico-size-76) !important; }
.px-80 { padding-inline: var(--mico-size-80) !important; }
.px-84 { padding-inline: var(--mico-size-84) !important; }
.px-88 { padding-inline: var(--mico-size-88) !important; }
.px-92 { padding-inline: var(--mico-size-92) !important; }
.px-96 { padding-inline: var(--mico-size-96) !important; }
.px-100 { padding-inline: var(--mico-size-100) !important; }
.px-104 { padding-inline: var(--mico-size-104) !important; }
.px-108 { padding-inline: var(--mico-size-108) !important; }
.px-112 { padding-inline: var(--mico-size-112) !important; }
.px-116 { padding-inline: var(--mico-size-116) !important; }
.px-120 { padding-inline: var(--mico-size-120) !important; }
.px-124 { padding-inline: var(--mico-size-124) !important; }
.px-128 { padding-inline: var(--mico-size-128) !important; }
.px-132 { padding-inline: var(--mico-size-132) !important; }
.px-136 { padding-inline: var(--mico-size-136) !important; }
.px-140 { padding-inline: var(--mico-size-140) !important; }
.px-144 { padding-inline: var(--mico-size-144) !important; }
.px-148 { padding-inline: var(--mico-size-148) !important; }
.px-152 { padding-inline: var(--mico-size-152) !important; }
.px-156 { padding-inline: var(--mico-size-156) !important; }
.px-160 { padding-inline: var(--mico-size-160) !important; }
.px-164 { padding-inline: var(--mico-size-164) !important; }
.px-168 { padding-inline: var(--mico-size-168) !important; }
.px-172 { padding-inline: var(--mico-size-172) !important; }
.px-176 { padding-inline: var(--mico-size-176) !important; }
.px-180 { padding-inline: var(--mico-size-180) !important; }
.px-184 { padding-inline: var(--mico-size-184) !important; }
.px-188 { padding-inline: var(--mico-size-188) !important; }
.px-192 { padding-inline: var(--mico-size-192) !important; }
.px-196 { padding-inline: var(--mico-size-196) !important; }
.px-200 { padding-inline: var(--mico-size-200) !important; }
.px-204 { padding-inline: var(--mico-size-204) !important; }
.px-208 { padding-inline: var(--mico-size-208) !important; }
.px-212 { padding-inline: var(--mico-size-212) !important; }
.px-216 { padding-inline: var(--mico-size-216) !important; }
.px-220 { padding-inline: var(--mico-size-220) !important; }
.px-224 { padding-inline: var(--mico-size-224) !important; }
.px-228 { padding-inline: var(--mico-size-228) !important; }
.px-232 { padding-inline: var(--mico-size-232) !important; }
.px-236 { padding-inline: var(--mico-size-236) !important; }
.px-240 { padding-inline: var(--mico-size-240) !important; }
.px-244 { padding-inline: var(--mico-size-244) !important; }
.px-248 { padding-inline: var(--mico-size-248) !important; }
.px-252 { padding-inline: var(--mico-size-252) !important; }
.px-256 { padding-inline: var(--mico-size-256) !important; }
.px-260 { padding-inline: var(--mico-size-260) !important; }
.px-264 { padding-inline: var(--mico-size-264) !important; }
.px-268 { padding-inline: var(--mico-size-268) !important; }
.px-272 { padding-inline: var(--mico-size-272) !important; }
.px-276 { padding-inline: var(--mico-size-276) !important; }
.px-280 { padding-inline: var(--mico-size-280) !important; }
.px-284 { padding-inline: var(--mico-size-284) !important; }
.px-288 { padding-inline: var(--mico-size-288) !important; }
.px-292 { padding-inline: var(--mico-size-292) !important; }
.px-296 { padding-inline: var(--mico-size-296) !important; }
.px-300 { padding-inline: var(--mico-size-300) !important; }
.px-304 { padding-inline: var(--mico-size-304) !important; }
.px-308 { padding-inline: var(--mico-size-308) !important; }
.px-312 { padding-inline: var(--mico-size-312) !important; }
.px-316 { padding-inline: var(--mico-size-316) !important; }
.px-320 { padding-inline: var(--mico-size-320) !important; }
.px-324 { padding-inline: var(--mico-size-324) !important; }
.px-328 { padding-inline: var(--mico-size-328) !important; }
.px-332 { padding-inline: var(--mico-size-332) !important; }
.px-336 { padding-inline: var(--mico-size-336) !important; }
.px-340 { padding-inline: var(--mico-size-340) !important; }
.px-344 { padding-inline: var(--mico-size-344) !important; }
.px-348 { padding-inline: var(--mico-size-348) !important; }
.px-352 { padding-inline: var(--mico-size-352) !important; }
.px-356 { padding-inline: var(--mico-size-356) !important; }
.px-360 { padding-inline: var(--mico-size-360) !important; }
.px-364 { padding-inline: var(--mico-size-364) !important; }
.px-368 { padding-inline: var(--mico-size-368) !important; }
.px-372 { padding-inline: var(--mico-size-372) !important; }
.px-376 { padding-inline: var(--mico-size-376) !important; }
.px-380 { padding-inline: var(--mico-size-380) !important; }
.px-384 { padding-inline: var(--mico-size-384) !important; }
.px-388 { padding-inline: var(--mico-size-388) !important; }
.px-392 { padding-inline: var(--mico-size-392) !important; }
.px-396 { padding-inline: var(--mico-size-396) !important; }
.px-400 { padding-inline: var(--mico-size-400) !important; }


/* Padding-block classes from 4 to 100 */
.py-4 { padding-block: var(--mico-size-4) !important; }
.py-8 { padding-block: var(--mico-size-8) !important; }
.py-12 { padding-block: var(--mico-size-12) !important; }
.py-16 { padding-block: var(--mico-size-16) !important; }
.py-20 { padding-block: var(--mico-size-20) !important; }
.py-24 { padding-block: var(--mico-size-24) !important; }
.py-28 { padding-block: var(--mico-size-28) !important; }
.py-32 { padding-block: var(--mico-size-32) !important; }
.py-36 { padding-block: var(--mico-size-36) !important; }
.py-40 { padding-block: var(--mico-size-40) !important; }
.py-44 { padding-block: var(--mico-size-44) !important; }
.py-48 { padding-block: var(--mico-size-48) !important; }
.py-52 { padding-block: var(--mico-size-52) !important; }
.py-56 { padding-block: var(--mico-size-56) !important; }
.py-60 { padding-block: var(--mico-size-60) !important; }
.py-64 { padding-block: var(--mico-size-64) !important; }
.py-68 { padding-block: var(--mico-size-68) !important; }
.py-72 { padding-block: var(--mico-size-72) !important; }
.py-76 { padding-block: var(--mico-size-76) !important; }
.py-80 { padding-block: var(--mico-size-80) !important; }
.py-84 { padding-block: var(--mico-size-84) !important; }
.py-88 { padding-block: var(--mico-size-88) !important; }
.py-92 { padding-block: var(--mico-size-92) !important; }
.py-96 { padding-block: var(--mico-size-96) !important; }
.py-100 { padding-block: var(--mico-size-100) !important; }
.py-104 { padding-block: var(--mico-size-104) !important; }
.py-108 { padding-block: var(--mico-size-108) !important; }
.py-112 { padding-block: var(--mico-size-112) !important; }
.py-116 { padding-block: var(--mico-size-116) !important; }
.py-120 { padding-block: var(--mico-size-120) !important; }
.py-124 { padding-block: var(--mico-size-124) !important; }
.py-128 { padding-block: var(--mico-size-128) !important; }
.py-132 { padding-block: var(--mico-size-132) !important; }
.py-136 { padding-block: var(--mico-size-136) !important; }
.py-140 { padding-block: var(--mico-size-140) !important; }
.py-144 { padding-block: var(--mico-size-144) !important; }
.py-148 { padding-block: var(--mico-size-148) !important; }
.py-152 { padding-block: var(--mico-size-152) !important; }
.py-156 { padding-block: var(--mico-size-156) !important; }
.py-160 { padding-block: var(--mico-size-160) !important; }
.py-164 { padding-block: var(--mico-size-164) !important; }
.py-168 { padding-block: var(--mico-size-168) !important; }
.py-172 { padding-block: var(--mico-size-172) !important; }
.py-176 { padding-block: var(--mico-size-176) !important; }
.py-180 { padding-block: var(--mico-size-180) !important; }
.py-184 { padding-block: var(--mico-size-184) !important; }
.py-188 { padding-block: var(--mico-size-188) !important; }
.py-192 { padding-block: var(--mico-size-192) !important; }
.py-196 { padding-block: var(--mico-size-196) !important; }
.py-200 { padding-block: var(--mico-size-200) !important; }
.py-204 { padding-block: var(--mico-size-204) !important; }
.py-208 { padding-block: var(--mico-size-208) !important; }
.py-212 { padding-block: var(--mico-size-212) !important; }
.py-216 { padding-block: var(--mico-size-216) !important; }
.py-220 { padding-block: var(--mico-size-220) !important; }
.py-224 { padding-block: var(--mico-size-224) !important; }
.py-228 { padding-block: var(--mico-size-228) !important; }
.py-232 { padding-block: var(--mico-size-232) !important; }
.py-236 { padding-block: var(--mico-size-236) !important; }
.py-240 { padding-block: var(--mico-size-240) !important; }
.py-244 { padding-block: var(--mico-size-244) !important; }
.py-248 { padding-block: var(--mico-size-248) !important; }
.py-252 { padding-block: var(--mico-size-252) !important; }
.py-256 { padding-block: var(--mico-size-256) !important; }
.py-260 { padding-block: var(--mico-size-260) !important; }
.py-264 { padding-block: var(--mico-size-264) !important; }
.py-268 { padding-block: var(--mico-size-268) !important; }
.py-272 { padding-block: var(--mico-size-272) !important; }
.py-276 { padding-block: var(--mico-size-276) !important; }
.py-280 { padding-block: var(--mico-size-280) !important; }
.py-284 { padding-block: var(--mico-size-284) !important; }
.py-288 { padding-block: var(--mico-size-288) !important; }
.py-292 { padding-block: var(--mico-size-292) !important; }
.py-296 { padding-block: var(--mico-size-296) !important; }
.py-300 { padding-block: var(--mico-size-300) !important; }
.py-304 { padding-block: var(--mico-size-304) !important; }
.py-308 { padding-block: var(--mico-size-308) !important; }
.py-312 { padding-block: var(--mico-size-312) !important; }
.py-316 { padding-block: var(--mico-size-316) !important; }
.py-320 { padding-block: var(--mico-size-320) !important; }
.py-324 { padding-block: var(--mico-size-324) !important; }
.py-328 { padding-block: var(--mico-size-328) !important; }
.py-332 { padding-block: var(--mico-size-332) !important; }
.py-336 { padding-block: var(--mico-size-336) !important; }
.py-340 { padding-block: var(--mico-size-340) !important; }
.py-344 { padding-block: var(--mico-size-344) !important; }
.py-348 { padding-block: var(--mico-size-348) !important; }
.py-352 { padding-block: var(--mico-size-352) !important; }
.py-356 { padding-block: var(--mico-size-356) !important; }
.py-360 { padding-block: var(--mico-size-360) !important; }
.py-364 { padding-block: var(--mico-size-364) !important; }
.py-368 { padding-block: var(--mico-size-368) !important; }
.py-372 { padding-block: var(--mico-size-372) !important; }
.py-376 { padding-block: var(--mico-size-376) !important; }
.py-380 { padding-block: var(--mico-size-380) !important; }
.py-384 { padding-block: var(--mico-size-384) !important; }
.py-388 { padding-block: var(--mico-size-388) !important; }
.py-392 { padding-block: var(--mico-size-392) !important; }
.py-396 { padding-block: var(--mico-size-396) !important; }
.py-400 { padding-block: var(--mico-size-400) !important; }
