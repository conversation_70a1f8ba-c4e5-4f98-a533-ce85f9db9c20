/**
 * Mico CSS Framework - Layout Utilities
 *
 * This file provides utilities for controlling the layout of elements:
 *
 * - Box model properties (sizing, decoration, etc.)
 * - Float and clear properties
 * - Isolation and stacking context
 * - Object fit and position
 * - Overflow behavior
 * - Visibility control
 * - Positioning (static, fixed, absolute, etc.)
 * - Display properties (block, inline, flex, grid, etc.)
 * - Flexbox properties (flex container and item control)
 * - Grid properties (grid container and item control)
 * - Width and height control
 * - Min and max width/height control
 * - Aspect ratio control
 * - Overscroll behavior
 * - Z-index control
 * - Gap control (for flex and grid)
 *
 * USAGE:
 * Box Model: .box-border, .box-content
 * Float and Clear: .float-right, .clear-both
 * Isolation: .isolate
 * Object Fit and Position: .object-contain, .object-top
 * Overflow: .overflow-hidden, .overflow-x-auto
 * Visibility: .visibility-hidden
 * Positioning: .fixed, .absolute, .top-0
 * Display: .d-block, .d-flex
 * Flexbox: .flex-row, .flex-wrap, .justify-content-center
 * Grid: .grid, .grid-cols-2, .grid-rows-3
 * Width and Height: .w-100, .h-50
 * Min and Max Width/Height: .min-w-20, .max-h-80
 * Aspect Ratio: .aspect-16-9
 * Z-index: .z-0, .z-10
 * Gap: .gap-4, .gap-x-8
 *
 *

/* Box Model */
.box-border { box-sizing: border-box; }
.box-content { box-sizing: content-box; }

.decoration-slice { box-decoration-break: slice; }
.decoration-clone { box-decoration-break: clone; }

/* Float and Clear */
.float-right { float: right !important; }
.float-left { float: left !important; }
.float-none { float: none !important; }

.clear-left { clear: left !important; }
.clear-right { clear: right !important; }
.clear-both { clear: both !important; }
.clear-none { clear: none !important; }

/* Isolation */
.isolate { isolation: isolate; }
.isolate-auto { isolation: auto; }

/* Object Fit and Position */
.object-contain { object-fit: contain; }
.object-cover { object-fit: cover; }
.object-fill { object-fit: fill; }
.object-none { object-fit: none; }
.object-scale-down { object-fit: scale-down; }

.object-top { object-position: top; }
.object-bottom { object-position: bottom; }
.object-center { object-position: center; }
.object-left { object-position: left; }
.object-right { object-position: right; }
.object-top-left { object-position: top left; }
.object-top-right { object-position: top right; }
.object-bottom-left { object-position: bottom left; }
.object-bottom-right { object-position: bottom right; }

/* Overflow */
.overflow-visible { overflow: visible; }
.overflow-hidden { overflow: hidden; }
.overflow-clip { overflow: clip; }
.overflow-scroll { overflow: scroll; }
.overflow-auto { overflow: auto; }

.overflow-x-visible { overflow-x: visible; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-clip { overflow-x: clip; }
.overflow-x-scroll { overflow-x: scroll; }
.overflow-x-auto { overflow-x: auto; }

.overflow-y-visible { overflow-y: visible; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-clip { overflow-y: clip; }
.overflow-y-scroll { overflow-y: scroll; }
.overflow-y-auto { overflow-y: auto; }

/* Visibility */
.visibility-visible { visibility: visible; }
.visibility-hidden { visibility: hidden; }
.visibility-collapse { visibility: collapse; }

/* Overscroll Behavior */
.overscroll-auto { overscroll-behavior: auto; }
.overscroll-contain { overscroll-behavior: contain; }
.overscroll-none { overscroll-behavior: none; }

.overscroll-x-auto { overscroll-behavior-x: auto; }
.overscroll-x-contain { overscroll-behavior-x: contain; }
.overscroll-x-none { overscroll-behavior-x: none; }

.overscroll-y-auto { overscroll-behavior-y: auto; }
.overscroll-y-contain { overscroll-behavior-y: contain; }
.overscroll-y-none { overscroll-behavior-y: none; }

/* Position */
.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

.inset-0 { inset: 0; }
.inset-auto { inset: auto; }
.inset-x-0 { left: 0; right: 0; }
.inset-y-0 { top: 0; bottom: 0; }
.inset-x-auto { left: auto; right: auto; }
.inset-y-auto { top: auto; bottom: auto; }

/* Display */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }
.d-table { display: table !important; }
.d-table-row { display: table-row !important; }
.d-table-cell { display: table-cell !important; }
.d-table-column { display: table-column !important; }
.d-table-column-group { display: table-column-group !important; }
.d-table-header-group { display: table-header-group !important; }
.d-table-footer-group { display: table-footer-group !important; }
.d-table-row-group { display: table-row-group !important; }
.d-flow-root { display: flow-root !important; }
.d-contents { display: contents !important; }
.d-list-item { display: list-item !important; }
.d-ruby { display: ruby !important; }
.d-ruby-base { display: ruby-base !important; }
.d-ruby-text { display: ruby-text !important; }
.d-ruby-base-container { display: ruby-base-container !important; }

/* Flexbox */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }


.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }
.align-content-end { align-content: flex-end !important; }
.align-content-center { align-content: center !important; }
.align-content-between { align-content: space-between !important; }
.align-content-around { align-content: space-around !important; }
.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }
.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

.flex-1 { flex: 1 1 0% !important; }
.flex-auto { flex: 1 1 auto !important; }
.flex-initial { flex: 0 1 auto !important; }
.flex-none { flex: none !important; }

/* Z-index */
.z-auto { z-index: auto; }
.z-0 { z-index: 0; }
.z-4 { z-index: 4; }
.z-8 { z-index: 8; }
.z-12 { z-index: 12; }
.z-16 { z-index: 16; }
.z-20 { z-index: 20; }
.z-n4 { z-index: -4; }
.z-n8 { z-index: -8; }
.z-n12 { z-index: -12; }
.z-n16 { z-index: -16; }
.z-n20 { z-index: -20; }
.z-100 { z-index: 100; }
.z-200 { z-index: 200; }
.z-max { z-index: 9999; }

/* Gap */
.gap-0 { gap: var(--mico-size-0) !important; }
.gap-4 { gap: var(--mico-size-4) !important; }
.gap-8 { gap: var(--mico-size-8) !important; }
.gap-12 { gap: var(--mico-size-12) !important; }
.gap-16 { gap: var(--mico-size-16) !important; }
.gap-20 { gap: var(--mico-size-20) !important; }
.gap-24 { gap: var(--mico-size-24) !important; }
.gap-28 { gap: var(--mico-size-28) !important; }
.gap-32 { gap: var(--mico-size-32) !important; }
.gap-36 { gap: var(--mico-size-36) !important; }
.gap-40 { gap: var(--mico-size-40) !important; }

/* Grid */
.grid-cols-1 { grid-template-columns: repeat(1, 1fr) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr) !important; }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr) !important; }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr) !important; }
.grid-cols-12 { grid-template-columns: repeat(12, 1fr) !important; }

.grid-rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)) !important; }
.grid-rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)) !important; }
.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)) !important; }
.grid-rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)) !important; }
.grid-rows-5 { grid-template-rows: repeat(5, minmax(0, 1fr)) !important; }
.grid-rows-6 { grid-template-rows: repeat(6, minmax(0, 1fr)) !important; }

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(var(--mico-grid-min-column-width), 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(var(--mico-grid-min-column-width), 1fr));
}

.grid-area {
  grid-area: var(--mico-grid-area);
}

.grid-template-areas {
  grid-template-areas: var(--mico-grid-template-areas);
}

/* Width and Height */
.w-none { width: none !important; }
.w-auto { width: auto !important; }
.w-screen { width: 100vw !important; }
.w-10p { width: 10% !important; }
.w-20p { width: 20% !important; }
.w-30p { width: 30% !important; }
.w-40p { width: 40% !important; }
.w-50p { width: 50% !important; }
.w-60p { width: 60% !important; }
.w-70p { width: 70% !important; }
.w-80p { width: 80% !important; }
.w-90p { width: 90% !important; }
.w-100p { width: 100% !important; }

.h-none { height: none !important; }
.h-auto { height: auto !important; }
.h-screen { height: 100vh !important; }
.h-10p { height: 10% !important; }
.h-20p { height: 20% !important; }
.h-30p { height: 30% !important; }
.h-40p { height: 40% !important; }
.h-50p { height: 50% !important; }
.h-60p { height: 60% !important; }
.h-70p { height: 70% !important; }
.h-80p { height: 80% !important; }
.h-90p { height: 90% !important; }
.h-100p { height: 100% !important; }

/* Column spans */
.col-1 { grid-column: span 1 !important; }
.col-2 { grid-column: span 2 !important; }
.col-3 { grid-column: span 3 !important; }
.col-4 { grid-column: span 4 !important; }
.col-5 { grid-column: span 5 !important; }
.col-6 { grid-column: span 6 !important; }
.col-7 { grid-column: span 7 !important; }
.col-8 { grid-column: span 8 !important; }
.col-9 { grid-column: span 9 !important; }
.col-10 { grid-column: span 10 !important; }
.col-11 { grid-column: span 11 !important; }
.col-12 { grid-column: span 12 !important; }



/* Advanced grid features */
.grid-masonry {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(var(--mico-grid-min-column-width), 1fr));
  grid-auto-rows: 1px;
}

.grid-masonry > * {
  break-inside: avoid;
}

/* Fallbacks for older browsers */
@supports not (display: grid) {
  .grid-masonry {
    column-count: auto;
    column-width: var(--mico-grid-min-column-width);
  }
  .grid-masonry > * {
    display: inline-block;
    width: 100%;
  }
  
  .grid {
    display: flex;
    flex-wrap: wrap;
    margin: calc(-1 * var(--mico-gap-md));
  }


  /* Grid item styles */
  .grid > * {
    flex-basis: calc((100% / var(--mico-grid-column-count)) - (2 * var(--mico-gap-md)));
    margin: var(--mico-gap-md);
  }

  /* Responsive grid items */
  @media (max-width: 768px) {
    .grid > * {
      flex-basis: calc(50% - (2 * var(--mico-gap-md)));
    }
  }

  @media (max-width: 320px) {
    .grid > * {
      flex-basis: 100%;
    }
  }

}