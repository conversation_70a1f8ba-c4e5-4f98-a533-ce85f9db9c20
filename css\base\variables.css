/**
 * Mico CSS Framework - Core Variables
 *
 * This file defines the foundational CSS variables that power the Mico CSS framework.
 * These variables create a consistent design system that can be used throughout your project.
 *
 * USAGE:
 * Variables are accessed using the var() function:
 * Example: font-size: var(--mico-text-md);
 */

/* ========================================================================== */
/* RESPONSIVE BREAKPOINTS                                                     */
/* ========================================================================== */

:root {
  /**
   * Breakpoints define the viewport width thresholds for responsive design.
   * These values align with common device sizes and provide a consistent
   * foundation for responsive layouts.
   *
   * Usage: @media (min-width: var(--mico-breakpoint-md)) { ... }
   */
  --mico-breakpoint-xs: 320px;   /* Extra small devices (phones) */
  --mico-breakpoint-sm: 576px;   /* Small devices (large phones, portrait tablets) */
  --mico-breakpoint-md: 768px;   /* Medium devices (tablets) */
  --mico-breakpoint-lg: 992px;   /* Large devices (desktops) */
  --mico-breakpoint-xl: 1280px;  /* Extra large devices (large desktops) */
  --mico-breakpoint-2xl: 1440px; /* Ultra large devices (wide screens) */
  --mico-breakpoint-3xl: 1600px;  /* Super large devices (wider screens) */


  /* ====================================================================== */
  /* COMMON VALUES SYSTEM                                                   */
  /* ====================================================================== */

  /**
   * Common Values
   *
   * These variables define frequently used values to ensure consistency
   * and reduce repetition throughout the framework.
   */
  --mico-value-auto: auto;
  --mico-value-none: none;
  --mico-value-normal: normal;
  --mico-value-inherit: inherit;
  --mico-value-initial: initial;
  --mico-value-unset: unset;
  --mico-value-current: currentColor;
  --mico-value-transparent: transparent;
  --mico-value-0: 0;

  /* ====================================================================== */
  /* TYPOGRAPHY SYSTEM                                                      */
  /* ====================================================================== */

  /**
   * Font Families
   *
   * Standard font stacks for different text purposes.
   * These provide fallbacks for consistent typography across platforms.
   */

  /**
   * Font Sizes using clamp() for responsive scaling
   *
   * The clamp() function takes three values: minimum, preferred, and maximum
   * This creates text that scales smoothly between viewport sizes while
   * maintaining readable minimum and maximum sizes.
   *
   * Format: clamp(min-size, viewport-based-size, max-size)
   */
  --mico-fs-xs: clamp(0.995rem, 1.5vw, 0.975rem); /* Extra small text, captions */
  --mico-fs-sm: clamp(0.980rem, 2vw, 1.2rem);    /* Small text */
  --mico-fs-md: clamp(1.051rem, 2.5vw, 1.30rem);  /* Standard body text */
  --mico-fs-lg: clamp(1.25rem, 3vw, 1.675rem); /* Small headings (h4) */
  --mico-fs-xl: clamp(1.5rem, 4vw, 2rem);   /* Medium headings (h3) */
  --mico-fs-2xl: clamp(1.875rem, 5vw, 3rem);   /* Large headings (h2) */
  --mico-fs-3xl: clamp(2.25rem, 5.5vw, 3.5rem); /* Large headings (h1) */
  --mico-fs-4xl: clamp(2.5rem, 6vw, 4rem);     /* Large headings */
  --mico-fs-5xl: clamp(3rem, 6.5vw, 4.5rem);   /* Large headings */
  --mico-fs-6xl: clamp(3.5rem, 7vw, 5rem);     /* Large headings */
  --mico-fs-7xl: clamp(3.75rem, 7.5vw, 5.5rem); /* Very large headings */
  --mico-fs-8xl: clamp(4rem, 8vw, 6rem);       /* Extra massive headings */
  --mico-fs-9xl: clamp(4.5rem, 9vw, 7rem);     /* Massive headings */

  /**
   * Font Weights
   *
   * Standard font weight values from 100 (thinnest) to 900 (boldest)
   * Using consistent naming pattern with descriptive suffixes
   */
  --mico-fw-100: 100;  /* Thinnest */
  --mico-fw-200: 200;  /* Extra light */
  --mico-fw-300: 300;  /* Light */
  --mico-fw-400: 400;  /* Normal/Regular */
  --mico-fw-500: 500;  /* Medium */
  --mico-fw-600: 600;  /* Semi bold */
  --mico-fw-700: 700;  /* Bold */
  --mico-fw-800: 800;  /* Extra bold */
  --mico-fw-900: 900;  /* Black/Heaviest */

  /**
   * Font Stretch Properties
   *
   * Controls the width of glyphs if the font family has variable widths.
   * Values represent percentage of normal width.
   */
  --mico-font-stretch-ultra-condensed: ultra-condensed; /* 50% */
  --mico-font-stretch-extra-condensed: extra-condensed; /* 62.5% */
  --mico-font-stretch-condensed: condensed;             /* 75% */
  --mico-font-stretch-semi-condensed: semi-condensed;   /* 87.5% */
  --mico-font-stretch-normal: var(--mico-value-normal); /* 100% */
  --mico-font-stretch-semi-expanded: semi-expanded;     /* 112.5% */
  --mico-font-stretch-expanded: expanded;               /* 125% */
  --mico-font-stretch-extra-expanded: extra-expanded;   /* 150% */
  --mico-font-stretch-ultra-expanded: ultra-expanded;   /* 200% */

  /**
   * Font Style Properties
   *
   * Standard font style values for consistent usage.
   */
  --mico-font-style-normal: var(--mico-value-normal);
  --mico-font-style-italic: italic;

  /**
   * Font Variant Numeric Properties
   *
   * Controls numeric font features for enhanced typography.
   */
  --mico-font-variant-numeric-normal: var(--mico-value-normal);
  --mico-font-variant-numeric-ordinal: ordinal;
  --mico-font-variant-numeric-slashed-zero: slashed-zero;
  --mico-font-variant-numeric-lining-nums: lining-nums;
  --mico-font-variant-numeric-oldstyle-nums: oldstyle-nums;
  --mico-font-variant-numeric-proportional-nums: proportional-nums;
  --mico-font-variant-numeric-tabular-nums: tabular-nums;
  --mico-font-variant-numeric-diagonal-fractions: diagonal-fractions;
  --mico-font-variant-numeric-stacked-fractions: stacked-fractions;

  /**
   * Font Variant Ligatures Properties
   *
   * Controls ligature rendering for enhanced typography.
   */
  --mico-font-variant-ligatures-common: common-ligatures;
  --mico-font-variant-ligatures-no-common: no-common-ligatures;
  --mico-font-variant-ligatures-discretionary: discretionary-ligatures;
  --mico-font-variant-ligatures-no-discretionary: no-discretionary-ligatures;
  --mico-font-variant-ligatures-historical: historical-ligatures;
  --mico-font-variant-ligatures-no-historical: no-historical-ligatures;
  --mico-font-variant-ligatures-contextual: contextual;
  --mico-font-variant-ligatures-no-contextual: no-contextual;

  /**
   * Font Variant Caps Properties
   *
   * Controls capitalization rendering for enhanced typography.
   */
  --mico-font-variant-caps-normal: var(--mico-value-normal);
  --mico-font-variant-caps-small-caps: small-caps;
  --mico-font-variant-caps-all-small-caps: all-small-caps;
  --mico-font-variant-caps-petite-caps: petite-caps;
  --mico-font-variant-caps-all-petite-caps: all-petite-caps;
  --mico-font-variant-caps-unicase: unicase;
  --mico-font-variant-caps-titling-caps: titling-caps;

  /**
   * Line Heights
   *
   * Line height controls the vertical spacing between lines of text.
   * - Values below 1.5 are good for headings
   * - Values 1.5-1.7 are ideal for body text for readability
   * - Larger values create more spacing for easier reading
   */
  --mico-lh-xs: 1;       /* Compact (headings) */
  --mico-lh-sm: 1.25;    /* Slightly compact */
  --mico-lh-md: 1.5;     /* Standard body text */
  --mico-lh-lg: 1.625;   /* Slightly relaxed */
  --mico-lh-xl: 2;       /* Loose (easy reading) */
  --mico-lh-2xl: 0.75rem; /* Fixed line height for small text */
  --mico-lh-3xl: 1rem;    /* Fixed line height */
  --mico-lh-4xl: 1.25rem; /* Fixed line height */
  --mico-lh-5xl: 1.5rem;  /* Fixed line height */
  --mico-lh-6xl: 1.75rem; /* Fixed line height */
  --mico-lh-7xl: 2rem;    /* Fixed line height */
  --mico-lh-8xl: 2.25rem; /* Fixed line height */
  --mico-lh-9xl: 2.5rem;  /* Fixed line height */


  /**
   * Letter Spacing
   *
   * Controls the horizontal spacing between characters.
   * - Negative values bring letters closer together
   * - Positive values spread letters apart
   * - 'em' units scale with the font size
   */
  --mico-ls-xs: -0.05em;   /* Tighter spacing */
  --mico-ls-sm: -0.025em;  /* Slightly tighter */
  --mico-ls-md: 0em;       /* Normal spacing */
  --mico-ls-lg: 0.025em;   /* Slightly wider */
  --mico-ls-xl: 0.05em;    /* Wider spacing */
  --mico-ls-2xl: 0.1em;    /* Widest spacing */

  /**
   * Text Decoration Properties
   *
   * Controls the appearance of underlines and other text decorations
   */
  --mico-underline-offset-auto: var(--mico-value-auto);
  --mico-underline-offset-0: var(--mico-value-0);
  --mico-underline-offset-1: 1px;
  --mico-underline-offset-2: 2px;
  --mico-underline-offset-4: 4px;
  --mico-underline-offset-8: 8px;
  --mico-underline-offset: 0.15em;  /* Default distance between text and underline */

  --mico-decoration-thickness-auto: var(--mico-value-auto);
  --mico-decoration-thickness-from-font: from-font;
  --mico-decoration-thickness-0: 0px;
  --mico-decoration-thickness-1: 1px;
  --mico-decoration-thickness-2: 2px;
  --mico-decoration-thickness-4: 4px;
  --mico-decoration-thickness-8: 8px;
  --mico-underline-thickness: 0.05em; /* Default thickness of the underline */

  --mico-decoration-style-solid: solid;
  --mico-decoration-style-double: double;
  --mico-decoration-style-dotted: dotted;
  --mico-decoration-style-dashed: dashed;
  --mico-decoration-style-wavy: wavy;

  --mico-underline-position-auto: var(--mico-value-auto);
  --mico-underline-position-under: under;
  --mico-underline-position-left: left;
  --mico-underline-position-right: right;

  /**
   * Text Transform Properties
   *
   * Controls text case transformation
   */
  --mico-text-transform-uppercase: uppercase;
  --mico-text-transform-lowercase: lowercase;
  --mico-text-transform-capitalize: capitalize;
  --mico-text-transform-none: var(--mico-value-none);

  /**
   * Text Alignment Properties
   *
   * These variables define text alignment values for consistent usage.
   */
  --mico-text-align-left: left;
  --mico-text-align-center: center;
  --mico-text-align-right: right;
  --mico-text-align-justify: justify;
  --mico-text-align-start: start;
  --mico-text-align-end: end;

  /**
   * Text Overflow Properties
   *
   * Controls how overflowing text is handled
   */
  --mico-text-overflow-ellipsis: ellipsis;
  --mico-text-overflow-clip: clip;

  /**
   * White Space Properties
   *
   * Controls how whitespace is handled
   */
  --mico-whitespace-normal: var(--mico-value-normal);
  --mico-whitespace-nowrap: nowrap;
  --mico-whitespace-pre: pre;
  --mico-whitespace-pre-line: pre-line;
  --mico-whitespace-pre-wrap: pre-wrap;
  --mico-whitespace-break-spaces: break-spaces;



  /**
   * Text Indent Properties
   *
   * Controls the indentation of the first line of text
   */
  --mico-indent-0: var(--mico-value-0);
  --mico-indent-xs: 1px;
  --mico-indent-sm: 0.25rem;
  --mico-indent-md: 0.5rem;
  --mico-indent-lg: 1rem;
  --mico-indent-xl: 2rem;

  /**
   * Text Shadow Properties
   *
   * Controls text shadow effects
   */
  --mico-text-shadow-none: var(--mico-value-none);
  --mico-text-shadow-xs: 1px 1px 2px rgba(0, 0, 0, 0.1);
  --mico-text-shadow-sm: 2px 2px 4px rgba(0, 0, 0, 0.1);
  --mico-text-shadow-md: 4px 4px 6px rgba(0, 0, 0, 0.1);
  --mico-text-shadow-lg: 6px 6px 8px rgba(0, 0, 0, 0.15);

  /**
   * Text Stroke Properties
   *
   * Controls text stroke (outline) effects
   */
  --mico-text-stroke-xs: 1px;
  --mico-text-stroke-sm: 2px;
  --mico-text-stroke-md: 4px;

  /**
   * List Style Properties
   *
   * Controls list styling
   */
  --mico-list-style-type-none: var(--mico-value-none);
  --mico-list-style-type-disc: disc;
  --mico-list-style-type-decimal: decimal;
  --mico-list-style-type-square: square;
  --mico-list-style-type-upper-roman: upper-roman;
  --mico-list-style-type-lower-roman: lower-roman;
  --mico-list-style-type-upper-alpha: upper-alpha;
  --mico-list-style-type-lower-alpha: lower-alpha;

  --mico-list-style-position-inside: inside;
  --mico-list-style-position-outside: outside;

  /**
   * Text Direction Properties
   *
   * Controls text direction for internationalization
   */
  --mico-text-direction-ltr: ltr;
  --mico-text-direction-rtl: rtl;

  /**
   * Writing Mode Properties
   *
   * Controls text writing direction
   */
  --mico-writing-mode-horizontal-tb: horizontal-tb;
  --mico-writing-mode-vertical-rl: vertical-rl;
  --mico-writing-mode-vertical-lr: vertical-lr;

  /**
   * Text Orientation Properties
   *
   * Controls text orientation in vertical writing modes
   */
  --mico-text-orientation-mixed: mixed;
  --mico-text-orientation-upright: upright;
  --mico-text-orientation-sideways: sideways;

  /**
   * Hyphens Properties
   *
   * Controls automatic hyphenation
   */
  --mico-hyphens-none: var(--mico-value-none);
  --mico-hyphens-manual: manual;
  --mico-hyphens-auto: var(--mico-value-auto);

  /**
   * Text Align Last Properties
   *
   * Controls alignment of the last line in a block
   */
  --mico-text-align-last-auto: var(--mico-value-auto);
  --mico-text-align-last-start: start;
  --mico-text-align-last-end: end;
  --mico-text-align-last-left: left;
  --mico-text-align-last-right: right;
  --mico-text-align-last-center: center;
  --mico-text-align-last-justify: justify;

  /**
   * Text Justify Properties
   *
   * Controls how justified text is spaced
   */
  --mico-text-justify-auto: var(--mico-value-auto);
  --mico-text-justify-inter-word: inter-word;
  --mico-text-justify-inter-character: inter-character;
  --mico-text-justify-none: var(--mico-value-none);

  /**
   * User Select Properties
   *
   * Controls whether text can be selected
   */
  --mico-user-select-none: var(--mico-value-none);
  --mico-user-select-text: text;
  --mico-user-select-all: all;
  --mico-user-select-auto: var(--mico-value-auto);

  /**
   * Word Break Properties
   *
   * Controls how words break at line endings
   */
  --mico-word-break-normal: var(--mico-value-normal);
  --mico-word-break-break-all: break-all;
  --mico-word-break-keep-all: keep-all;
  --mico-overflow-wrap-normal: var(--mico-value-normal);
  --mico-overflow-wrap-break-word: break-word;

  /* ====================================================================== */
  /* SPACING SYSTEM                                                         */
  /* ====================================================================== */

  /**
   * Spacing Scale
   *
   * A comprehensive spacing system based on a 4px unit.
   * This creates a consistent rhythm throughout the interface.
   *
   * The base unit (--mico-size-unit) is 4px, and all other spacing
   * values are multiples of this unit, making it easy to maintain
   * consistent proportional spacing.
   */
  --mico-size-unit: 4px;            /* Base unit for spacing system */

  /* Core spacing values (most commonly used) */
  --mico-size-0: 0;                 /* No spacing */
  --mico-size-1: 1px;               /* Pixel-perfect adjustments */
  --mico-size-2: 2px;               /* Minimal spacing */
  --mico-size-4: calc(var(--mico-size-unit) * 1);    /* 4px - Tiny spacing */
  --mico-size-8: calc(var(--mico-size-unit) * 2);    /* 8px - Extra small spacing */
  --mico-size-12: calc(var(--mico-size-unit) * 3);   /* 12px - Small spacing */
  --mico-size-16: calc(var(--mico-size-unit) * 4);   /* 16px - Default spacing */
  --mico-size-20: calc(var(--mico-size-unit) * 5);   /* 20px - Medium spacing */
  --mico-size-24: calc(var(--mico-size-unit) * 6);   /* 24px - Medium spacing */
  --mico-size-28: calc(var(--mico-size-unit) * 7);   /* 28px - Medium spacing */
  --mico-size-32: calc(var(--mico-size-unit) * 8);   /* 32px - Large spacing */
  --mico-size-36: calc(var(--mico-size-unit) * 9);   /* 36px - Large spacing */
  --mico-size-40: calc(var(--mico-size-unit) * 10);  /* 40px - Extra large spacing */
  --mico-size-44: calc(var(--mico-size-unit) * 11);  /* 44px - Extra large spacing */
  --mico-size-48: calc(var(--mico-size-unit) * 12);  /* 48px - Extra large spacing */
  --mico-size-52: calc(var(--mico-size-unit) * 13);  /* 52px - Extra large spacing */
  --mico-size-56: calc(var(--mico-size-unit) * 14);  /* 56px - Huge spacing */
  --mico-size-60: calc(var(--mico-size-unit) * 15);  /* 60px - Huge spacing */
  --mico-size-64: calc(var(--mico-size-unit) * 16);  /* 64px - Huge spacing */
  --mico-size-72: calc(var(--mico-size-unit) * 18);  /* 72px - Huge spacing */
  --mico-size-80: calc(var(--mico-size-unit) * 20);  /* 80px - Huge spacing */
  --mico-size-96: calc(var(--mico-size-unit) * 24);  /* 96px - Giant spacing */
  --mico-size-100: calc(var(--mico-size-unit) * 25); /* 100px - Giant spacing */
  --mico-size-112: calc(var(--mico-size-unit) * 28); /* 112px - Section spacing */
  --mico-size-128: calc(var(--mico-size-unit) * 32); /* 128px - Section spacing */
  --mico-size-144: calc(var(--mico-size-unit) * 36); /* 144px - Large section spacing */
  --mico-size-160: calc(var(--mico-size-unit) * 40); /* 160px - Large section spacing */
  --mico-size-176: calc(var(--mico-size-unit) * 44); /* 176px - Extra large section spacing */
  --mico-size-192: calc(var(--mico-size-unit) * 48); /* 192px - Extra large section spacing */
  --mico-size-208: calc(var(--mico-size-unit) * 52); /* 208px - Huge section spacing */
  --mico-size-224: calc(var(--mico-size-unit) * 56); /* 224px - Huge section spacing */
  --mico-size-240: calc(var(--mico-size-unit) * 60); /* 240px - Maximum spacing */
  --mico-size-256: calc(var(--mico-size-unit) * 64); /* 256px - Maximum spacing */
  --mico-size-260: calc(var(--mico-size-unit) * 68); /* 260px - */
  --mico-size-264: calc(var(--mico-size-unit) * 72); /* 264px - */
  --mico-size-268: calc(var(--mico-size-unit) * 76); /* 268px - */
  --mico-size-272: calc(var(--mico-size-unit) * 80); /* 272px - */
  --mico-size-276: calc(var(--mico-size-unit) * 84); /* 276px - */
  --mico-size-280: calc(var(--mico-size-unit) * 88); /* 280px - */
  --mico-size-284: calc(var(--mico-size-unit) * 92); /* 284px - */
  --mico-size-288: calc(var(--mico-size-unit) * 96); /* 288px - */
  --mico-size-292: calc(var(--mico-size-unit) * 100); /* 292px - */
  --mico-size-296: calc(var(--mico-size-unit) * 104); /* 296px - */
  --mico-size-300: calc(var(--mico-size-unit) * 108); /* 300px - */
  --mico-size-304: calc(var(--mico-size-unit) * 112); /* 304px - */
  --mico-size-308: calc(var(--mico-size-unit) * 116); /* 308px - */
  --mico-size-312: calc(var(--mico-size-unit) * 120); /* 312px - */
  --mico-size-316: calc(var(--mico-size-unit) * 124); /* 316px - */
  --mico-size-320: calc(var(--mico-size-unit) * 128); /* 320px - */
  --mico-size-324: calc(var(--mico-size-unit) * 132); /* 324px - */
  --mico-size-328: calc(var(--mico-size-unit) * 136); /* 328px - */
  --mico-size-332: calc(var(--mico-size-unit) * 140); /* 332px - */
  --mico-size-336: calc(var(--mico-size-unit) * 144); /* 336px - */
  --mico-size-340: calc(var(--mico-size-unit) * 148); /* 340px - */
  --mico-size-344: calc(var(--mico-size-unit) * 152); /* 344px - */
  --mico-size-348: calc(var(--mico-size-unit) * 156); /* 348px - */
  --mico-size-352: calc(var(--mico-size-unit) * 160); /* 352px - */
  --mico-size-356: calc(var(--mico-size-unit) * 164); /* 356px - */
  --mico-size-360: calc(var(--mico-size-unit) * 168); /* 360px - */
  --mico-size-364: calc(var(--mico-size-unit) * 172); /* 364px - */
  --mico-size-368: calc(var(--mico-size-unit) * 176); /* 368px - */
  --mico-size-372: calc(var(--mico-size-unit) * 180); /* 372px - */
  --mico-size-376: calc(var(--mico-size-unit) * 184); /* 376px - */
  --mico-size-380: calc(var(--mico-size-unit) * 188); /* 380px - */
  --mico-size-384: calc(var(--mico-size-unit) * 192); /* 384px - */
  --mico-size-388: calc(var(--mico-size-unit) * 196); /* 388px - */
  --mico-size-392: calc(var(--mico-size-unit) * 200); /* 392px - */
  --mico-size-396: calc(var(--mico-size-unit) * 204); /* 396px - */
  --mico-size-400: calc(var(--mico-size-unit) * 208); /* 400px - */



  /**
   * Main & Section Fluid Spacing Gutter
   *
   * Responsive spacing that adapts to viewport size.
   * Uses clamp() to create spacing that scales between a minimum and maximum value.
   *
   * Format: clamp(min-size, viewport-based-size, max-size)
   */
  --mico-size-fluid-xs: clamp(var(--mico-size-16), 3vw, var(--mico-size-32));
  --mico-size-fluid-sm: clamp(var(--mico-size-16), 4vw, var(--mico-size-56));
  --mico-size-fluid-md: clamp(var(--mico-size-16), 6vw, var(--mico-size-80));
  --mico-size-fluid-lg: clamp(var(--mico-size-16), 8vw, var(--mico-size-100));
  --mico-size-fluid-xl: clamp(var(--mico-size-16), 10vw, var(--mico-size-128));
  --mico-size-fluid-2xl: clamp(var(--mico-size-16), 12vw, var(--mico-size-192));


  /* ====================================================================== */
  /* BORDER SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Border Radius
   *
   * Controls the roundness of element corners.
   * Consistent border radius values create a cohesive design language.
   */
  --mico-radius-none: 0;            /* No rounding */
  --mico-radius-xs: 1px;            /* Barely visible rounding */
  --mico-radius-sm: 2px;            /* Subtle rounding */
  --mico-radius-md: 4px;            /* Standard rounding */
  --mico-radius-lg: 8px;            /* Prominent rounding */
  --mico-radius-xl: 12px;           /* Very rounded corners */
  --mico-radius-2xl: 16px;          /* Extra rounded corners */
  --mico-radius-full: 9999px;       /* Fully rounded (circles/pills) */

  /**
   * Border Styles
   *
   * Standard CSS border styles for consistent usage.
   * These variables make it easier to maintain consistent border styles.
   */
   
  --mico-border-none: none;
  --mico-border-solid: solid;
  --mico-border-dashed: dashed;
  --mico-border-dotted: dotted;
  --mico-border-double: double;
  --mico-border-groove: groove;
  --mico-border-ridge: ridge;
  --mico-border-inset: inset;
  --mico-border-outset: outset;

  /**
   * Border Widths
   *
   * Standard border thickness values.
   * These follow the same scale pattern as other properties.
   */
  --mico-border-width-0: 0px;       /* No border */
  --mico-border-width-1: 1px;       /* Thin border (standard) */
  --mico-border-width-2: 2px;       /* Medium border */
  --mico-border-width-4: 4px;       /* Thick border */
  --mico-border-width-8: 8px;       /* Very thick border */
  

  /**
   * Outline Properties
   *
   * These variables define outline styles for accessibility and focus states.
   */
  --mico-outline-width-0: 0px;      /* No outline */
  --mico-outline-width-1: 1px;      /* Thin outline */
  --mico-outline-width-2: 2px;      /* Medium outline */
  --mico-outline-width-4: 4px;      /* Thick outline */
  --mico-outline-width-8: 8px;      /* Very thick outline */

  --mico-outline-style-none: none;  /* No outline */
  --mico-outline-style-solid: solid; /* Solid outline */
  --mico-outline-style-dashed: dashed; /* Dashed outline */
  --mico-outline-style-dotted: dotted; /* Dotted outline */
  --mico-outline-style-double: double; /* Double outline */

  --mico-outline-offset-0: 0px;     /* No offset */
  --mico-outline-offset-1: 1px;     /* Small offset */
  --mico-outline-offset-2: 2px;     /* Medium offset */
  --mico-outline-offset-4: 4px;     /* Large offset */
  --mico-outline-offset-8: 8px;     /* Extra large offset */

  /**
   * Divide Properties
   *
   * These variables define border styles for dividing child elements.
   */
  --mico-divide-width-0: 0px;       /* No divide border */
  --mico-divide-width-1: 1px;       /* Thin divide border */
  --mico-divide-width-2: 2px;       /* Medium divide border */
  --mico-divide-width-4: 4px;       /* Thick divide border */
  --mico-divide-width-8: 8px;       /* Very thick divide border */

  /* ====================================================================== */
  /* SHADOW SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Box Shadows
   *
   * Creates depth and elevation in the interface.
   * Different shadow intensities represent different elevation levels.
   *
   * Light mode shadows use black with opacity for a subtle effect.
   * Dark mode shadows use white with opacity for a subtle effect.
   */
--mico-shadow-primary: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-primary) h calc(s * 0.55) l);
--mico-shadow-secondary: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-secondary) h calc(s * 0.55) l);
--mico-shadow-accent: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-accent) h calc(s * 0.55) l);
--mico-shadow-success: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-success) h calc(s * 0.55) l);
--mico-shadow-error: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-error) h calc(s * 0.55) l);
--mico-shadow-warning: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-warning) h calc(s * 0.55) l);
--mico-shadow-info: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-info) h calc(s * 0.55) l);

--mico-shadow-hover-primary: 0px 4.5px 4px -3.8px hsl(from var(--mico-color-primary) h calc(s * 0.9) l) !important; 
--mico-shadow-hover-secondary: 0px 4.5px 4px -3.8px hsl(from var(--mico-color-secondary) h calc(s * 0.9) l) !important; 
--mico-shadow-hover-accent: 0px 4.5px 4px -3.8px hsl(from var(--mico-color-accent) h calc(s * 0.9) l) !important; 
  
--mico-shadow-focus-primary: 0px 4.6px 4px -3.8px hsl(from var(--mico-color-primary) h calc(s * 0.8) l) !important; 
--mico-shadow-focus-secondary: 0px 4.6px 4px -3.8px hsl(from var(--mico-color-secondary) h calc(s * 0.8) l) !important; 
--mico-shadow-focus-accent: 0px 4.6px 4px -3.8px hsl(from var(--mico-color-accent) h calc(s * 0.8) l) !important; 
  
--mico-shadow-active-primary: 0px 4.8px 4px -2.8px hsl(from var(--mico-color-primary) h calc(s * 0.7) l) !important; 
--mico-shadow-active-secondary: 0px 4.8px 4px -2.8px hsl(from var(--mico-color-secondary) h calc(s * 0.7) l) !important; 
--mico-shadow-active-accent: 0px 4.8px 4px -2.8px hsl(from var(--mico-color-accent) h calc(s * 0.7) l) !important; 

  
   /* Light Mode Shadows */
  --mico-shadow-none-light: var(--mico-value-none);
  --mico-shadow-xs-light: 0 2px 2px rgba(0, 0, 0, 0.05);  /* Subtle shadow */
  --mico-shadow-sm-light: 0 2px 2px rgba(0, 0, 0, 0.05);  /* Subtle shadow */
  --mico-shadow-md-light: 0 4px 6px rgba(0, 0, 0, 0.1);   /* Medium shadow */
  --mico-shadow-lg-light: 0 10px 15px rgba(0, 0, 0, 0.1); /* Large shadow */
  --mico-shadow-xl-light: 0 20px 25px rgba(0, 0, 0, 0.15); /* Extra large shadow */
  --mico-shadow-2xl-light: ;
  --mico-shadow-3xl-light: ;



  /* Inset Shadows (for pressed/inset effects) */
  --mico-shadow-inset-none-light: inset var(--mico-value-none);
  --mico-shadow-inset-xs-light: inset 0 2px 2px rgba(0, 0, 0, 0.05);  /* Subtle shadow */
  --mico-shadow-inset-sm-light: inset 0 1px 2px rgba(0, 0, 0, 0.05);
  --mico-shadow-inset-md-light: inset 0 4px 6px rgba(0, 0, 0, 0.1);
  --mico-shadow-inset-lg-light: inset 0 20px 25px rgba(0, 0, 0, 0.15); /* Extra large shadow */
  --mico-shadow-inset-xl-light: inset 0 20px 25px rgba(0, 0, 0, 0.20); /* Extra large shadow */
  --mico-shadow-inset-2xl-light: inset ;
  --mico-shadow-inset-3xl-light: inset ;

  /* Focus Shadow for accessibility */
  --mico-shadow-focus: 0 0 0 3px rgba(66, 153, 225, 0.5);

  /**
   * Media Queries for Adaptive Shadows
   *
   * These media queries automatically adjust shadow styles based on user preferences.
   * - Dark mode: Uses lighter shadows on dark backgrounds
   * - High contrast mode: Uses more visible focus indicators
   */
  @media (prefers-color-scheme: dark) {
  /* Dark Mode Shadows */
  --mico-shadow-none-dark: var(--mico-value-none);
  --mico-shadow-xs-dark: 0 1px 2px rgba(255, 255, 255, 0.05);
  --mico-shadow-sm-dark: 0 1px 2px rgba(255, 255, 255, 0.05);
  --mico-shadow-md-dark: 0 4px 6px rgba(255, 255, 255, 0.1);
  --mico-shadow-lg-dark: 0 10px 15px rgba(255, 255, 255, 0.1);
  --mico-shadow-xl-dark: 0 20px 25px rgba(255, 255, 255, 0.15);
  --mico-shadow-2xl-light: ;
  --mico-shadow-3xl-light: ;

  --mico-shadow-inset-none-dark: inset var(--mico-value-none);
  --mico-shadow-inset-xs-dark: inset 0 1px 2px rgba(255, 255, 255, 0.05);
  --mico-shadow-inset-sm-dark: inset 0 1px 2px rgba(255, 255, 255, 0.05);
  --mico-shadow-inset-md-dark: inset 0 4px 6px rgba(255, 255, 255, 0.1);
  --mico-shadow-inset-lg-light: inset 0 20px 25px rgba(0, 0, 0, 0.15); /* Still shadow for light mode, change them to match dark mode */
  --mico-shadow-inset-xl-light: inset 0 20px 25px rgba(0, 0, 0, 0.20); /* Extra large shadow */
  --mico-shadow-inset-2xl-light: inset ;
  --mico-shadow-inset-3xl-light: inset ;
  --mico-shadow-focus: 0 0 0 3px rgba(191, 219, 254, 0.6);
  }

  @media (prefers-contrast: high) {
    --mico-shadow-focus: 0 0 0 4px rgba(0, 0, 0, 1);
    --mico-shadow-sm-contrast: 0 0 0 1px currentColor;
    --mico-shadow-md-contrast: 0 0 0 2px currentColor;
    --mico-shadow-lg-contrast: 0 0 0 3px currentColor;
    --mico-shadow-xl-contrast: 0 0 0 4px currentColor;
  }

  /* ====================================================================== */
  /* LAYOUT SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Layout Properties
   *
   * These variables define common layout values for consistent usage.
   * Using variables for these properties ensures consistency across the codebase.
   */

  /**
   * Position Properties
   *
   * These variables define CSS position values for consistent usage.
   * Position determines how an element is positioned in the document flow.
   */
  --mico-position-static: static;     /* Default positioning in document flow */
  --mico-position-relative: relative; /* Positioned relative to normal position */
  --mico-position-absolute: absolute; /* Positioned relative to nearest positioned ancestor */
  --mico-position-fixed: fixed;       /* Positioned relative to viewport */
  --mico-position-sticky: sticky;     /* Positioned based on scroll position */

  /**
   * Display Properties
   *
   * These variables define CSS display values for consistent usage.
   * Display determines how an element is rendered in the layout.
   */
  --mico-display-block: block;           /* Element generates a block box */
  --mico-display-inline: inline;         /* Element generates an inline box */
  --mico-display-inline-block: inline-block; /* Inline-level block container */
  --mico-display-flex: flex;             /* Flexible box layout */
  --mico-display-inline-flex: inline-flex; /* Inline-level flex container */
  --mico-display-grid: grid;             /* Grid layout */
  --mico-display-none: none;             /* Element is not displayed */

  /**
   * Box Model Properties
   *
   * These variables define box model behavior for consistent usage.
   */
  --mico-box-sizing-border: border-box;  /* Width/height includes padding and border */
  --mico-box-sizing-content: content-box; /* Width/height excludes padding and border */
  --mico-box-decoration-slice: slice;    /* Background doesn't extend across fragments */
  --mico-box-decoration-clone: clone;    /* Background extends across fragments */

  /**
   * Overflow Properties
   *
   * These variables define how content that overflows the element's box is handled.
   */
  --mico-overflow-auto: auto;           /* Add scrollbars when needed */
  --mico-overflow-hidden: hidden;       /* Clip overflow content */
  --mico-overflow-visible: visible;     /* Content not clipped, may overflow */
  --mico-overflow-scroll: scroll;       /* Always show scrollbars */
  --mico-overscroll-auto: auto;         /* Default overscroll behavior */
  --mico-overscroll-contain: contain;   /* Prevent scroll chaining */
  --mico-overscroll-none: none;         /* Prevent overscroll effects */

  /**
   * Aspect Ratio Properties
   *
   * These variables define common aspect ratios for responsive elements.
   * Useful for maintaining proportional dimensions for images, videos, etc.
   */
  --mico-aspect-ratio-square: 1 / 1;        /* 1:1 ratio (square) */
  --mico-aspect-ratio-video: 16 / 9;        /* 16:9 ratio (standard video) */
  --mico-aspect-ratio-portrait: 3 / 4;      /* 3:4 ratio (portrait) */
  --mico-aspect-ratio-landscape: 4 / 3;     /* 4:3 ratio (landscape) */
  --mico-aspect-ratio-widescreen: 21 / 9;   /* 21:9 ratio (ultrawide) */
  --mico-aspect-ratio-golden: 1.618 / 1;    /* Golden ratio (aesthetically pleasing) */

  /**
   * Float and Clear Properties
   *
   * These variables define float and clear values for consistent usage.
   * Float allows elements to be placed to the left or right of their container.
   */
  --mico-float-left: left;              /* Float element to the left */
  --mico-float-right: right;            /* Float element to the right */
  --mico-float-none: none;              /* Do not float element */
  --mico-clear-left: left;              /* Clear left floats */
  --mico-clear-right: right;            /* Clear right floats */
  --mico-clear-both: both;              /* Clear both left and right floats */

  /**
   * Object Fit Properties
   *
   * These variables define how replaced elements (like images) should be resized.
   */
  --mico-object-fit-contain: contain;    /* Preserve aspect ratio, fit within box */
  --mico-object-fit-cover: cover;        /* Fill box, may crop image */
  --mico-object-fit-fill: fill;          /* Stretch to fill box */
  --mico-object-fit-scale-down: scale-down; /* Smaller of contain or none */
  --mico-object-position-center: center; /* Center the object within its box */

  /**
   * Visibility and Isolation Properties
   *
   * These variables define visibility and stacking context behavior.
   */
  --mico-visibility-visible: visible;    /* Element is visible */
  --mico-visibility-hidden: hidden;      /* Element is hidden but takes up space */
  --mico-visibility-collapse: collapse;  /* Element is hidden (for table rows/columns) */
  --mico-isolation-isolate: isolate;     /* Create new stacking context */
  --mico-isolation-auto: auto;           /* Default stacking context behavior */

  /**
   * Positioning Properties
   *
   * These variables define common inset values for positioned elements.
   */
  --mico-inset-0: 0;                     /* No offset from container edges */
  --mico-inset-auto: auto;               /* Automatic positioning */

  /* ====================================================================== */
  /* FLEXBOX SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Flexbox Properties
   *
   * These variables define common flexbox values for consistent usage.
   * Flexbox is a one-dimensional layout method for arranging items.
   */

  /* Flex Direction */
  --mico-flex-row: row;               /* Items arranged in a row */
  --mico-flex-row-reverse: row-reverse; /* Items arranged in a row, reversed */
  --mico-flex-col: column;            /* Items arranged in a column */
  --mico-flex-col-reverse: column-reverse; /* Items arranged in a column, reversed */

  /* Flex Wrap */
  --mico-flex-wrap: wrap;             /* Items wrap to multiple lines */
  --mico-flex-nowrap: nowrap;         /* Items forced into a single line */
  --mico-flex-wrap-reverse: wrap-reverse; /* Items wrap to multiple lines, reversed */

  /* Justify Content (main axis) */
  --mico-justify-start: flex-start;   /* Items packed at start of container */
  --mico-justify-end: flex-end;       /* Items packed at end of container */
  --mico-justify-center: center;      /* Items centered in container */
  --mico-justify-between: space-between; /* Items evenly distributed with space between */
  --mico-justify-around: space-around; /* Items evenly distributed with space around */
  --mico-justify-evenly: space-evenly; /* Items evenly distributed with equal space */

  /* Align Items (cross axis) */
  --mico-items-start: flex-start;     /* Items aligned at start of cross axis */
  --mico-items-end: flex-end;         /* Items aligned at end of cross axis */
  --mico-items-center: center;        /* Items centered on cross axis */
  --mico-items-baseline: baseline;    /* Items aligned by text baseline */
  --mico-items-stretch: stretch;      /* Items stretched to fill container */

  /* ====================================================================== */
  /* GRID SYSTEM                                                            */
  /* ====================================================================== */
  /**
   * Grid Auto Properties
   *
   * These variables define how grid items are automatically placed and sized.
   * Auto-fit and auto-fill create responsive layouts without media queries.
   */
  --mico-grid-auto-fit: auto-fit;    /* Expands items to fill available space */
  --mico-grid-auto-fill: auto-fill;  /* Creates as many tracks as possible */

  /**
   * Grid Placement Properties
   *
   * These variables define how grid items are placed within the grid container.
   * They control both individual items and groups of items.
   */
  --mico-place-items-start: start;    /* Items placed at start of their area */
  --mico-place-items-end: end;        /* Items placed at end of their area */
  --mico-place-items-center: center;  /* Items placed at center of their area */
  --mico-place-items-stretch: stretch; /* Items stretched to fill their area */

  --mico-place-content-start: start;    /* Content placed at start of grid */
  --mico-place-content-end: end;        /* Content placed at end of grid */
  --mico-place-content-center: center;  /* Content placed at center of grid */
  --mico-place-content-stretch: stretch; /* Content stretched to fill grid */
  --mico-place-content-around: space-around; /* Content evenly distributed with space around */
  --mico-place-content-between: space-between; /* Content evenly distributed with space between */
  --mico-place-content-evenly: space-evenly;   /* Content evenly distributed with equal space */

  /**
   * Grid Properties
   *
   * These variables define common grid layout values for consistent usage.
   * CSS Grid is a two-dimensional layout system for complex layouts.
   */

  /* Grid Configuration */
  --mico-grid-column-count: 12;       /* Default number of columns */
  --mico-grid-min-column-width: 200px; /* Minimum column width for responsive grids */
  --mico-grid-row-count: 1;           /* Default number of rows */
  --mico-grid-min-row-height: 100px;  /* Minimum row height for responsive grids */

  /* Grid Item Placement */
  --mico-column-span: 1;              /* Default column span for grid items */
  --mico-row-span: 1;                 /* Default row span for grid items */
  --mico-min-column-width: 0;         /* Minimum width for auto columns */
  --mico-max-column-width: 1fr;       /* Maximum width for auto columns */
  --mico-min-row-height: 0;           /* Minimum height for auto rows */
  --mico-max-row-height: 1fr;         /* Maximum height for auto rows */

  /* Grid Templates */
  --mico-grid-cols: repeat(var(--mico-grid-column-count, 12), minmax(0, 1fr)); /* Equal columns */
  --mico-grid-cols-auto-fit: repeat(auto-fit, minmax(var(--mico-grid-min-column-width, 200px), 1fr)); /* Responsive columns */
  --mico-grid-rows: repeat(var(--mico-grid-row-count, 1), minmax(0, 1fr)); /* Equal rows */

  /* Grid Item Placement */
  --mico-col-span: span var(--mico-column-span, 1); /* Column span for grid items */
  --mico-row-span: span var(--mico-row-span, 1);    /* Row span for grid items */

  /* Grid Flow */
  --mico-grid-flow-row: row;          /* Grid auto-placement by row */
  --mico-grid-flow-col: column;       /* Grid auto-placement by column */
  --mico-grid-flow-dense: dense;      /* Dense packing algorithm */

  /* Grid Auto Columns and Rows */
  --mico-auto-cols: minmax(var(--mico-min-column-width, 0), var(--mico-max-column-width, 1fr));
  --mico-auto-rows: minmax(var(--mico-min-row-height, 0), var(--mico-max-row-height, 1fr));

  /* Grid Gap */
  --mico-gap-xs: var(--mico-size-4);  /* Extra small gap */
  --mico-gap-sm: var(--mico-size-8);  /* Small gap */
  --mico-gap-md: var(--mico-size-16); /* Medium gap (default) */
  --mico-gap-lg: var(--mico-size-24); /* Large gap */
  --mico-gap-xl: var(--mico-size-32); /* Extra large gap */

  /* ====================================================================== */
  /* BACKGROUND SYSTEM                                                       */
  /* ====================================================================== */

  /**
   * Background Properties
   *
   * These variables define common background property values for consistent usage.
   * Background properties control how backgrounds are displayed.
   */

  /* Background Repeat */
  --mico-bg-none: none;               /* No background image */
  --mico-bg-repeat: repeat;           /* Repeat in both directions */
  --mico-bg-no-repeat: no-repeat;     /* No repetition */
  --mico-bg-repeat-x: repeat-x;       /* Repeat horizontally only */
  --mico-bg-repeat-y: repeat-y;       /* Repeat vertically only */

  /* Background Attachment */
  --mico-bg-fixed: fixed;             /* Fixed to viewport */
  --mico-bg-local: local;             /* Scrolls with content */
  --mico-bg-scroll: scroll;           /* Scrolls with element */

  /* Background Clip */
  --mico-bg-clip-border: border-box;  /* Extend to outer border edge */
  --mico-bg-clip-padding: padding-box; /* Extend to outer padding edge */
  --mico-bg-clip-content: content-box; /* Extend to content edge */

  /* ====================================================================== */
  /* FILTER SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Filter Properties
   *
   * These variables define CSS filter effects for visual manipulation.
   * Filters can be combined to create complex visual effects.
   */
  --mico-filter-blur: blur(8px);           /* Blurs the element */
  --mico-filter-brightness: brightness(1.5); /* Adjusts brightness */
  --mico-filter-contrast: contrast(1.2);    /* Adjusts contrast */
  --mico-filter-grayscale: grayscale(100%); /* Converts to grayscale */
  --mico-filter-hue-rotate: hue-rotate(90deg); /* Shifts colors */
  --mico-filter-invert: invert(100%);      /* Inverts colors */
  --mico-filter-saturate: saturate(2);     /* Adjusts color saturation */
  --mico-filter-sepia: sepia(100%);        /* Applies sepia tone */

  /* ====================================================================== */
  /* OPACITY SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Opacity Properties
   *
   * These variables define standard opacity values for consistent usage.
   * Opacity controls the transparency of elements.
   */
  --mico-opacity-0p: 0;    
  --mico-opacity-10p: 0.10;               
  --mico-opacity-20p: 0.20; 
  --mico-opacity-30p: 0.30; 
  --mico-opacity-40p: 0.40; 
  --mico-opacity-50p: 0.5;    
  --mico-opacity-60p: 0.60;    
  --mico-opacity-70p: 0.70;  
  --mico-opacity-80p: 0.80;  
  --mico-opacity-90p: 0.90;  
  --mico-opacity-100p: 1;      

  /* ====================================================================== */
  /* TRANSFORM SYSTEM                                                       */
  /* ====================================================================== */

  /**
   * Transform Properties
   *
   * These variables define common transform functions for consistent usage.
   * Transforms allow elements to be visually manipulated in 2D or 3D space.
   */

  /* Scale Transforms */
  --mico-scale-100: scale(1);         /* Original size (no scaling) */
  --mico-scale-75: scale(0.75);       /* 75% of original size */
  --mico-scale-50: scale(0.5);        /* 50% of original size */

  /* Rotation Transforms */
  --mico-rotate-45: rotate(45deg);    /* 45-degree rotation */
  --mico-rotate-90: rotate(90deg);    /* 90-degree rotation */

  /* Translation Transforms */
  --mico-translate-x-full: translateX(100%); /* Move 100% right */
  --mico-translate-y-full: translateY(100%); /* Move 100% down */

  /* ====================================================================== */
  /* TABLE SYSTEM                                                           */
  /* ====================================================================== */

  /**
   * Table Properties
   *
   * These variables define table layout algorithms for consistent usage.
   * Table layout affects how tables calculate column widths.
   */
  --mico-table-auto: auto;            /* Automatic table layout algorithm */
  --mico-table-fixed: fixed;          /* Fixed table layout algorithm */

  /* ====================================================================== */
  /* SVG SYSTEM                                                             */
  /* ====================================================================== */

  /**
   * SVG Properties
   *
   * These variables define common SVG property values for consistent usage.
   * These help maintain consistent styling between HTML and SVG elements.
   */
  --mico-fill-current: currentColor;  /* Use current text color for fill */
  --mico-stroke-current: currentColor; /* Use current text color for stroke */

  /* ====================================================================== */
  /* BUTTON SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Button Size Variables
   *
   * These variables define consistent button sizing across the framework.
   */
  --mico-btn-padding-xs: var(--mico-size-8) var(--mico-size-12);       /* Extra small button padding */
  --mico-btn-padding-sm: var(--mico-size-12) var(--mico-size-16);      /* Small button padding */
  --mico-btn-padding-md: var(--mico-size-16) var(--mico-size-24);      /* Medium button padding (default) */
  --mico-btn-padding-lg: var(--mico-size-20) var(--mico-size-32);      /* Large button padding */
  --mico-btn-padding-xl: var(--mico-size-24) var(--mico-size-40);      /* Extra large button padding */

  --mico-btn-font-size-xs: var(--mico-fs-xs);                          /* Extra small button font size */
  --mico-btn-font-size-sm: var(--mico-fs-sm);                          /* Small button font size */
  --mico-btn-font-size-md: var(--mico-fs-md);                          /* Medium button font size (default) */
  --mico-btn-font-size-lg: var(--mico-fs-lg);                          /* Large button font size */
  --mico-btn-font-size-xl: var(--mico-fs-xl);                          /* Extra large button font size */

  /**
   * Button Border Radius Variables
   *
   * These variables define button border radius options.
   */
  --mico-btn-radius-square: var(--mico-radius-none);                    /* Square buttons (no radius) */
  --mico-btn-radius-sm: var(--mico-radius-sm);                         /* Small radius */
  --mico-btn-radius-md: var(--mico-radius-md);                         /* Medium radius (default) */
  --mico-btn-radius-lg: var(--mico-radius-lg);                         /* Large radius */
  --mico-btn-radius-pill: var(--mico-radius-full);                     /* Pill-shaped buttons */
  --mico-btn-radius-circle: var(--mico-radius-full);                   /* Circular buttons */

  /**
   * Button Shadow Variables
   *
   * These variables define button shadow options for depth and elevation.
   */
  --mico-btn-shadow-none: none;                                         /* No shadow */
  --mico-btn-shadow-sm: var(--mico-shadow-sm);                         /* Small shadow */
  --mico-btn-shadow-md: var(--mico-shadow-md);                         /* Medium shadow (default) */
  --mico-btn-shadow-lg: var(--mico-shadow-lg);                         /* Large shadow */

  /**
   * Button Icon Variables
   *
   * These variables define spacing and sizing for buttons with icons.
   */
  --mico-btn-icon-gap: var(--mico-size-8);                             /* Gap between icon and text */
  --mico-btn-icon-only-size: var(--mico-size-40);                      /* Size for icon-only buttons */

  /* ====================================================================== */
  /* CURSOR SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Cursor Properties
   *
   * These variables define cursor styles for different interactive states.
   * Cursors provide visual feedback about what the user can do.
   */
  --mico-cursor-auto: auto;           /* Browser default cursor */
  --mico-cursor-default: default;     /* Default cursor (arrow) */
  --mico-cursor-pointer: pointer;     /* Pointing hand (for links) */
  --mico-cursor-wait: wait;           /* Waiting (hourglass) */
  --mico-cursor-text: text;           /* Text selection (I-beam) */
  --mico-cursor-move: move;           /* Movement indicator */
  --mico-cursor-not-allowed: not-allowed; /* Forbidden action */
  --mico-cursor-grab: grab;           /* Grabbable element */
  --mico-cursor-grabbing: grabbing;   /* Element being grabbed */
  --mico-cursor-help: help;           /* Help cursor */

  /* ====================================================================== */
  /* MISCELLANEOUS UTILITIES SYSTEM                                         */
  /* ====================================================================== */

  /**
   * Appearance Variables
   *
   * These variables control the appearance property for form elements.
   */
  --mico-appearance-none: none;       /* Remove default browser styling */
  --mico-appearance-auto: auto;       /* Use default browser styling */

  /**
   * Pointer Events Variables
   *
   * These variables control pointer event behavior.
   */
  --mico-pointer-events-none: none;   /* Element cannot be target of pointer events */
  --mico-pointer-events-auto: auto;   /* Element can be target of pointer events */

  /**
   * Will Change Variables
   *
   * These variables provide performance hints to the browser.
   * Use with caution as they can consume more resources if overused.
   */
  --mico-will-change-auto: auto;      /* Browser decides what to optimize */
  --mico-will-change-transform: transform; /* Optimize for transform changes */
  --mico-will-change-opacity: opacity; /* Optimize for opacity changes */
  --mico-will-change-scroll: scroll-position; /* Optimize for scroll changes */

  /**
   * Bleed Utility Variables
   *
   * These variables control full-bleed and column-bleed effects.
   */
  --mico-bleed-offset-sm: 10vw;       /* Small bleed offset */
  --mico-bleed-offset-md: 20vw;       /* Medium bleed offset (default) */
  --mico-bleed-offset-lg: 30vw;       /* Large bleed offset */

  /**
   * Mask Variables
   *
   * These variables define CSS mask gradients for fade effects.
   */
  --mico-mask-fade-to-top: linear-gradient(to top, black 50%, transparent 100%);
  --mico-mask-fade-to-bottom: linear-gradient(to bottom, black 50%, transparent 100%);
  --mico-mask-fade-to-left: linear-gradient(to left, black 50%, transparent 100%);
  --mico-mask-fade-to-right: linear-gradient(to right, black 50%, transparent 100%);

  /* Fade intensity variations */
  --mico-mask-fade-short: linear-gradient(to bottom, black 80%, transparent 100%);
  --mico-mask-fade-long: linear-gradient(to bottom, black 20%, transparent 100%);

  /* ====================================================================== */
  /* SIZING SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Width and Height Properties
   *
   * These variables define common sizing values for consistent usage.
   * Consistent sizing helps maintain a cohesive layout.
   */

  /* Content-based Sizing */
  --mico-fit-content: fit-content;    /* Size based on content with constraints */
  --mico-min-content: min-content;    /* Smallest size that fits content */
  --mico-max-content: max-content;    /* Largest size needed for content */

  /* Percentage-based Sizing */
  --mico-width-full: 100%;            /* Full width of container */
  --mico-height-full: 100%;           /* Full height of container */
  --mico-width-half: 50%;             /* Half width of container */
  --mico-height-half: 50%;            /* Half height of container */
  --mico-width-quarter: 25%;           /* Quarter width of container */
  --mico-height-quarter: 25%;         /* Quarter height of container */
  --mico-width-third: 33.33%;         /* Third width of container */
  --mico-height-third: 33.33%;       /* Third height of container */

  /* Viewport-based Sizing */
  --mico-width-screen: 100vw;         /* Full viewport width */
  --mico-height-screen: 100vh;        /* Full viewport height */

  /* Min/Max Constraints */
  --mico-min-width-0: 0;              /* No minimum width */
  --mico-min-height-0: 0;             /* No minimum height */
  --mico-max-width-full: 100%;        /* Maximum width of container */
  --mico-max-height-full: 100%;       /* Maximum height of container */

  /* ====================================================================== */
  /* ALIGNMENT SYSTEM                                                       */
  /* ====================================================================== */

  /**
   * Vertical Alignment Properties
   *
   * These variables define vertical alignment values for consistent usage.
   * Vertical alignment controls how inline or table-cell elements are aligned.
   */

  /* Basic Vertical Alignment */
  --mico-align-baseline: baseline;    /* Align to text baseline */
  --mico-align-top: top;              /* Align to top */
  --mico-align-middle: middle;        /* Align to middle */
  --mico-align-bottom: bottom;        /* Align to bottom */
  --mico-align-text-top: text-top;    /* Align to top of text */
  --mico-align-text-bottom: text-bottom; /* Align to bottom of text */

  /* Extended Vertical Alignment */
  --mico-vertical-align-sub: sub;     /* Subscript alignment */
  --mico-vertical-align-super: super; /* Superscript alignment */

  /* ====================================================================== */
  /* ANIMATION SYSTEM                                                       */
  /* ====================================================================== */

  /**
   * Animation Duration Variables
   *
   * These variables define animation durations for consistent timing across the framework.
   * Used by both transitions and animations for unified motion design.
   */
  --mico-duration-xs: 0.2s;          /* Extra fast animations */
  --mico-duration-sm: 0.5s;          /* Fast animations */
  --mico-duration-md: 0.8s;          /* Standard animations */
  --mico-duration-lg: 1.2s;          /* Slow animations */
  --mico-duration-xl: 2s;            /* Very slow animations */

  /**
   * Animation Delay Variables
   *
   * These variables define animation delays for staggered effects.
   */
  --mico-delay-xs: 0.1s;             /* Minimal delay */
  --mico-delay-sm: 0.2s;             /* Small delay */
  --mico-delay-md: 0.4s;             /* Medium delay */
  --mico-delay-lg: 0.6s;             /* Large delay */
  --mico-delay-xl: 0.8s;             /* Extra large delay */

  /**
   * Animation Engine Variables
   *
   * These variables control the default behavior of the animation engine.
   */
  --mico-anim-default-duration: var(--mico-duration-md);        /* Default animation duration */
  --mico-anim-default-timing: ease-out;                         /* Default timing function */
  --mico-anim-default-fill-mode: both;                          /* Default fill mode */

  /**
   * Interactive Animation Variables
   *
   * These variables control interactive elements like ripples and typewriter effects.
   */
  --mico-ripple-bg-default: rgba(255, 255, 255, 0.3);          /* Default ripple background */
  --mico-ripple-bg-dark: rgba(0, 0, 0, 0.2);                   /* Dark ripple background */
  --mico-typewriter-cursor-color: currentColor;                 /* Typewriter cursor color */

  /**
   * Animation Properties
   *
   * These variables define common animation presets for consistent usage.
   * Animations create movement and visual interest in the interface.
   */
  --mico-animation-none: none;        /* No animation */
  --mico-animation-spin: spin 1s linear infinite; /* Spinning animation */
  --mico-animation-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite; /* Ping/pulse effect */
  --mico-animation-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; /* Subtle pulse */
  --mico-animation-bounce: bounce 1s infinite; /* Bouncing effect */

  /* ====================================================================== */
  /* EASING SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Easing Functions
   *
   * These variables define timing functions that control animation pacing.
   * Different easing functions create different feelings of motion.
   */

  /* Standard Easing Functions */
  --mico-ease: cubic-bezier(0.25, 0.1, 0.25, 1.0); /* Standard ease */
  --mico-ease-in: cubic-bezier(0.42, 0, 1.0, 1.0); /* Slow start, fast end */
  --mico-ease-out: cubic-bezier(0, 0, 0.58, 1.0); /* Fast start, slow end */
  --mico-ease-in-out: cubic-bezier(0.42, 0, 0.58, 1.0); /* Slow start and end */

  /* Expressive Easing Functions */
  --mico-ease-elastic: cubic-bezier(0.68, -0.55, 0.265, 1.55); /* Elastic/bouncy */
  --mico-ease-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bouncy end */
  --mico-ease-back: cubic-bezier(0.68, -0.55, 0.265, 1.55); /* Slight overshoot */

  /* Physics-inspired Easing */
  --mico-ease-spring: cubic-bezier(0.5, 0.1, 0.1, 1); /* Spring-like motion */
  --mico-ease-gravity: cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Gravity effect */
  --mico-ease-snappy: cubic-bezier(0.1, 0.9, 0.2, 1); /* Quick, snappy motion */

  /* ====================================================================== */
  /* TRANSITION SYSTEM                                                      */
  /* ====================================================================== */

  /**
   * Transition Properties
   *
   * These variables define common transition presets for consistent usage.
   * Transitions create smooth animations between property changes.
   */

  /* Transition Durations */
  --mico-transition-duration-fast: 150ms;  /* Fast transitions */
  --mico-transition-duration-normal: 300ms; /* Standard transitions */
  --mico-transition-duration-slow: 500ms;  /* Slow transitions */

  /* Common Transitions */
  --mico-transition-all: all .4s var(--mico-ease); /* All properties */
  --mico-transition-color: color .4s var(--mico-ease); /* Color only */
  --mico-transition-background: background .4s var(--mico-ease); /* Background only */
  --mico-transition-border: border .4s var(--mico-ease); /* Border only */
  --mico-transition-opacity: opacity .4s var(--mico-ease); /* Opacity only */
  --mico-transition-transform: transform .4s var(--mico-ease); /* Transform only */
  --mico-transition-box-shadow: box-shadow .4s var(--mico-ease); /* Shadow only */

  /* ====================================================================== */
  /* COLOR SYSTEM - OKLCH POWERED                                          */
  /* ====================================================================== */

  /**
   * OKLCH Color System
   *
   * This advanced color system uses OKLCH (Oklab Lightness Chroma Hue) color space
   * for superior color manipulation and perceptual uniformity. OKLCH provides:
   *
   * - Better perceptual uniformity than HSL
   * - More predictable lightness adjustments
   * - Wider color gamut support
   * - Superior color mixing and variations
   *
   * Base Colors: Each color serves as the foundation for 5 tints and 5 shades
   * Tints (lighter): 2xlight, 3xlight, 4xlight, 5xlight (progressively lighter)
   * Shades (darker): 2xdark, 3xdark, 4xdark, 5xdark (progressively darker)
   *
   * Browser Support: Modern browsers support oklch() and oklch(from var()) syntax.
   * Fallbacks are provided for older browsers.
   */

  /**
   * OKLCH Color System - Harmonious Tint & Shade Generation Controls
   *
   * This system creates beautiful, harmonious color variations that maintain the original
   * color's character while providing smooth, gradual progressions suitable for UI/UX design.
   * All variations follow WCAG accessibility guidelines and perceptual uniformity principles.
   *
   * DESIGN PRINCIPLES:
   * - Tints: Lighter variations that preserve color warmth and character
   * - Shades: Darker variations that enhance richness without becoming muddy
   * - Gradual progression: Each step creates a visually pleasing transition
   * - Color preservation: Original hue and feel maintained throughout variations
   * - UI suitability: Perfect for backgrounds, text, borders, and interactive states
   */

  /* ====================================================================== */
  /* TINT GENERATION CONTROLS (Lighter Variations)                         */
  /* ====================================================================== */

  /**
   * Lightness Controls for Tints
   * Progressive lightening that maintains color vibrancy and character
   */
  --mico-color-light-tint: calc(l + (1 - l) * 0.15);      /* Subtle lightening */
  --mico-color-light-2xtint: calc(l + (1 - l) * 0.25);    /* Gentle lightening */
  --mico-color-light-3xtint: calc(l + (1 - l) * 0.35);    /* Moderate lightening */
  --mico-color-light-4xtint: calc(l + (1 - l) * 0.45);    /* Noticeable lightening */
  --mico-color-light-5xtint: calc(l + (1 - l) * 0.55);    /* Significant lightening */
  --mico-color-light-6xtint: calc(l + (1 - l) * 0.65);    /* Strong lightening */
  --mico-color-light-7xtint: calc(l + (1 - l) * 0.75);    /* Very light */
  --mico-color-light-8xtint: calc(l + (1 - l) * 0.85);    /* Extremely light */

  /**
   * Chroma Controls for Tints
   * Gradual saturation reduction to prevent harsh, oversaturated light colors
   */
  --mico-color-chrome-tint: calc(c * 0.95);               /* Minimal desaturation */
  --mico-color-chrome-2xtint: calc(c * 0.88);             /* Slight desaturation */
  --mico-color-chrome-3xtint: calc(c * 0.78);             /* Gentle desaturation */
  --mico-color-chrome-4xtint: calc(c * 0.65);             /* Moderate desaturation */
  --mico-color-chrome-5xtint: calc(c * 0.50);             /* Noticeable desaturation */
  --mico-color-chrome-6xtint: calc(c * 0.38);             /* Strong desaturation */
  --mico-color-chrome-7xtint: calc(c * 0.25);             /* Very desaturated */
  --mico-color-chrome-8xtint: calc(c * 0.15);             /* Extremely desaturated */

  /**
   * Hue Controls for Tints
   * Subtle hue shifts to create warmer, more appealing light variations
   */
  --mico-color-hue-tint: calc(h + 2);                     /* Slight warm shift */
  --mico-color-hue-2xtint: calc(h + 3);                   /* Gentle warm shift */
  --mico-color-hue-3xtint: calc(h + 4);                   /* Moderate warm shift */
  --mico-color-hue-4xtint: calc(h + 5);                   /* Noticeable warm shift */
  --mico-color-hue-5xtint: calc(h + 6);                   /* Significant warm shift */
  --mico-color-hue-6xtint: calc(h + 7);                   /* Strong warm shift */
  --mico-color-hue-7xtint: calc(h + 8);                   /* Very warm shift */
  --mico-color-hue-8xtint: calc(h + 9);                   /* Extremely warm shift */

  /* ====================================================================== */
  /* SHADE GENERATION CONTROLS (Darker Variations)                         */
  /* ====================================================================== */

  /**
   * Lightness Controls for Shades
   * Progressive darkening that maintains color richness without becoming muddy
   */
  --mico-color-light-shade: calc(l * 0.92);               /* Subtle darkening */
  --mico-color-light-2xshade: calc(l * 0.82);             /* Gentle darkening */
  --mico-color-light-3xshade: calc(l * 0.70);             /* Moderate darkening */
  --mico-color-light-4xshade: calc(l * 0.58);             /* Noticeable darkening */
  --mico-color-light-5xshade: calc(l * 0.46);             /* Significant darkening */
  --mico-color-light-6xshade: calc(l * 0.36);             /* Strong darkening */
  --mico-color-light-7xshade: calc(l * 0.28);             /* Very dark */
  --mico-color-light-8xshade: calc(l * 0.22);             /* Extremely dark */

  /**
   * Chroma Controls for Shades
   * Gradual saturation enhancement to create rich, vibrant dark colors
   */
  --mico-color-chrome-shade: calc(c * 1.02);              /* Minimal saturation boost */
  --mico-color-chrome-2xshade: calc(c * 1.05);           /* Slight saturation boost */
  --mico-color-chrome-3xshade: calc(c * 1.08);           /* Gentle saturation boost */
  --mico-color-chrome-4xshade: calc(c * 1.12);           /* Moderate saturation boost */
  --mico-color-chrome-5xshade: calc(c * 1.16);           /* Noticeable saturation boost */
  --mico-color-chrome-6xshade: calc(c * 1.20);           /* Strong saturation boost */
  --mico-color-chrome-7xshade: calc(c * 1.24);           /* Very saturated */
  --mico-color-chrome-8xshade: calc(c * 1.28);           /* Extremely saturated */

  /**
   * Hue Controls for Shades
   * Subtle hue shifts to create deeper, more sophisticated dark variations
   */
  --mico-color-hue-shade: calc(h - 1);                    /* Slight cool shift */
  --mico-color-hue-2xshade: calc(h - 2);                  /* Gentle cool shift */
  --mico-color-hue-3xshade: calc(h - 3);                  /* Moderate cool shift */
  --mico-color-hue-4xshade: calc(h - 4);                  /* Noticeable cool shift */
  --mico-color-hue-5xshade: calc(h - 5);                  /* Significant cool shift */
  --mico-color-hue-6xshade: calc(h - 6);                  /* Strong cool shift */
  --mico-color-hue-7xshade: calc(h - 7);                  /* Very cool shift */
  --mico-color-hue-8xshade: calc(h - 8);                  /* Extremely cool shift */

  /* ====================================================================== */
  /* BRAND COLOR VARIATIONS - HARMONIOUS OKLCH SYSTEM                      */
  /* ====================================================================== */

  /**
   * Brand Colors - Primary
   * Base color defined in styleguide.css, variations auto-generated here
   * Using the harmonious tint/shade control system for perfect color progression
   */

  /* Primary Color Tints (lighter variations) */
  --mico-color-primary-light: oklch(from var(--mico-color-primary) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint)); /* Subtle primary tint */
  --mico-color-primary-2xlight: oklch(from var(--mico-color-primary) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint)); /* Gentle primary tint */
  --mico-color-primary-3xlight: oklch(from var(--mico-color-primary) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint)); /* Moderate primary tint */
  --mico-color-primary-4xlight: oklch(from var(--mico-color-primary) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint)); /* Noticeable primary tint */
  --mico-color-primary-5xlight: oklch(from var(--mico-color-primary) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint)); /* Significant primary tint */
  --mico-color-primary-6xlight: oklch(from var(--mico-color-primary) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint)); /* Strong primary tint */
  --mico-color-primary-7xlight: oklch(from var(--mico-color-primary) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint)); /* Very light primary */
  --mico-color-primary-8xlight: oklch(from var(--mico-color-primary) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint)); /* Extremely light primary */

  /* Primary Color Shades (darker variations) */
  --mico-color-primary-dark: oklch(from var(--mico-color-primary) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade)); /* Subtle primary shade */
  --mico-color-primary-2xdark: oklch(from var(--mico-color-primary) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade)); /* Gentle primary shade */
  --mico-color-primary-3xdark: oklch(from var(--mico-color-primary) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade)); /* Moderate primary shade */
  --mico-color-primary-4xdark: oklch(from var(--mico-color-primary) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade)); /* Noticeable primary shade */
  --mico-color-primary-5xdark: oklch(from var(--mico-color-primary) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade)); /* Significant primary shade */
  --mico-color-primary-6xdark: oklch(from var(--mico-color-primary) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade)); /* Strong primary shade */
  --mico-color-primary-7xdark: oklch(from var(--mico-color-primary) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade)); /* Very dark primary */
  --mico-color-primary-8xdark: oklch(from var(--mico-color-primary) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade)); /* Extremely dark primary */

  /**
   * Brand Colors - Secondary
   * Base color defined in styleguide.css, variations auto-generated here
   * Using the harmonious tint/shade control system for perfect color progression
   */

  /* Secondary Color Tints (lighter variations) */
  --mico-color-secondary-light: oklch(from var(--mico-color-secondary) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint)); /* Subtle secondary tint */
  --mico-color-secondary-2xlight: oklch(from var(--mico-color-secondary) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint)); /* Gentle secondary tint */
  --mico-color-secondary-3xlight: oklch(from var(--mico-color-secondary) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint)); /* Moderate secondary tint */
  --mico-color-secondary-4xlight: oklch(from var(--mico-color-secondary) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint)); /* Noticeable secondary tint */
  --mico-color-secondary-5xlight: oklch(from var(--mico-color-secondary) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint)); /* Significant secondary tint */
  --mico-color-secondary-6xlight: oklch(from var(--mico-color-secondary) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint)); /* Strong secondary tint */
  --mico-color-secondary-7xlight: oklch(from var(--mico-color-secondary) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint)); /* Very light secondary */
  --mico-color-secondary-8xlight: oklch(from var(--mico-color-secondary) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint)); /* Extremely light secondary */

  /* Secondary Color Shades (darker variations) */
  --mico-color-secondary-dark: oklch(from var(--mico-color-secondary) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade)); /* Subtle secondary shade */
  --mico-color-secondary-2xdark: oklch(from var(--mico-color-secondary) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade)); /* Gentle secondary shade */
  --mico-color-secondary-3xdark: oklch(from var(--mico-color-secondary) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade)); /* Moderate secondary shade */
  --mico-color-secondary-4xdark: oklch(from var(--mico-color-secondary) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade)); /* Noticeable secondary shade */
  --mico-color-secondary-5xdark: oklch(from var(--mico-color-secondary) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade)); /* Significant secondary shade */
  --mico-color-secondary-6xdark: oklch(from var(--mico-color-secondary) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade)); /* Strong secondary shade */
  --mico-color-secondary-7xdark: oklch(from var(--mico-color-secondary) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade)); /* Very dark secondary */
  --mico-color-secondary-8xdark: oklch(from var(--mico-color-secondary) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade)); /* Extremely dark secondary */

  /**
   * Brand Colors - Accent
   * Base color defined in styleguide.css, variations auto-generated here
   * Using the harmonious tint/shade control system for perfect color progression
   */

  /* Accent Color Tints (lighter variations) */
  --mico-color-accent-light: oklch(from var(--mico-color-accent) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint)); /* Subtle accent tint */
  --mico-color-accent-2xlight: oklch(from var(--mico-color-accent) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint)); /* Gentle accent tint */
  --mico-color-accent-3xlight: oklch(from var(--mico-color-accent) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint)); /* Moderate accent tint */
  --mico-color-accent-4xlight: oklch(from var(--mico-color-accent) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint)); /* Noticeable accent tint */
  --mico-color-accent-5xlight: oklch(from var(--mico-color-accent) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint)); /* Significant accent tint */
  --mico-color-accent-6xlight: oklch(from var(--mico-color-accent) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint)); /* Strong accent tint */
  --mico-color-accent-7xlight: oklch(from var(--mico-color-accent) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint)); /* Very light accent */
  --mico-color-accent-8xlight: oklch(from var(--mico-color-accent) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint)); /* Extremely light accent */

  /* Accent Color Shades (darker variations) */
  --mico-color-accent-dark: oklch(from var(--mico-color-accent) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade)); /* Subtle accent shade */
  --mico-color-accent-2xdark: oklch(from var(--mico-color-accent) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade)); /* Gentle accent shade */
  --mico-color-accent-3xdark: oklch(from var(--mico-color-accent) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade)); /* Moderate accent shade */
  --mico-color-accent-4xdark: oklch(from var(--mico-color-accent) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade)); /* Noticeable accent shade */
  --mico-color-accent-5xdark: oklch(from var(--mico-color-accent) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade)); /* Significant accent shade */
  --mico-color-accent-6xdark: oklch(from var(--mico-color-accent) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade)); /* Strong accent shade */
  --mico-color-accent-7xdark: oklch(from var(--mico-color-accent) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade)); /* Very dark accent */
  --mico-color-accent-8xdark: oklch(from var(--mico-color-accent) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade)); /* Extremely dark accent */

  /**
   * Semantic Colors - OKLCH System
   *
   * Colors that convey meaning and state information.
   * Each semantic color includes 5 tints and 5 shades for comprehensive usage.
   * All colors maintain WCAG AA contrast ratios for accessibility.
   */

  /* Success Color System */
  --mico-color-success: oklch(0.65 0.15 150); /* Base success color - WCAG AA compliant */

  /* Success Tints (lighter variations) */
  --mico-color-success-light: oklch(from var(--mico-color-success) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint));
  --mico-color-success-2xlight: oklch(from var(--mico-color-success) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint));
  --mico-color-success-3xlight: oklch(from var(--mico-color-success) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint));
  --mico-color-success-4xlight: oklch(from var(--mico-color-success) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint));
  --mico-color-success-5xlight: oklch(from var(--mico-color-success) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint));
  --mico-color-success-6xlight: oklch(from var(--mico-color-success) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint));
  --mico-color-success-7xlight: oklch(from var(--mico-color-success) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint));
  --mico-color-success-8xlight: oklch(from var(--mico-color-success) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint));

  /* Success Shades (darker variations) */
  --mico-color-success-dark: oklch(from var(--mico-color-success) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade));
  --mico-color-success-2xdark: oklch(from var(--mico-color-success) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade));
  --mico-color-success-3xdark: oklch(from var(--mico-color-success) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade));
  --mico-color-success-4xdark: oklch(from var(--mico-color-success) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade));
  --mico-color-success-5xdark: oklch(from var(--mico-color-success) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade));
  --mico-color-success-6xdark: oklch(from var(--mico-color-success) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade));
  --mico-color-success-7xdark: oklch(from var(--mico-color-success) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade));
  --mico-color-success-8xdark: oklch(from var(--mico-color-success) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade));

  /* Warning Color System */
  --mico-color-warning: oklch(0.75 0.15 80); /* Base warning color - WCAG AA compliant */

  /* Warning Tints (lighter variations) */
  --mico-color-warning-light: oklch(from var(--mico-color-warning) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint));
  --mico-color-warning-2xlight: oklch(from var(--mico-color-warning) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint));
  --mico-color-warning-3xlight: oklch(from var(--mico-color-warning) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint));
  --mico-color-warning-4xlight: oklch(from var(--mico-color-warning) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint));
  --mico-color-warning-5xlight: oklch(from var(--mico-color-warning) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint));
  --mico-color-warning-6xlight: oklch(from var(--mico-color-warning) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint));
  --mico-color-warning-7xlight: oklch(from var(--mico-color-warning) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint));
  --mico-color-warning-8xlight: oklch(from var(--mico-color-warning) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint));

  /* Warning Shades (darker variations) */
  --mico-color-warning-dark: oklch(from var(--mico-color-warning) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade));
  --mico-color-warning-2xdark: oklch(from var(--mico-color-warning) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade));
  --mico-color-warning-3xdark: oklch(from var(--mico-color-warning) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade));
  --mico-color-warning-4xdark: oklch(from var(--mico-color-warning) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade));
  --mico-color-warning-5xdark: oklch(from var(--mico-color-warning) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade));
  --mico-color-warning-6xdark: oklch(from var(--mico-color-warning) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade));
  --mico-color-warning-7xdark: oklch(from var(--mico-color-warning) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade));
  --mico-color-warning-8xdark: oklch(from var(--mico-color-warning) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade));

  /* Error Color System */
  --mico-color-error: oklch(0.6 0.18 25); /* Base error color - WCAG AA compliant */

  /* Error Tints (lighter variations) */
  --mico-color-error-light: oklch(from var(--mico-color-error) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint));
  --mico-color-error-2xlight: oklch(from var(--mico-color-error) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint));
  --mico-color-error-3xlight: oklch(from var(--mico-color-error) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint));
  --mico-color-error-4xlight: oklch(from var(--mico-color-error) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint));
  --mico-color-error-5xlight: oklch(from var(--mico-color-error) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint));
  --mico-color-error-6xlight: oklch(from var(--mico-color-error) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint));
  --mico-color-error-7xlight: oklch(from var(--mico-color-error) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint));
  --mico-color-error-8xlight: oklch(from var(--mico-color-error) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint));

  /* Error Shades (darker variations) */
  --mico-color-error-dark: oklch(from var(--mico-color-error) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade));
  --mico-color-error-2xdark: oklch(from var(--mico-color-error) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade));
  --mico-color-error-3xdark: oklch(from var(--mico-color-error) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade));
  --mico-color-error-4xdark: oklch(from var(--mico-color-error) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade));
  --mico-color-error-5xdark: oklch(from var(--mico-color-error) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade));
  --mico-color-error-6xdark: oklch(from var(--mico-color-error) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade));
  --mico-color-error-7xdark: oklch(from var(--mico-color-error) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade));
  --mico-color-error-8xdark: oklch(from var(--mico-color-error) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade));

  /* Info Color System */
  --mico-color-info: oklch(0.6 0.15 250); /* Base info color - WCAG AA compliant */

  /* Info Tints (lighter variations) */
  --mico-color-info-light: oklch(from var(--mico-color-info) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint));
  --mico-color-info-2xlight: oklch(from var(--mico-color-info) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint));
  --mico-color-info-3xlight: oklch(from var(--mico-color-info) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint));
  --mico-color-info-4xlight: oklch(from var(--mico-color-info) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint));
  --mico-color-info-5xlight: oklch(from var(--mico-color-info) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint));
  --mico-color-info-6xlight: oklch(from var(--mico-color-info) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint));
  --mico-color-info-7xlight: oklch(from var(--mico-color-info) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint));
  --mico-color-info-8xlight: oklch(from var(--mico-color-info) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint));

  /* Info Shades (darker variations) */
  --mico-color-info-dark: oklch(from var(--mico-color-info) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade));
  --mico-color-info-2xdark: oklch(from var(--mico-color-info) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade));
  --mico-color-info-3xdark: oklch(from var(--mico-color-info) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade));
  --mico-color-info-4xdark: oklch(from var(--mico-color-info) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade));
  --mico-color-info-5xdark: oklch(from var(--mico-color-info) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade));
  --mico-color-info-6xdark: oklch(from var(--mico-color-info) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade));
  --mico-color-info-7xdark: oklch(from var(--mico-color-info) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade));
  --mico-color-info-8xdark: oklch(from var(--mico-color-info) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade));

  /* Visited Color System */
  --mico-color-visited: oklch(0.55 0.15 300); /* Base visited color - distinct from primary */

  /* Visited Tints (lighter variations) */
  --mico-color-visited-2xlight: oklch(from var(--mico-color-visited) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft visited tint */
  --mico-color-visited-3xlight: oklch(from var(--mico-color-visited) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium visited tint */
  --mico-color-visited-4xlight: oklch(from var(--mico-color-visited) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light visited tint */
  --mico-color-visited-5xlight: oklch(from var(--mico-color-visited) calc(l + (1 - l) * 0.85) calc(c * 0.2) h); /* Pale visited tint */

  /* Visited Shades (darker variations) */
  --mico-color-visited-2xdark: oklch(from var(--mico-color-visited) calc(l * 0.8) calc(c * 1.1) h); /* Soft visited shade */
  --mico-color-visited-3xdark: oklch(from var(--mico-color-visited) calc(l * 0.6) calc(c * 1.2) h); /* Medium visited shade */
  --mico-color-visited-4xdark: oklch(from var(--mico-color-visited) calc(l * 0.4) calc(c * 1.3) h); /* Dark visited shade */
  --mico-color-visited-5xdark: oklch(from var(--mico-color-visited) calc(l * 0.25) calc(c * 1.4) h); /* Deep visited shade */

  /**
   * Neutral Colors - Black System (OKLCH)
   *
   * Black color variations with 5 tints and 5 shades.
   * These provide depth and contrast in dark themes with superior perceptual uniformity.
   */
  --mico-color-black: oklch(0.1 0.005 270); /* Base black color */

  /* Black Tints (lighter variations) */
  --mico-color-black-2xlight: oklch(from var(--mico-color-black) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft black tint */
  --mico-color-black-3xlight: oklch(from var(--mico-color-black) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium black tint */
  --mico-color-black-4xlight: oklch(from var(--mico-color-black) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light black tint */
  --mico-color-black-5xlight: oklch(from var(--mico-color-black) calc(l + (1 - l) * 0.85) calc(c * 0.2) h); /* Pale black tint */

  /* Black Shades (darker variations) */
  --mico-color-black-2xdark: oklch(from var(--mico-color-black) calc(l * 0.8) calc(c * 1.1) h); /* Soft black shade */
  --mico-color-black-3xdark: oklch(from var(--mico-color-black) calc(l * 0.6) calc(c * 1.2) h); /* Medium black shade */
  --mico-color-black-4xdark: oklch(from var(--mico-color-black) calc(l * 0.4) calc(c * 1.3) h); /* Dark black shade */
  --mico-color-black-5xdark: oklch(from var(--mico-color-black) calc(l * 0.25) calc(c * 1.4) h); /* Deep black shade */

  /**
   * Neutral Colors - Gray System (OKLCH)
   *
   * Comprehensive gray palette with 5 tints and 5 shades for UI elements, text, and backgrounds.
   * These colors provide optimal contrast and accessibility with perceptual uniformity.
   */
  --mico-color-gray: oklch(0.5 0.01 270); /* Base gray color - true neutral */

  /* Gray Tints (lighter variations) */
  --mico-color-gray-2xlight: oklch(from var(--mico-color-gray) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft gray tint */
  --mico-color-gray-3xlight: oklch(from var(--mico-color-gray) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium gray tint */
  --mico-color-gray-4xlight: oklch(from var(--mico-color-gray) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light gray tint */
  --mico-color-gray-5xlight: oklch(from var(--mico-color-gray) calc(l + (1 - l) * 0.85) calc(c * 0.2) h); /* Pale gray tint */

  /* Gray Shades (darker variations) */
  --mico-color-gray-2xdark: oklch(from var(--mico-color-gray) calc(l * 0.8) calc(c * 1.1) h); /* Soft gray shade */
  --mico-color-gray-3xdark: oklch(from var(--mico-color-gray) calc(l * 0.6) calc(c * 1.2) h); /* Medium gray shade */
  --mico-color-gray-4xdark: oklch(from var(--mico-color-gray) calc(l * 0.4) calc(c * 1.3) h); /* Dark gray shade */
  --mico-color-gray-5xdark: oklch(from var(--mico-color-gray) calc(l * 0.25) calc(c * 1.4) h); /* Deep gray shade */

  /**
   * Neutral Colors - White System (OKLCH)
   *
   * White color variations with 5 tints and 5 shades for subtle backgrounds and overlays.
   * These provide clean, minimal aesthetics in light themes with superior color precision.
   */
  --mico-color-white: oklch(0.95 0.005 270); /* Base white color */

  /* White Tints (lighter variations) */
  --mico-color-white-2xlight: oklch(from var(--mico-color-white) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft white tint */
  --mico-color-white-3xlight: oklch(from var(--mico-color-white) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium white tint */
  --mico-color-white-4xlight: oklch(from var(--mico-color-white) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light white tint */
  --mico-color-white-5xlight: oklch(from var(--mico-color-white) calc(l + (1 - l) * 0.85) calc(c * 0.2) h); /* Pale white tint */

  /* White Shades (darker variations) */
  --mico-color-white-2xdark: oklch(from var(--mico-color-white) calc(l * 0.8) calc(c * 1.1) h); /* Soft white shade */
  --mico-color-white-3xdark: oklch(from var(--mico-color-white) calc(l * 0.6) calc(c * 1.2) h); /* Medium white shade */
  --mico-color-white-4xdark: oklch(from var(--mico-color-white) calc(l * 0.4) calc(c * 1.3) h); /* Dark white shade */
  --mico-color-white-5xdark: oklch(from var(--mico-color-white) calc(l * 0.25) calc(c * 1.4) h); /* Deep white shade */

  /**
   * Transparency Colors - OKLCH Alpha System
   *
   * Creative transparency variations using OKLCH with alpha channels.
   * These provide sophisticated overlay and background effects with superior color mixing.
   * Each transparency level has a creative name for intuitive usage.
   */

  /* Black Transparency Variations */
  --mico-color-black-whisper: oklch(from var(--mico-color-black) l c h / 0.1);    /* Subtle black overlay */
  --mico-color-black-breath: oklch(from var(--mico-color-black) l c h / 0.2);     /* Light black overlay */
  --mico-color-black-mist: oklch(from var(--mico-color-black) l c h / 0.3);       /* Gentle black overlay */
  --mico-color-black-veil: oklch(from var(--mico-color-black) l c h / 0.4);       /* Moderate black overlay */
  --mico-color-black-shadow: oklch(from var(--mico-color-black) l c h / 0.5);     /* Balanced black overlay */
  --mico-color-black-shroud: oklch(from var(--mico-color-black) l c h / 0.6);     /* Strong black overlay */
  --mico-color-black-cloak: oklch(from var(--mico-color-black) l c h / 0.7);      /* Heavy black overlay */
  --mico-color-black-eclipse: oklch(from var(--mico-color-black) l c h / 0.8);    /* Deep black overlay */
  --mico-color-black-void: oklch(from var(--mico-color-black) l c h / 0.9);       /* Intense black overlay */

  /* Gray Transparency Variations */
  --mico-color-gray-whisper: oklch(from var(--mico-color-gray) l c h / 0.1);      /* Subtle gray overlay */
  --mico-color-gray-breath: oklch(from var(--mico-color-gray) l c h / 0.2);       /* Light gray overlay */
  --mico-color-gray-mist: oklch(from var(--mico-color-gray) l c h / 0.3);         /* Gentle gray overlay */
  --mico-color-gray-veil: oklch(from var(--mico-color-gray) l c h / 0.4);         /* Moderate gray overlay */
  --mico-color-gray-shadow: oklch(from var(--mico-color-gray) l c h / 0.5);       /* Balanced gray overlay */
  --mico-color-gray-shroud: oklch(from var(--mico-color-gray) l c h / 0.6);       /* Strong gray overlay */
  --mico-color-gray-cloak: oklch(from var(--mico-color-gray) l c h / 0.7);        /* Heavy gray overlay */
  --mico-color-gray-eclipse: oklch(from var(--mico-color-gray) l c h / 0.8);      /* Deep gray overlay */
  --mico-color-gray-void: oklch(from var(--mico-color-gray) l c h / 0.9);         /* Intense gray overlay */

  /* White Transparency Variations */
  --mico-color-white-whisper: oklch(from var(--mico-color-white) l c h / 0.1);    /* Subtle white overlay */
  --mico-color-white-breath: oklch(from var(--mico-color-white) l c h / 0.2);     /* Light white overlay */
  --mico-color-white-mist: oklch(from var(--mico-color-white) l c h / 0.3);       /* Gentle white overlay */
  --mico-color-white-veil: oklch(from var(--mico-color-white) l c h / 0.4);       /* Moderate white overlay */
  --mico-color-white-shadow: oklch(from var(--mico-color-white) l c h / 0.5);     /* Balanced white overlay */
  --mico-color-white-shroud: oklch(from var(--mico-color-white) l c h / 0.6);     /* Strong white overlay */
  --mico-color-white-cloak: oklch(from var(--mico-color-white) l c h / 0.7);      /* Heavy white overlay */
  --mico-color-white-eclipse: oklch(from var(--mico-color-white) l c h / 0.8);    /* Deep white overlay */
  --mico-color-white-void: oklch(from var(--mico-color-white) l c h / 0.9);       /* Intense white overlay */

  /* Brand Color Transparency Variations */
  --mico-color-primary-whisper: oklch(from var(--mico-color-primary) l c h / 0.1);   /* Subtle primary overlay */
  --mico-color-primary-breath: oklch(from var(--mico-color-primary) l c h / 0.2);    /* Light primary overlay */
  --mico-color-primary-mist: oklch(from var(--mico-color-primary) l c h / 0.3);      /* Gentle primary overlay */
  --mico-color-primary-veil: oklch(from var(--mico-color-primary) l c h / 0.4);      /* Moderate primary overlay */
  --mico-color-primary-shadow: oklch(from var(--mico-color-primary) l c h / 0.5);    /* Balanced primary overlay */

  --mico-color-secondary-whisper: oklch(from var(--mico-color-secondary) l c h / 0.1); /* Subtle secondary overlay */
  --mico-color-secondary-breath: oklch(from var(--mico-color-secondary) l c h / 0.2);  /* Light secondary overlay */
  --mico-color-secondary-mist: oklch(from var(--mico-color-secondary) l c h / 0.3);    /* Gentle secondary overlay */
  --mico-color-secondary-veil: oklch(from var(--mico-color-secondary) l c h / 0.4);    /* Moderate secondary overlay */
  --mico-color-secondary-shadow: oklch(from var(--mico-color-secondary) l c h / 0.5);  /* Balanced secondary overlay */

  --mico-color-accent-whisper: oklch(from var(--mico-color-accent) l c h / 0.1);     /* Subtle accent overlay */
  --mico-color-accent-breath: oklch(from var(--mico-color-accent) l c h / 0.2);      /* Light accent overlay */
  --mico-color-accent-mist: oklch(from var(--mico-color-accent) l c h / 0.3);        /* Gentle accent overlay */
  --mico-color-accent-veil: oklch(from var(--mico-color-accent) l c h / 0.4);        /* Moderate accent overlay */
  --mico-color-accent-shadow: oklch(from var(--mico-color-accent) l c h / 0.5);      /* Balanced accent overlay */

  /**
   * Extended Color Palette - OKLCH System
   *
   * Comprehensive color palette for diverse design needs using OKLCH color space.
   * Each color includes 5 tints and 5 shades for superior color consistency.
   * All colors maintain accessibility and contrast compliance with perceptual uniformity.
   */

  /* Red Color System - Error states, alerts, danger */
  --mico-color-red: oklch(0.55 0.22 27); /* Base red color - vibrant crimson, WCAG AA compliant */

  /* Red Tints (lighter variations) */
  --mico-color-red-light: oklch(from var(--mico-color-red) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint));
  --mico-color-red-2xlight: oklch(from var(--mico-color-red) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint));
  --mico-color-red-3xlight: oklch(from var(--mico-color-red) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint));
  --mico-color-red-4xlight: oklch(from var(--mico-color-red) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint));
  --mico-color-red-5xlight: oklch(from var(--mico-color-red) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint));
  --mico-color-red-6xlight: oklch(from var(--mico-color-red) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint));
  --mico-color-red-7xlight: oklch(from var(--mico-color-red) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint));
  --mico-color-red-8xlight: oklch(from var(--mico-color-red) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint));

  /* Red Shades (darker variations) */
  --mico-color-red-dark: oklch(from var(--mico-color-red) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade));
  --mico-color-red-2xdark: oklch(from var(--mico-color-red) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade));
  --mico-color-red-3xdark: oklch(from var(--mico-color-red) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade));
  --mico-color-red-4xdark: oklch(from var(--mico-color-red) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade));
  --mico-color-red-5xdark: oklch(from var(--mico-color-red) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade));
  --mico-color-red-6xdark: oklch(from var(--mico-color-red) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade));
  --mico-color-red-7xdark: oklch(from var(--mico-color-red) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade));
  --mico-color-red-8xdark: oklch(from var(--mico-color-red) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade));

  /* Yellow Color System - Warning states, highlights */
  --mico-color-yellow: oklch(96.267% 0.19961 108.728); /* Base yellow color - warm amber, not harsh */

  /* Yellow Tints (lighter variations) */
  --mico-color-yellow-light: oklch(from var(--mico-color-yellow) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint));
  --mico-color-yellow-2xlight: oklch(from var(--mico-color-yellow) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint));
  --mico-color-yellow-3xlight: oklch(from var(--mico-color-yellow) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint));
  --mico-color-yellow-4xlight: oklch(from var(--mico-color-yellow) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint));
  --mico-color-yellow-5xlight: oklch(from var(--mico-color-yellow) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint));
  --mico-color-yellow-6xlight: oklch(from var(--mico-color-yellow) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint));
  --mico-color-yellow-7xlight: oklch(from var(--mico-color-yellow) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint));
  --mico-color-yellow-8xlight: oklch(from var(--mico-color-yellow) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint));

  /* Yellow Shades (darker variations) */
  --mico-color-yellow-dark: oklch(from var(--mico-color-yellow) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade));
  --mico-color-yellow-2xdark: oklch(from var(--mico-color-yellow) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade));
  --mico-color-yellow-3xdark: oklch(from var(--mico-color-yellow) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade));
  --mico-color-yellow-4xdark: oklch(from var(--mico-color-yellow) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade));
  --mico-color-yellow-5xdark: oklch(from var(--mico-color-yellow) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade));
  --mico-color-yellow-6xdark: oklch(from var(--mico-color-yellow) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade));
  --mico-color-yellow-7xdark: oklch(from var(--mico-color-yellow) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade));
  --mico-color-yellow-8xdark: oklch(from var(--mico-color-yellow) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade));

  /* Green Color System - Success states, positive actions */
  --mico-color-green: oklch(67.749% 0.19955 141.94); /* Base green color - fresh emerald, nature-inspired */

  /* Green Tints (lighter variations) */
  --mico-color-green-light: oklch(from var(--mico-color-green) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint));
  --mico-color-green-2xlight: oklch(from var(--mico-color-green) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint));
  --mico-color-green-3xlight: oklch(from var(--mico-color-green) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint));
  --mico-color-green-4xlight: oklch(from var(--mico-color-green) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint));
  --mico-color-green-5xlight: oklch(from var(--mico-color-green) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint));
  --mico-color-green-6xlight: oklch(from var(--mico-color-green) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint));
  --mico-color-green-7xlight: oklch(from var(--mico-color-green) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint));
  --mico-color-green-8xlight: oklch(from var(--mico-color-green) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint));

  /* Green Shades (darker variations) */
  --mico-color-green-dark: oklch(from var(--mico-color-green) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade));
  --mico-color-green-2xdark: oklch(from var(--mico-color-green) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade));
  --mico-color-green-3xdark: oklch(from var(--mico-color-green) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade));
  --mico-color-green-4xdark: oklch(from var(--mico-color-green) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade));
  --mico-color-green-5xdark: oklch(from var(--mico-color-green) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade));
  --mico-color-green-6xdark: oklch(from var(--mico-color-green) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade));
  --mico-color-green-7xdark: oklch(from var(--mico-color-green) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade));
  --mico-color-green-8xdark: oklch(from var(--mico-color-green) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade));

  /* Blue Color System - Information, links, primary actions */
  --mico-color-blue: oklch(52% 0.20589 259.44); /* Base blue color - trustworthy azure, professional */

  /* Blue Tints (lighter variations) */
  --mico-color-blue-light: oklch(from var(--mico-color-blue) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint));
  --mico-color-blue-2xlight: oklch(from var(--mico-color-blue) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint));
  --mico-color-blue-3xlight: oklch(from var(--mico-color-blue) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint));
  --mico-color-blue-4xlight: oklch(from var(--mico-color-blue) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint));
  --mico-color-blue-5xlight: oklch(from var(--mico-color-blue) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint));
  --mico-color-blue-6xlight: oklch(from var(--mico-color-blue) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint));
  --mico-color-blue-7xlight: oklch(from var(--mico-color-blue) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint));
  --mico-color-blue-8xlight: oklch(from var(--mico-color-blue) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint));

  /* Blue Shades (darker variations) */
  --mico-color-blue-dark: oklch(from var(--mico-color-blue) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade));
  --mico-color-blue-2xdark: oklch(from var(--mico-color-blue) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade));
  --mico-color-blue-3xdark: oklch(from var(--mico-color-blue) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade));
  --mico-color-blue-4xdark: oklch(from var(--mico-color-blue) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade));
  --mico-color-blue-5xdark: oklch(from var(--mico-color-blue) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade));
  --mico-color-blue-6xdark: oklch(from var(--mico-color-blue) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade));
  --mico-color-blue-7xdark: oklch(from var(--mico-color-blue) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade));
  --mico-color-blue-8xdark: oklch(from var(--mico-color-blue) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade));

  /* Indigo Color System - Deep blues, professional themes */
  --mico-color-indigo: oklch(0.52 0.17 275); /* Base indigo color - sophisticated violet-blue */

  /* Indigo Tints (lighter variations) */
  --mico-color-indigo-light: oklch(from var(--mico-color-indigo) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint));
  --mico-color-indigo-2xlight: oklch(from var(--mico-color-indigo) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint));
  --mico-color-indigo-3xlight: oklch(from var(--mico-color-indigo) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint));
  --mico-color-indigo-4xlight: oklch(from var(--mico-color-indigo) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint));
  --mico-color-indigo-5xlight: oklch(from var(--mico-color-indigo) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint));
  --mico-color-indigo-6xlight: oklch(from var(--mico-color-indigo) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint));
  --mico-color-indigo-7xlight: oklch(from var(--mico-color-indigo) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint));
  --mico-color-indigo-8xlight: oklch(from var(--mico-color-indigo) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint));

  /* Indigo Shades (darker variations) */
  --mico-color-indigo-dark: oklch(from var(--mico-color-indigo) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade));
  --mico-color-indigo-2xdark: oklch(from var(--mico-color-indigo) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade));
  --mico-color-indigo-3xdark: oklch(from var(--mico-color-indigo) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade));
  --mico-color-indigo-4xdark: oklch(from var(--mico-color-indigo) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade));
  --mico-color-indigo-5xdark: oklch(from var(--mico-color-indigo) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade));
  --mico-color-indigo-6xdark: oklch(from var(--mico-color-indigo) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade));
  --mico-color-indigo-7xdark: oklch(from var(--mico-color-indigo) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade));
  --mico-color-indigo-8xdark: oklch(from var(--mico-color-indigo) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade));

  /* Purple Color System - Creative themes, luxury */
  --mico-color-purple: oklch(48.171% 0.20979 307.996); /* Base purple color - rich amethyst, creative */

  /* Purple Tints (lighter variations) */
  --mico-color-purple-light: oklch(from var(--mico-color-purple) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint));
  --mico-color-purple-2xlight: oklch(from var(--mico-color-purple) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint));
  --mico-color-purple-3xlight: oklch(from var(--mico-color-purple) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint));
  --mico-color-purple-4xlight: oklch(from var(--mico-color-purple) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint));
  --mico-color-purple-5xlight: oklch(from var(--mico-color-purple) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint));
  --mico-color-purple-6xlight: oklch(from var(--mico-color-purple) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint));
  --mico-color-purple-7xlight: oklch(from var(--mico-color-purple) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint));
  --mico-color-purple-8xlight: oklch(from var(--mico-color-purple) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint));

  /* Purple Shades (darker variations) */
  --mico-color-purple-dark: oklch(from var(--mico-color-purple) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade));
  --mico-color-purple-2xdark: oklch(from var(--mico-color-purple) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade));
  --mico-color-purple-3xdark: oklch(from var(--mico-color-purple) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade));
  --mico-color-purple-4xdark: oklch(from var(--mico-color-purple) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade));
  --mico-color-purple-5xdark: oklch(from var(--mico-color-purple) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade));
  --mico-color-purple-6xdark: oklch(from var(--mico-color-purple) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade));
  --mico-color-purple-7xdark: oklch(from var(--mico-color-purple) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade));
  --mico-color-purple-8xdark: oklch(from var(--mico-color-purple) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade));

  /* Pink Color System - Warm themes, highlights */
  --mico-color-pink: oklch(0.68 0.16 5); /* Base pink color - warm rose, approachable */

  /* Pink Tints (lighter variations) */
  --mico-color-pink-light: oklch(from var(--mico-color-pink) var(--mico-color-light-tint) var(--mico-color-chrome-tint) var(--mico-color-hue-tint));
  --mico-color-pink-2xlight: oklch(from var(--mico-color-pink) var(--mico-color-light-2xtint) var(--mico-color-chrome-2xtint) var(--mico-color-hue-2xtint));
  --mico-color-pink-3xlight: oklch(from var(--mico-color-pink) var(--mico-color-light-3xtint) var(--mico-color-chrome-3xtint) var(--mico-color-hue-3xtint));
  --mico-color-pink-4xlight: oklch(from var(--mico-color-pink) var(--mico-color-light-4xtint) var(--mico-color-chrome-4xtint) var(--mico-color-hue-4xtint));
  --mico-color-pink-5xlight: oklch(from var(--mico-color-pink) var(--mico-color-light-5xtint) var(--mico-color-chrome-5xtint) var(--mico-color-hue-5xtint));
  --mico-color-pink-6xlight: oklch(from var(--mico-color-pink) var(--mico-color-light-6xtint) var(--mico-color-chrome-6xtint) var(--mico-color-hue-6xtint));
  --mico-color-pink-7xlight: oklch(from var(--mico-color-pink) var(--mico-color-light-7xtint) var(--mico-color-chrome-7xtint) var(--mico-color-hue-7xtint));
  --mico-color-pink-8xlight: oklch(from var(--mico-color-pink) var(--mico-color-light-8xtint) var(--mico-color-chrome-8xtint) var(--mico-color-hue-8xtint));

  /* Pink Shades (darker variations) */
  --mico-color-pink-dark: oklch(from var(--mico-color-pink) var(--mico-color-light-shade) var(--mico-color-chrome-shade) var(--mico-color-hue-shade));
  --mico-color-pink-2xdark: oklch(from var(--mico-color-pink) var(--mico-color-light-2xshade) var(--mico-color-chrome-2xshade) var(--mico-color-hue-2xshade));
  --mico-color-pink-3xdark: oklch(from var(--mico-color-pink) var(--mico-color-light-3xshade) var(--mico-color-chrome-3xshade) var(--mico-color-hue-3xshade));
  --mico-color-pink-4xdark: oklch(from var(--mico-color-pink) var(--mico-color-light-4xshade) var(--mico-color-chrome-4xshade) var(--mico-color-hue-4xshade));
  --mico-color-pink-5xdark: oklch(from var(--mico-color-pink) var(--mico-color-light-5xshade) var(--mico-color-chrome-5xshade) var(--mico-color-hue-5xshade));
  --mico-color-pink-6xdark: oklch(from var(--mico-color-pink) var(--mico-color-light-6xshade) var(--mico-color-chrome-6xshade) var(--mico-color-hue-6xshade));
  --mico-color-pink-7xdark: oklch(from var(--mico-color-pink) var(--mico-color-light-7xshade) var(--mico-color-chrome-7xshade) var(--mico-color-hue-7xshade));
  --mico-color-pink-8xdark: oklch(from var(--mico-color-pink) var(--mico-color-light-8xshade) var(--mico-color-chrome-8xshade) var(--mico-color-hue-8xshade));
}