/* --mico-breakpoint-3xl: 1600px; Super large devices (wider screens) */ 
@media screen and (max-width: 1600px) {


  .text-left-3xl { text-align: var(--mico-text-align-left) !important; }
  .text-center-3xl { text-align: var(--mico-text-align-center) !important; }
  .text-right-3xl { text-align: var(--mico-text-align-right) !important; }
  .text-justify-3xl { text-align: var(--mico-text-align-justify) !important; }
  .text-start-3xl { text-align: var(--mico-text-align-start) !important; }
  .text-end-3xl { text-align: var(--mico-text-align-end) !important; }

  .mx-auto-3xl { margin-inline: auto !important; }
  .my-auto-3xl { margin-block: auto !important; }

  .d-none-3xl { display: none !important; }
  .d-inline-3xl { display: inline !important; }
  .d-inline-block-3xl { display: inline-block !important; }
  .d-block-3xl { display: block !important; }
  .d-flex-3xl { display: flex !important; }
  .d-inline-flex-3xl { display: inline-flex !important; }
  .d-grid-3xl { display: grid !important; }

  .grid-cols-1-3xl { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-3xl { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-3xl { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-3xl { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-3xl { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-3xl { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-3xl { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-3xl { grid-column: span 1 !important; }
  .col-2-3xl { grid-column: span 2 !important; }
  .col-3-3xl { grid-column: span 3 !important; }
  .col-4-3xl { grid-column: span 4 !important; }
  .col-5-3xl { grid-column: span 5 !important; }
  .col-6-3xl { grid-column: span 6 !important; }
  .col-7-3xl { grid-column: span 7 !important; }
  .col-8-3xl { grid-column: span 8 !important; }
  .col-9-3xl { grid-column: span 9 !important; }
  .col-10-3xl { grid-column: span 10 !important; }
  .col-11-3xl { grid-column: span 11 !important; }
  .col-12-3xl { grid-column: span 12 !important; }


  /* Flexbox */
.flex-row-3xl { flex-direction: row !important; }
.flex-column-3xl { flex-direction: column !important; }
.flex-row-reverse-3xl { flex-direction: row-reverse !important; }
.flex-column-reverse-3xl { flex-direction: column-reverse !important; }


.flex-wrap-3xl { flex-wrap: wrap !important; }
.flex-nowrap-3xl { flex-wrap: nowrap !important; }
.flex-wrap-reverse-3xl { flex-wrap: wrap-reverse !important; }

.justify-content-start-3xl { justify-content: flex-start !important; }
.justify-content-end-3xl { justify-content: flex-end !important; }
.justify-content-center-3xl { justify-content: center !important; }
.justify-content-between-3xl { justify-content: space-between !important; }
.justify-content-around-3xl { justify-content: space-around !important; }
.justify-content-evenly-3xl { justify-content: space-evenly !important; }

.align-items-start-3xl { align-items: flex-start !important; }
.align-items-end-3xl { align-items: flex-end !important; }
.align-items-center-3xl { align-items: center !important; }
.align-items-baseline-3xl { align-items: baseline !important; }
.align-items-stretch-3xl { align-items: stretch !important; }

.align-content-start-3xl { align-content: flex-start !important; }
.align-content-end-3xl { align-content: flex-end !important; }
.align-content-center-3xl { align-content: center !important; }
.align-content-between-3xl { align-content: space-between !important; }
.align-content-around-3xl { align-content: space-around !important; }
.align-content-stretch-3xl { align-content: stretch !important; }

.align-self-auto-3xl { align-self: auto !important; }
.align-self-start-3xl { align-self: flex-start !important; }
.align-self-end-3xl { align-self: flex-end !important; }
.align-self-center-3xl { align-self: center !important; }
.align-self-baseline-3xl { align-self: baseline !important; }
.align-self-stretch-3xl { align-self: stretch !important; }

.flex-grow-0-3xl { flex-grow: 0 !important; }
.flex-grow-1-3xl { flex-grow: 1 !important; }
.flex-shrink-0-3xl { flex-shrink: 0 !important; }
.flex-shrink-1-3xl { flex-shrink: 1 !important; }

.flex-1-3xl { flex: 1 1 0% !important; }
.flex-auto-3xl { flex: 1 1 auto !important; }
.flex-initial-3xl { flex: 0 1 auto !important; }
.flex-none-3xl { flex: none !important; }


  /* Width and Height */
.w-none-3xl { width: none !important; }
.w-auto-3xl { width: auto !important; }
.w-screen-3xl { width: 100vw !important; }
.w-10p-3xl { width: 10% !important; }
.w-20p-3xl { width: 20% !important; }
.w-30p-3xl { width: 30% !important; }
.w-40p-3xl { width: 40% !important; }
.w-50p-3xl { width: 50% !important; }
.w-60p-3xl { width: 60% !important; }
.w-70p-3xl { width: 70% !important; }
.w-80p-3xl { width: 80% !important; }
.w-90p-3xl { width: 90% !important; }
.w-100p-3xl { width: 100% !important; }

.h-none-3xl { height: none !important; }
.h-auto-3xl { height: auto !important; }
.h-screen-3xl { height: 100vh !important; }
.h-10p-3xl { height: 10% !important; }
.h-20p-3xl { height: 20% !important; }
.h-30p-3xl { height: 30% !important; }
.h-40p-3xl { height: 40% !important; }
.h-50p-3xl { height: 50% !important; }
.h-60p-3xl { height: 60% !important; }
.h-70p-3xl { height: 70% !important; }
.h-80p-3xl { height: 80% !important; }
.h-90p-3xl { height: 90% !important; }
.h-100p-3xl { height: 100% !important; }

/* Block Button */
.btn-block-3xl {
  display: var(--mico-display-block);
  width: var(--mico-width-full);
}
}

/* --mico-breakpoint-2xl: 1440px; Ultra large devices (wide screens) */
@media screen and (max-width: 1440px) {



  .text-left-2xl { text-align: var(--mico-text-align-left) !important; }
  .text-center-2xl { text-align: var(--mico-text-align-center) !important; }
  .text-right-2xl { text-align: var(--mico-text-align-right) !important; }
  .text-justify-2xl { text-align: var(--mico-text-align-justify) !important; }
  .text-start-2xl { text-align: var(--mico-text-align-start) !important; }
  .text-end-2xl { text-align: var(--mico-text-align-end) !important; }

  .mx-auto-2xl { margin-inline: auto !important; }
  .my-auto-2xl { margin-block: auto !important; }

  .d-none-2xl { display: none !important; }
  .d-inline-2xl { display: inline !important; }
  .d-inline-block-2xl { display: inline-block !important; }
  .d-block-2xl { display: block !important; }
  .d-flex-2xl { display: flex !important; }
  .d-inline-flex-2xl { display: inline-flex !important; }
  .d-grid-2xl { display: grid !important; }

  .grid-cols-1-2xl { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-2xl { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-2xl { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-2xl { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-2xl { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-2xl { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-2xl { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-2xl { grid-column: span 1 !important; }
  .col-2-2xl { grid-column: span 2 !important; }
  .col-3-2xl { grid-column: span 3 !important; }
  .col-4-2xl { grid-column: span 4 !important; }
  .col-5-2xl { grid-column: span 5 !important; }
  .col-6-2xl { grid-column: span 6 !important; }
  .col-7-2xl { grid-column: span 7 !important; }
  .col-8-2xl { grid-column: span 8 !important; }
  .col-9-2xl { grid-column: span 9 !important; }
  .col-10-2xl { grid-column: span 10 !important; }
  .col-11-2xl { grid-column: span 11 !important; }
  .col-12-2xl { grid-column: span 12 !important; }


  /* Flexbox */
.flex-row-2xl { flex-direction: row !important; }
.flex-column-2xl { flex-direction: column !important; }
.flex-row-reverse-2xl { flex-direction: row-reverse !important; }
.flex-column-reverse-2xl { flex-direction: column-reverse !important; }


.flex-wrap-2xl { flex-wrap: wrap !important; }
.flex-nowrap-2xl { flex-wrap: nowrap !important; }
.flex-wrap-reverse-2xl { flex-wrap: wrap-reverse !important; }

.justify-content-start-2xl { justify-content: flex-start !important; }
.justify-content-end-2xl { justify-content: flex-end !important; }
.justify-content-center-2xl { justify-content: center !important; }
.justify-content-between-2xl { justify-content: space-between !important; }
.justify-content-around-2xl { justify-content: space-around !important; }
.justify-content-evenly-2xl { justify-content: space-evenly !important; }

.align-items-start-2xl { align-items: flex-start !important; }
.align-items-end-2xl { align-items: flex-end !important; }
.align-items-center-2xl { align-items: center !important; }
.align-items-baseline-2xl { align-items: baseline !important; }
.align-items-stretch-2xl { align-items: stretch !important; }

.align-content-start-2xl { align-content: flex-start !important; }
.align-content-end-2xl { align-content: flex-end !important; }
.align-content-center-2xl { align-content: center !important; }
.align-content-between-2xl { align-content: space-between !important; }
.align-content-around-2xl { align-content: space-around !important; }
.align-content-stretch-2xl { align-content: stretch !important; }

.align-self-auto-2xl { align-self: auto !important; }
.align-self-start-2xl { align-self: flex-start !important; }
.align-self-end-2xl { align-self: flex-end !important; }
.align-self-center-2xl { align-self: center !important; }
.align-self-baseline-2xl { align-self: baseline !important; }
.align-self-stretch-2xl { align-self: stretch !important; }

.flex-grow-0-2xl { flex-grow: 0 !important; }
.flex-grow-1-2xl { flex-grow: 1 !important; }
.flex-shrink-0-2xl { flex-shrink: 0 !important; }
.flex-shrink-1-2xl { flex-shrink: 1 !important; }

.flex-1-2xl { flex: 1 1 0% !important; }
.flex-auto-2xl { flex: 1 1 auto !important; }
.flex-initial-2xl { flex: 0 1 auto !important; }
.flex-none-2xl { flex: none !important; }


  /* Width and Height */
.w-none-2xl { width: none !important; }
.w-auto-2xl { width: auto !important; }
.w-screen-2xl { width: 100vw !important; }
.w-10p-2xl { width: 10% !important; }
.w-20p-2xl { width: 20% !important; }
.w-30p-2xl { width: 30% !important; }
.w-40p-2xl { width: 40% !important; }
.w-50p-2xl { width: 50% !important; }
.w-60p-2xl { width: 60% !important; }
.w-70p-2xl { width: 70% !important; }
.w-80p-2xl { width: 80% !important; }
.w-90p-2xl { width: 90% !important; }
.w-100p-2xl { width: 100% !important; }

.h-none-2xl { height: none !important; }
.h-auto-2xl { height: auto !important; }
.h-screen-2xl { height: 100vh !important; }
.h-10p-2xl { height: 10% !important; }
.h-20p-2xl { height: 20% !important; }
.h-30p-2xl { height: 30% !important; }
.h-40p-2xl { height: 40% !important; }
.h-50p-2xl { height: 50% !important; }
.h-60p-2xl { height: 60% !important; }
.h-70p-2xl { height: 70% !important; }
.h-80p-2xl { height: 80% !important; }
.h-90p-2xl { height: 90% !important; }
.h-100p-2xl { height: 100% !important; }


/* Block Button */
.btn-block-2xl {
  display: var(--mico-display-block);
  width: var(--mico-width-full);
}
}

/* --mico-breakpoint-xl: 1280px; Extra large devices (large screens) */
@media screen and (max-width: 1280px) {



  .text-left-xl { text-align: var(--mico-text-align-left) !important; }
  .text-center-xl { text-align: var(--mico-text-align-center) !important; }
  .text-right-xl { text-align: var(--mico-text-align-right) !important; }
  .text-justify-xl { text-align: var(--mico-text-align-justify) !important; }
  .text-start-xl { text-align: var(--mico-text-align-start) !important; }
  .text-end-xl { text-align: var(--mico-text-align-end) !important; }

  .mx-auto-xl { margin-inline: auto !important; }
  .my-auto-xl { margin-block: auto !important; }

  .d-none-xl { display: none !important; }
  .d-inline-xl { display: inline !important; }
  .d-inline-block-xl { display: inline-block !important; }
  .d-block-xl { display: block !important; }
  .d-flex-xl { display: flex !important; }
  .d-inline-flex-xl { display: inline-flex !important; }
  .d-grid-xl { display: grid !important; }

  .grid-cols-1-xl { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-xl { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-xl { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-xl { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-xl { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-xl { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-xl { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-xl { grid-column: span 1 !important; }
  .col-2-xl { grid-column: span 2 !important; }
  .col-3-xl { grid-column: span 3 !important; }
  .col-4-xl { grid-column: span 4 !important; }
  .col-5-xl { grid-column: span 5 !important; }
  .col-6-xl { grid-column: span 6 !important; }
  .col-7-xl { grid-column: span 7 !important; }
  .col-8-xl { grid-column: span 8 !important; }
  .col-9-xl { grid-column: span 9 !important; }
  .col-10-xl { grid-column: span 10 !important; }
  .col-11-xl { grid-column: span 11 !important; }
  .col-12-xl { grid-column: span 12 !important; }


  /* Flexbox */
.flex-row-xl { flex-direction: row !important; }
.flex-column-xl { flex-direction: column !important; }
.flex-row-reverse-xl { flex-direction: row-reverse !important; }
.flex-column-reverse-xl { flex-direction: column-reverse !important; }


.flex-wrap-xl { flex-wrap: wrap !important; }
.flex-nowrap-xl { flex-wrap: nowrap !important; }
.flex-wrap-reverse-xl { flex-wrap: wrap-reverse !important; }

.justify-content-start-xl { justify-content: flex-start !important; }
.justify-content-end-xl { justify-content: flex-end !important; }
.justify-content-center-xl { justify-content: center !important; }
.justify-content-between-xl { justify-content: space-between !important; }
.justify-content-around-xl { justify-content: space-around !important; }
.justify-content-evenly-xl { justify-content: space-evenly !important; }

.align-items-start-xl { align-items: flex-start !important; }
.align-items-end-xl { align-items: flex-end !important; }
.align-items-center-xl { align-items: center !important; }
.align-items-baseline-xl { align-items: baseline !important; }
.align-items-stretch-xl { align-items: stretch !important; }

.align-content-start-xl { align-content: flex-start !important; }
.align-content-end-xl { align-content: flex-end !important; }
.align-content-center-xl { align-content: center !important; }
.align-content-between-xl { align-content: space-between !important; }
.align-content-around-xl { align-content: space-around !important; }
.align-content-stretch-xl { align-content: stretch !important; }

.align-self-auto-xl { align-self: auto !important; }
.align-self-start-xl { align-self: flex-start !important; }
.align-self-end-xl { align-self: flex-end !important; }
.align-self-center-xl { align-self: center !important; }
.align-self-baseline-xl { align-self: baseline !important; }
.align-self-stretch-xl { align-self: stretch !important; }

.flex-grow-0-xl { flex-grow: 0 !important; }
.flex-grow-1-xl { flex-grow: 1 !important; }
.flex-shrink-0-xl { flex-shrink: 0 !important; }
.flex-shrink-1-xl { flex-shrink: 1 !important; }

.flex-1-xl { flex: 1 1 0% !important; }
.flex-auto-xl { flex: 1 1 auto !important; }
.flex-initial-xl { flex: 0 1 auto !important; }
.flex-none-xl { flex: none !important; }
  /* Width and Height */
.w-none-xl { width: none !important; }
.w-auto-xl { width: auto !important; }
.w-screen-xl { width: 100vw !important; }
.w--10p-xl { width: 10% !important; }
.w--20p-xl { width: 20% !important; }
.w--30p-xl { width: 30% !important; }
.w--40p-xl { width: 40% !important; }
.w--50p-xl { width: 50% !important; }
.w--60p-xl { width: 60% !important; }
.w--70p-xl { width: 70% !important; }
.w--80p-xl { width: 80% !important; }
.w--90p-xl { width: 90% !important; }
.w--100p-xl { width: 100% !important; }

.h-none-xl { height: none !important; }
.h-auto-xl { height: auto !important; }
.h-screen-xl { height: 100vh !important; }
.h-10p-xl { height: 10% !important; }
.h-20p-xl { height: 20% !important; }
.h-30p-xl { height: 30% !important; }
.h-40p-xl { height: 40% !important; }
.h-50p-xl { height: 50% !important; }
.h-60p-xl { height: 60% !important; }
.h-70p-xl { height: 70% !important; }
.h-80p-xl { height: 80% !important; }
.h-90p-xl { height: 90% !important; }
.h-100p-xl { height: 100% !important; }


/* Block Button */
.btn-block-xl {
  display: var(--mico-display-block);
  width: var(--mico-width-full);
}
}

/* --mico-breakpoint-lg: 992px; Extra large devices (large screens) */
@media screen and (max-width: 992px) {

  .text-left-lg { text-align: var(--mico-text-align-left) !important; }
  .text-center-lg { text-align: var(--mico-text-align-center) !important; }
  .text-right-lg { text-align: var(--mico-text-align-right) !important; }
  .text-justify-lg { text-align: var(--mico-text-align-justify) !important; }
  .text-start-lg { text-align: var(--mico-text-align-start) !important; }
  .text-end-lg { text-align: var(--mico-text-align-end) !important; }

  .mx-auto-lg { margin-inline: auto !important; }
  .my-auto-lg { margin-block: auto !important; }

  .d-none-lg { display: none !important; }
  .d-inline-lg { display: inline !important; }
  .d-inline-block-lg { display: inline-block !important; }
  .d-inline-lg { display: inline !important; }
  .d-inline-block-lg { display: inline-block !important; }
  .d-block-lg { display: block !important; }
  .d-flex-lg { display: flex !important; }
  .d-inline-flex-lg { display: inline-flex !important; }
  .d-grid-lg { display: grid !important; }
  
  .grid-cols-1-lg { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-lg { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-lg { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-lg { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-lg { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-lg { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-lg { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-lg { grid-column: span 1 !important; }
  .col-2-lg { grid-column: span 2 !important; }
  .col-3-lg { grid-column: span 3 !important; }
  .col-4-lg { grid-column: span 4 !important; }
  .col-5-lg { grid-column: span 5 !important; }
  .col-6-lg { grid-column: span 6 !important; }
  .col-7-lg { grid-column: span 7 !important; }
  .col-8-lg { grid-column: span 8 !important; }
  .col-9-lg { grid-column: span 9 !important; }
  .col-10-lg { grid-column: span 10 !important; }
  .col-11-lg { grid-column: span 11 !important; }
  .col-12-lg { grid-column: span 12 !important; }


  /* Flexbox */
.flex-row-lg { flex-direction: row !important; }
.flex-column-lg { flex-direction: column !important; }
.flex-row-reverse-lg { flex-direction: row-reverse !important; }
.flex-column-reverse-lg { flex-direction: column-reverse !important; }


.flex-wrap-lg { flex-wrap: wrap !important; }
.flex-nowrap-lg { flex-wrap: nowrap !important; }
.flex-wrap-reverse-lg { flex-wrap: wrap-reverse !important; }

.justify-content-start-lg { justify-content: flex-start !important; }
.justify-content-end-lg { justify-content: flex-end !important; }
.justify-content-center-lg { justify-content: center !important; }
.justify-content-between-lg { justify-content: space-between !important; }
.justify-content-around-lg { justify-content: space-around !important; }
.justify-content-evenly-lg { justify-content: space-evenly !important; }

.align-items-start-lg { align-items: flex-start !important; }
.align-items-end-lg { align-items: flex-end !important; }
.align-items-center-lg { align-items: center !important; }
.align-items-baseline-lg { align-items: baseline !important; }
.align-items-stretch-lg { align-items: stretch !important; }

.align-content-start-lg { align-content: flex-start !important; }
.align-content-end-lg { align-content: flex-end !important; }
.align-content-center-lg { align-content: center !important; }
.align-content-between-lg { align-content: space-between !important; }
.align-content-around-lg { align-content: space-around !important; }
.align-content-stretch-lg { align-content: stretch !important; }

.align-self-auto-lg { align-self: auto !important; }
.align-self-start-lg { align-self: flex-start !important; }
.align-self-end-lg { align-self: flex-end !important; }
.align-self-center-lg { align-self: center !important; }
.align-self-baseline-lg { align-self: baseline !important; }
.align-self-stretch-lg { align-self: stretch !important; }

.flex-grow-0-lg { flex-grow: 0 !important; }
.flex-grow-1-lg { flex-grow: 1 !important; }
.flex-shrink-0-lg { flex-shrink: 0 !important; }
.flex-shrink-1-lg { flex-shrink: 1 !important; }

.flex-1-lg { flex: 1 1 0% !important; }
.flex-auto-lg { flex: 1 1 auto !important; }
.flex-initial-lg { flex: 0 1 auto !important; }
.flex-none-lg { flex: none !important; }


  /* Width and Height */
.w-none-lg { width: none !important; }
.w-auto-lg { width: auto !important; }
.w-screen-lg { width: 100vw !important; }
.w-10p-lg { width: 10% !important; }
.w-20p-lg { width: 20% !important; }
.w-30p-lg { width: 30% !important; }
.w-40p-lg { width: 40% !important; }
.w-50p-lg { width: 50% !important; }
.w-60p-lg { width: 60% !important; }
.w-70p-lg { width: 70% !important; }
.w-80p-lg { width: 80% !important; }
.w-90p-lg { width: 90% !important; }
.w-100p-lg { width: 100% !important; }

.h-none-lg { height: none !important; }
.h-auto-lg { height: auto !important; }
.h-screen-lg { height: 100vh !important; }
.h-10p-lg { height: 10% !important; }
.h-20p-lg { height: 20% !important; }
.h-30p-lg { height: 30% !important; }
.h-40p-lg { height: 40% !important; }
.h-50p-lg { height: 50% !important; }
.h-60p-lg { height: 60% !important; }
.h-70p-lg { height: 70% !important; }
.h-80p-lg { height: 80% !important; }
.h-90p-lg { height: 90% !important; }
.h-100p-lg { height: 100% !important; }


/* Block Button */
.btn-block-lg {
  display: var(--mico-display-block);
  width: var(--mico-width-full);
}
}

/*  --mico-breakpoint-md: 768px; Medium devices (tablets) */
@media screen and (max-width: 768px) {

  .text-left-md { text-align: var(--mico-text-align-left) !important; }
  .text-center-md { text-align: var(--mico-text-align-center) !important; }
  .text-right-md { text-align: var(--mico-text-align-right) !important; }
  .text-justify-md { text-align: var(--mico-text-align-justify) !important; }
  .text-start-md { text-align: var(--mico-text-align-start) !important; }
  .text-end-md { text-align: var(--mico-text-align-end) !important; }

  .mx-auto-md { margin-inline: auto !important; }
  .my-auto-md { margin-block: auto !important; }

  .d-none-md { display: none !important; }
  .d-inline-md { display: inline !important; }
  .d-inline-block-md { display: inline-block !important; }
  .d-block-md { display: block !important; }
  .d-flex-md { display: flex !important; }
  .d-inline-flex-md { display: inline-flex !important; }
  .d-grid-md { display: grid !important; }
  
  .grid-cols-1-md { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-md { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-md { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-md { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-md { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-md { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-md { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-md { grid-column: span 1 !important; }
  .col-2-md { grid-column: span 2 !important; }
  .col-3-md { grid-column: span 3 !important; }
  .col-4-md { grid-column: span 4 !important; }
  .col-5-md { grid-column: span 5 !important; }
  .col-6-md { grid-column: span 6 !important; }
  .col-7-md { grid-column: span 7 !important; }
  .col-8-md { grid-column: span 8 !important; }
  .col-9-md { grid-column: span 9 !important; }
  .col-10-md { grid-column: span 10 !important; }
  .col-11-md { grid-column: span 11 !important; }
  .col-12-md { grid-column: span 12 !important; }


  /* Flexbox */
.flex-row-md { flex-direction: row !important; }
.flex-column-md { flex-direction: column !important; }
.flex-row-reverse-md { flex-direction: row-reverse !important; }
.flex-column-reverse-md { flex-direction: column-reverse !important; }


.flex-wrap-md { flex-wrap: wrap !important; }
.flex-nowrap-md { flex-wrap: nowrap !important; }
.flex-wrap-reverse-md { flex-wrap: wrap-reverse !important; }

.justify-content-start-md { justify-content: flex-start !important; }
.justify-content-end-md { justify-content: flex-end !important; }
.justify-content-center-md { justify-content: center !important; }
.justify-content-between-md { justify-content: space-between !important; }
.justify-content-around-md { justify-content: space-around !important; }
.justify-content-evenly-md { justify-content: space-evenly !important; }

.align-items-start-md { align-items: flex-start !important; }
.align-items-end-md { align-items: flex-end !important; }
.align-items-center-md { align-items: center !important; }
.align-items-baseline-md { align-items: baseline !important; }
.align-items-stretch-md { align-items: stretch !important; }

.align-content-start-md { align-content: flex-start !important; }
.align-content-end-md { align-content: flex-end !important; }
.align-content-center-md { align-content: center !important; }
.align-content-between-md { align-content: space-between !important; }
.align-content-around-md { align-content: space-around !important; }
.align-content-stretch-md { align-content: stretch !important; }

.align-self-auto-md { align-self: auto !important; }
.align-self-start-md { align-self: flex-start !important; }
.align-self-end-md { align-self: flex-end !important; }
.align-self-center-md { align-self: center !important; }
.align-self-baseline-md { align-self: baseline !important; }
.align-self-stretch-md { align-self: stretch !important; }

.flex-grow-0-md { flex-grow: 0 !important; }
.flex-grow-1-md { flex-grow: 1 !important; }
.flex-shrink-0-md { flex-shrink: 0 !important; }
.flex-shrink-1-md { flex-shrink: 1 !important; }

.flex-1-md { flex: 1 1 0% !important; }
.flex-auto-md { flex: 1 1 auto !important; }
.flex-initial-md { flex: 0 1 auto !important; }
.flex-none-md { flex: none !important; }

  /* Width and Height */
.w-none-md { width: none !important; }
.w-auto-md { width: auto !important; }
.w-screen-md { width: 100vw !important; }
.w-10p-md { width: 10% !important; }
.w-20p-md { width: 20% !important; }
.w-30p-md { width: 30% !important; }
.w-40p-md { width: 40% !important; }
.w-50p-md { width: 50% !important; }
.w-60p-md { width: 60% !important; }
.w-70p-md { width: 70% !important; }
.w-80p-md { width: 80% !important; }
.w-90p-md { width: 90% !important; }
.w-100p-md { width: 100% !important; }

.h-none-md { height: none !important; }
.h-auto-md { height: auto !important; }
.h-screen-md { height: 100vh !important; }
.h-10p-md { height: 10% !important; }
.h-20p-md { height: 20% !important; }
.h-30p-md { height: 30% !important; }
.h-40p-md { height: 40% !important; }
.h-50p-md { height: 50% !important; }
.h-60p-md { height: 60% !important; }
.h-70p-md { height: 70% !important; }
.h-80p-md { height: 80% !important; }
.h-90p-md { height: 90% !important; }
.h-100p-md { height: 100% !important; }


/* Block Button */
.btn-block-md {
  display: var(--mico-display-block);
  width: var(--mico-width-full);
}
}

/*  --mico-breakpoint-sm: 576px; Small devices (large phones, portrait tablets) */
@media screen and (max-width: 576px) {

  .text-left-sm { text-align: var(--mico-text-align-left) !important; }
  .text-center-sm { text-align: var(--mico-text-align-center) !important; }
  .text-right-sm { text-align: var(--mico-text-align-right) !important; }
  .text-justify-sm { text-align: var(--mico-text-align-justify) !important; }
  .text-start-sm { text-align: var(--mico-text-align-start) !important; }
  .text-end-sm { text-align: var(--mico-text-align-end) !important; }

  .mx-auto-sm { margin-inline: auto !important; }
  .my-auto-sm { margin-block: auto !important; }

  .d-none-sm { display: none !important; }
  .d-inline-sm { display: inline !important; }
  .d-inline-block-sm { display: inline-block !important; }
  .d-block-sm { display: block !important; }
  .d-flex-sm { display: flex !important; }
  .d-inline-flex-sm { display: inline-flex !important; }
  .d-grid-sm { display: grid !important; }
  
  .grid-cols-1-sm { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-sm { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-sm { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-sm { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-sm { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-sm { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-sm { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-sm { grid-column: span 1 !important; }
  .col-2-sm { grid-column: span 2 !important; }
  .col-3-sm { grid-column: span 3 !important; }
  .col-4-sm { grid-column: span 4 !important; }
  .col-5-sm { grid-column: span 5 !important; }
  .col-6-sm { grid-column: span 6 !important; }
  .col-7-sm { grid-column: span 7 !important; }
  .col-8-sm { grid-column: span 8 !important; }
  .col-9-sm { grid-column: span 9 !important; }
  .col-10-sm { grid-column: span 10 !important; }
  .col-11-sm { grid-column: span 11 !important; }
  .col-12-sm { grid-column: span 12 !important; }


/* Flexbox */
.flex-row-sm { flex-direction: row !important; }
.flex-column-sm { flex-direction: column !important; }
.flex-row-reverse-sm { flex-direction: row-reverse !important; }
.flex-column-reverse-sm { flex-direction: column-reverse !important; }


.flex-wrap-sm { flex-wrap: wrap !important; }
.flex-nowrap-sm { flex-wrap: nowrap !important; }
.flex-wrap-reverse-sm { flex-wrap: wrap-reverse !important; }

.justify-content-start-sm { justify-content: flex-start !important; }
.justify-content-end-sm { justify-content: flex-end !important; }
.justify-content-center-sm { justify-content: center !important; }
.justify-content-between-sm { justify-content: space-between !important; }
.justify-content-around-sm { justify-content: space-around !important; }
.justify-content-evenly-sm { justify-content: space-evenly !important; }

.align-items-start-sm { align-items: flex-start !important; }
.align-items-end-sm { align-items: flex-end !important; }
.align-items-center-sm { align-items: center !important; }
.align-items-baseline-sm { align-items: baseline !important; }
.align-items-stretch-sm { align-items: stretch !important; }

.align-content-start-sm { align-content: flex-start !important; }
.align-content-end-sm { align-content: flex-end !important; }
.align-content-center-sm { align-content: center !important; }
.align-content-between-sm { align-content: space-between !important; }
.align-content-around-sm { align-content: space-around !important; }
.align-content-stretch-sm { align-content: stretch !important; }

.align-self-auto-sm { align-self: auto !important; }
.align-self-start-sm { align-self: flex-start !important; }
.align-self-end-sm { align-self: flex-end !important; }
.align-self-center-sm { align-self: center !important; }
.align-self-baseline-sm { align-self: baseline !important; }
.align-self-stretch-md { align-self: stretch !important; }

.flex-grow-0-sm { flex-grow: 0 !important; }
.flex-grow-1-sm { flex-grow: 1 !important; }
.flex-shrink-0-sm { flex-shrink: 0 !important; }
.flex-shrink-1-sm { flex-shrink: 1 !important; }

.flex-1-sm { flex: 1 1 0% !important; }
.flex-auto-sm { flex: 1 1 auto !important; }
.flex-initial-sm { flex: 0 1 auto !important; }
.flex-none-sm { flex: none !important; }

  /* Width and Height */
.w-none-sm { width: none !important; }
.w-auto-sm { width: auto !important; }
.w-screen-sm { width: 100vw !important; }
.w-10p-sm { width: 10% !important; }
.w-20p-sm { width: 20% !important; }
.w-30p-sm { width: 30% !important; }
.w-40p-sm { width: 40% !important; }
.w-50p-sm { width: 50% !important; }
.w-60p-sm { width: 60% !important; }
.w-70p-sm { width: 70% !important; }
.w-80p-sm { width: 80% !important; }
.w-90p-sm { width: 90% !important; }
.w-100p-sm { width: 100% !important; }

.h-none-sm { height: none !important; }
.h-auto-sm { height: auto !important; }
.h-screen-sm { height: 100vh !important; }
.h-10p-sm { height: 10% !important; }
.h-20p-sm { height: 20% !important; }
.h-30p-sm { height: 30% !important; }
.h-40p-sm { height: 40% !important; }
.h-50p-sm { height: 50% !important; }
.h-60p-sm { height: 60% !important; }
.h-70p-sm { height: 70% !important; }
.h-80p-sm { height: 80% !important; }
.h-90p-sm { height: 90% !important; }
.h-100p-sm { height: 100% !important; }


/* Block Button */
.btn-block-sm {
  display: var(--mico-display-block);
  width: var(--mico-width-full);
}
}

/*  --mico-breakpoint-xs: 320px; * Extra small devices (phones) */
@media screen and (max-width: 320px) {
  
/* ====================================================================== */
/* TYPOGRAPHY UTILITIES                                                    */
/* ====================================================================== */

/**
 * Text Alignment
 */

  .text-left-xs { text-align: var(--mico-text-align-left) !important; }
  .text-center-xs { text-align: var(--mico-text-align-center) !important; }
  .text-right-xs { text-align: var(--mico-text-align-right) !important; }
  .text-justify-xs { text-align: var(--mico-text-align-justify) !important; }
  .text-start-xs { text-align: var(--mico-text-align-start) !important; }
  .text-end-xs { text-align: var(--mico-text-align-end) !important; }

  .mx-auto-xs { margin-inline: auto !important; }
  .my-auto-xs { margin-block: auto !important; }

  .d-none-xs { display: none !important; }
  .d-inline-xs { display: inline !important; }
  .d-inline-block-xs { display: inline-block !important; }
  .d-block-xs { display: block !important; }
  .d-flex-xs { display: flex !important; }
  .d-inline-flex-xs { display: inline-flex !important; }
  .d-grid-xs { display: grid !important; }
  
  .grid-cols-1-xs { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-xs { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-xs { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-xs { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-xs { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-xs { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-xs { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-xs { grid-column: span 1 !important; }
  .col-2-xs { grid-column: span 2 !important; }
  .col-3-xs { grid-column: span 3 !important; }
  .col-4-xs { grid-column: span 4 !important; }
  .col-5-xs { grid-column: span 5 !important; }
  .col-6-xs { grid-column: span 6 !important; }
  .col-7-xs { grid-column: span 7 !important; }
  .col-8-xs { grid-column: span 8 !important; }
  .col-9-xs { grid-column: span 9 !important; }
  .col-10-xs { grid-column: span 10 !important; }
  .col-11-xs { grid-column: span 11 !important; }
  .col-12-xs { grid-column: span 12 !important; }


  /* Flexbox */
.flex-row-xs { flex-direction: row !important; }
.flex-column-xs { flex-direction: column !important; }
.flex-row-reverse-xs { flex-direction: row-reverse !important; }
.flex-column-reverse-xs { flex-direction: column-reverse !important; }


.flex-wrap-xs { flex-wrap: wrap !important; }
.flex-nowrap-xs { flex-wrap: nowrap !important; }
.flex-wrap-reverse-xs { flex-wrap: wrap-reverse !important; }

.justify-content-start-xs { justify-content: flex-start !important; }
.justify-content-end-xs { justify-content: flex-end !important; }
.justify-content-center-xs { justify-content: center !important; }
.justify-content-between-xs { justify-content: space-between !important; }
.justify-content-around-xs { justify-content: space-around !important; }
.justify-content-evenly-xs { justify-content: space-evenly !important; }

.justify-items-start-xs { justify-items: flex-start !important; }
.justify-items-end-xs { justify-items: flex-end !important; }
.justify-items-center-xs { justify-items: center !important; }

.align-items-start-xs { align-items: flex-start !important; }
.align-items-end-xs { align-items: flex-end !important; }
.align-items-center-xs { align-items: center !important; }
.align-items-baseline-xs { align-items: baseline !important; }
.align-items-stretch-xs { align-items: stretch !important; }

.align-content-start-xs { align-content: flex-start !important; }
.align-content-end-xs { align-content: flex-end !important; }
.align-content-center-xs { align-content: center !important; }
.align-content-between-xs { align-content: space-between !important; }
.align-content-around-xs { align-content: space-around !important; }
.align-content-stretch-xs { align-content: stretch !important; }

.align-self-auto-xs { align-self: auto !important; }
.align-self-start-xs { align-self: flex-start !important; }
.align-self-end-xs { align-self: flex-end !important; }
.align-self-center-xs { align-self: center !important; }
.align-self-baseline-xs { align-self: baseline !important; }
.align-self-stretch-xs { align-self: stretch !important; }

.flex-grow-0-xs { flex-grow: 0 !important; }
.flex-grow-1-xs { flex-grow: 1 !important; }
.flex-shrink-0-xs { flex-shrink: 0 !important; }
.flex-shrink-1-xs { flex-shrink: 1 !important; }

.flex-1-xs { flex: 1 1 0% !important; }
.flex-auto-xs { flex: 1 1 auto !important; }
.flex-initial-xs { flex: 0 1 auto !important; }
.flex-none-xs { flex: none !important; }

  /* Width and Height */
.w-none-xs { width: none !important; }
.w-auto-xs { width: auto !important; }
.w-screen-xs { width: 100vw !important; }
.w-10p-xs { width: 10% !important; }
.w-20p-xs { width: 20% !important; }
.w-30p-xs { width: 30% !important; }
.w-40p-xs { width: 40% !important; }
.w-50p-xs { width: 50% !important; }
.w-60p-xs { width: 60% !important; }
.w-70p-xs { width: 70% !important; }
.w-80p-xs { width: 80% !important; }
.w-90p-xs { width: 90% !important; }
.w-100p-xs { width: 100% !important; }

.h-none-xs { height: none !important; }
.h-auto-xs { height: auto !important; }
.h-screen-xs { height: 100vh !important; }
.h-10p-xs { height: 10% !important; }
.h-20p-xs { height: 20% !important; }
.h-30p-xs { height: 30% !important; }
.h-40p-xs { height: 40% !important; }
.h-50p-xs { height: 50% !important; }
.h-60p-xs { height: 60% !important; }
.h-70p-xs { height: 70% !important; }
.h-80p-xs { height: 80% !important; }
.h-90p-xs { height: 90% !important; }
.h-100p-xs { height: 100% !important; }


/* Block Button */
.btn-block-xs {
  display: var(--mico-display-block);
  width: var(--mico-width-full);
}

}