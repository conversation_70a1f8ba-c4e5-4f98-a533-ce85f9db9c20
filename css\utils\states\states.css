/**
 * Mico CSS Framework - Interactive States
 *
 * This file defines utility classes for interactive states (hover, focus, active).
 * These classes follow a consistent naming pattern for easy use and extension.
 *
 * NAMING CONVENTION:
 * - Hover: .hover-{property}-{value}
 * - Focus: .focus-{property}-{value}
 * - Active: .active-{property}-{value}
 *
 * USAGE EXAMPLES:
 * <button class="hover-bg-primary">Hover for primary background</button>
 * <a class="hover-text-accent focus-text-primary">Interactive link</a>
 */

/* ========================================================================== */
/* BASE STATE UTILITIES                                                       */
/* ========================================================================== */

/**
 * Base transition for all interactive state classes
 * This ensures smooth transitions when states change
 */
[class^="hover-"], [class*=" hover-"],
[class^="focus-"], [class*=" focus-"],
[class^="active-"], [class*=" active-"] {
    transition: var(--mico-transition-all);
}

/* ========================================================================== */
/* HOVER STATE UTILITIES                                                      */
/* ========================================================================== */

/**
 * BACKGROUND COLOR HOVER STATES
 *
 * These utilities apply background colors on hover
 */

/* ---------- Brand Colors ---------- */
.hover-bg-primary:hover { background-color: var(--mico-color-primary) !important; }
.hover-bg-secondary:hover { background-color: var(--mico-color-secondary) !important; }
.hover-bg-accent:hover { background-color: var(--mico-color-accent) !important; }

/* ---------- Primary Color Variations ---------- */
/* Tints - Lighter variations */
.hover-bg-primary-2xlight:hover { background-color: var(--mico-color-primary-2xlight) !important; }
.hover-bg-primary-3xlight:hover { background-color: var(--mico-color-primary-3xlight) !important; }
.hover-bg-primary-4xlight:hover { background-color: var(--mico-color-primary-4xlight) !important; }
.hover-bg-primary-5xlight:hover { background-color: var(--mico-color-primary-5xlight) !important; }

/* Shades - Darker variations */
.hover-bg-primary-2xdark:hover { background-color: var(--mico-color-primary-2xdark) !important; }
.hover-bg-primary-3xdark:hover { background-color: var(--mico-color-primary-3xdark) !important; }
.hover-bg-primary-4xdark:hover { background-color: var(--mico-color-primary-4xdark) !important; }
.hover-bg-primary-5xdark:hover { background-color: var(--mico-color-primary-5xdark) !important; }

/* ---------- Secondary Color Variations ---------- */
/* Tints - Lighter variations */
.hover-bg-secondary-2xlight:hover { background-color: var(--mico-color-secondary-2xlight) !important; }
.hover-bg-secondary-3xlight:hover { background-color: var(--mico-color-secondary-3xlight) !important; }
.hover-bg-secondary-4xlight:hover { background-color: var(--mico-color-secondary-4xlight) !important; }
.hover-bg-secondary-5xlight:hover { background-color: var(--mico-color-secondary-5xlight) !important; }

/* Shades - Darker variations */
.hover-bg-secondary-2xdark:hover { background-color: var(--mico-color-secondary-2xdark) !important; }
.hover-bg-secondary-3xdark:hover { background-color: var(--mico-color-secondary-3xdark) !important; }
.hover-bg-secondary-4xdark:hover { background-color: var(--mico-color-secondary-4xdark) !important; }
.hover-bg-secondary-5xdark:hover { background-color: var(--mico-color-secondary-5xdark) !important; }

/* ---------- Accent Color Variations ---------- */
/* Tints - Lighter variations */
.hover-bg-accent-2xlight:hover { background-color: var(--mico-color-accent-2xlight) !important; }
.hover-bg-accent-3xlight:hover { background-color: var(--mico-color-accent-3xlight) !important; }
.hover-bg-accent-4xlight:hover { background-color: var(--mico-color-accent-4xlight) !important; }
.hover-bg-accent-5xlight:hover { background-color: var(--mico-color-accent-5xlight) !important; }

/* Shades - Darker variations */
.hover-bg-accent-2xdark:hover { background-color: var(--mico-color-accent-2xdark) !important; }
.hover-bg-accent-3xdark:hover { background-color: var(--mico-color-accent-3xdark) !important; }
.hover-bg-accent-4xdark:hover { background-color: var(--mico-color-accent-4xdark) !important; }
.hover-bg-accent-5xdark:hover { background-color: var(--mico-color-accent-5xdark) !important; }

/* ---------- Neutral Colors - OKLCH System ---------- */
/* Gray Color Variations */
.hover-bg-gray:hover { background-color: var(--mico-color-gray) !important; }
.hover-bg-gray-2xlight:hover { background-color: var(--mico-color-gray-2xlight) !important; }
.hover-bg-gray-3xlight:hover { background-color: var(--mico-color-gray-3xlight) !important; }
.hover-bg-gray-4xlight:hover { background-color: var(--mico-color-gray-4xlight) !important; }
.hover-bg-gray-5xlight:hover { background-color: var(--mico-color-gray-5xlight) !important; }
.hover-bg-gray-2xdark:hover { background-color: var(--mico-color-gray-2xdark) !important; }
.hover-bg-gray-3xdark:hover { background-color: var(--mico-color-gray-3xdark) !important; }
.hover-bg-gray-4xdark:hover { background-color: var(--mico-color-gray-4xdark) !important; }
.hover-bg-gray-5xdark:hover { background-color: var(--mico-color-gray-5xdark) !important; }

/* Black Color Variations */
.hover-bg-black:hover { background-color: var(--mico-color-black) !important; }
.hover-bg-black-2xlight:hover { background-color: var(--mico-color-black-2xlight) !important; }
.hover-bg-black-3xlight:hover { background-color: var(--mico-color-black-3xlight) !important; }
.hover-bg-black-4xlight:hover { background-color: var(--mico-color-black-4xlight) !important; }
.hover-bg-black-5xlight:hover { background-color: var(--mico-color-black-5xlight) !important; }
.hover-bg-black-2xdark:hover { background-color: var(--mico-color-black-2xdark) !important; }
.hover-bg-black-3xdark:hover { background-color: var(--mico-color-black-3xdark) !important; }
.hover-bg-black-4xdark:hover { background-color: var(--mico-color-black-4xdark) !important; }
.hover-bg-black-5xdark:hover { background-color: var(--mico-color-black-5xdark) !important; }

/* White Color Variations */
.hover-bg-white:hover { background-color: var(--mico-color-white) !important; }
.hover-bg-white-2xlight:hover { background-color: var(--mico-color-white-2xlight) !important; }
.hover-bg-white-3xlight:hover { background-color: var(--mico-color-white-3xlight) !important; }
.hover-bg-white-4xlight:hover { background-color: var(--mico-color-white-4xlight) !important; }
.hover-bg-white-5xlight:hover { background-color: var(--mico-color-white-5xlight) !important; }
.hover-bg-white-2xdark:hover { background-color: var(--mico-color-white-2xdark) !important; }
.hover-bg-white-3xdark:hover { background-color: var(--mico-color-white-3xdark) !important; }
.hover-bg-white-4xdark:hover { background-color: var(--mico-color-white-4xdark) !important; }
.hover-bg-white-5xdark:hover { background-color: var(--mico-color-white-5xdark) !important; }

/* ---------- Creative Transparency Variations ---------- */
.hover-bg-black-whisper:hover { background-color: var(--mico-color-black-whisper) !important; }
.hover-bg-black-breath:hover { background-color: var(--mico-color-black-breath) !important; }
.hover-bg-black-mist:hover { background-color: var(--mico-color-black-mist) !important; }
.hover-bg-black-veil:hover { background-color: var(--mico-color-black-veil) !important; }
.hover-bg-black-shadow:hover { background-color: var(--mico-color-black-shadow) !important; }
.hover-bg-black-shroud:hover { background-color: var(--mico-color-black-shroud) !important; }
.hover-bg-black-cloak:hover { background-color: var(--mico-color-black-cloak) !important; }
.hover-bg-black-eclipse:hover { background-color: var(--mico-color-black-eclipse) !important; }
.hover-bg-black-void:hover { background-color: var(--mico-color-black-void) !important; }

.hover-bg-white-whisper:hover { background-color: var(--mico-color-white-whisper) !important; }
.hover-bg-white-breath:hover { background-color: var(--mico-color-white-breath) !important; }
.hover-bg-white-mist:hover { background-color: var(--mico-color-white-mist) !important; }
.hover-bg-white-veil:hover { background-color: var(--mico-color-white-veil) !important; }
.hover-bg-white-shadow:hover { background-color: var(--mico-color-white-shadow) !important; }

/* ---------- State Colors ---------- */
.hover-bg-success:hover { background-color: var(--mico-color-success) !important; }
.hover-bg-warning:hover { background-color: var(--mico-color-warning) !important; }
.hover-bg-error:hover { background-color: var(--mico-color-error) !important; }
.hover-bg-info:hover { background-color: var(--mico-color-info) !important; }

/**
 * TEXT COLOR HOVER STATES
 *
 * These utilities apply text colors on hover
 */
.hover-text-primary:hover { color: var(--mico-color-primary) !important; }
.hover-text-secondary:hover { color: var(--mico-color-secondary) !important; }
.hover-text-accent:hover { color: var(--mico-color-accent) !important; }


/* ========================================================================== */
/* FOCUS STATE UTILITIES                                                      */
/* ========================================================================== */

/**
 * BACKGROUND COLOR FOCUS STATES
 *
 * These utilities apply background colors on focus
 */

/* ---------- Brand Colors ---------- */
.focus-bg-primary:focus { background-color: var(--mico-color-primary) !important; }
.focus-bg-secondary:focus { background-color: var(--mico-color-secondary) !important; }
.focus-bg-accent:focus { background-color: var(--mico-color-accent) !important; }

/* ---------- Primary Color Variations ---------- */
/* Tints - Lighter variations */
.focus-bg-primary-2xlight:focus { background-color: var(--mico-color-primary-2xlight) !important; }
.focus-bg-primary-3xlight:focus { background-color: var(--mico-color-primary-3xlight) !important; }
.focus-bg-primary-4xlight:focus { background-color: var(--mico-color-primary-4xlight) !important; }
.focus-bg-primary-5xlight:focus { background-color: var(--mico-color-primary-5xlight) !important; }

/* Shades - Darker variations */
.focus-bg-primary-2xdark:focus { background-color: var(--mico-color-primary-2xdark) !important; }
.focus-bg-primary-3xdark:focus { background-color: var(--mico-color-primary-3xdark) !important; }
.focus-bg-primary-4xdark:focus { background-color: var(--mico-color-primary-4xdark) !important; }
.focus-bg-primary-5xdark:focus { background-color: var(--mico-color-primary-5xdark) !important; }

/* ---------- Secondary Color Variations ---------- */
/* Tints - Lighter variations */
.focus-bg-secondary-2xlight:focus { background-color: var(--mico-color-secondary-2xlight) !important; }
.focus-bg-secondary-3xlight:focus { background-color: var(--mico-color-secondary-3xlight) !important; }
.focus-bg-secondary-4xlight:focus { background-color: var(--mico-color-secondary-4xlight) !important; }
.focus-bg-secondary-5xlight:focus { background-color: var(--mico-color-secondary-5xlight) !important; }

/* Shades - Darker variations */
.focus-bg-secondary-2xdark:focus { background-color: var(--mico-color-secondary-2xdark) !important; }
.focus-bg-secondary-3xdark:focus { background-color: var(--mico-color-secondary-3xdark) !important; }
.focus-bg-secondary-4xdark:focus { background-color: var(--mico-color-secondary-4xdark) !important; }
.focus-bg-secondary-5xdark:focus { background-color: var(--mico-color-secondary-5xdark) !important; }

/* ---------- Accent Color Variations ---------- */
/* Tints - Lighter variations */
.focus-bg-accent-2xlight:focus { background-color: var(--mico-color-accent-2xlight) !important; }
.focus-bg-accent-3xlight:focus { background-color: var(--mico-color-accent-3xlight) !important; }
.focus-bg-accent-4xlight:focus { background-color: var(--mico-color-accent-4xlight) !important; }
.focus-bg-accent-5xlight:focus { background-color: var(--mico-color-accent-5xlight) !important; }

/* Shades - Darker variations */
.focus-bg-accent-2xdark:focus { background-color: var(--mico-color-accent-2xdark) !important; }
.focus-bg-accent-3xdark:focus { background-color: var(--mico-color-accent-3xdark) !important; }
.focus-bg-accent-4xdark:focus { background-color: var(--mico-color-accent-4xdark) !important; }
.focus-bg-accent-5xdark:focus { background-color: var(--mico-color-accent-5xdark) !important; }


/* ---------- Grayscale ---------- */
.focus-bg-gray-100:focus { background-color: var(--mico-color-gray-100) !important; }
.focus-bg-gray-200:focus { background-color: var(--mico-color-gray-200) !important; }
.focus-bg-gray-300:focus { background-color: var(--mico-color-gray-300) !important; }
.focus-bg-gray-400:focus { background-color: var(--mico-color-gray-400) !important; }
.focus-bg-gray-500:focus { background-color: var(--mico-color-gray-500) !important; }
.focus-bg-gray-600:focus { background-color: var(--mico-color-gray-600) !important; }
.focus-bg-gray-700:focus { background-color: var(--mico-color-gray-700) !important; }
.focus-bg-gray-800:focus { background-color: var(--mico-color-gray-800) !important; }
.focus-bg-gray-900:focus { background-color: var(--mico-color-gray-900) !important; }

/* ---------- Black Scale ---------- */
.focus-bg-black-100:focus { background-color: var(--mico-color-black-100) !important; }
.focus-bg-black-200:focus { background-color: var(--mico-color-black-200) !important; }
.focus-bg-black-300:focus { background-color: var(--mico-color-black-300) !important; }
.focus-bg-black-400:focus { background-color: var(--mico-color-black-400) !important; }
.focus-bg-black-500:focus { background-color: var(--mico-color-black-500) !important; }
.focus-bg-black-600:focus { background-color: var(--mico-color-black-600) !important; }
.focus-bg-black-700:focus { background-color: var(--mico-color-black-700) !important; }
.focus-bg-black-800:focus { background-color: var(--mico-color-black-800) !important; }
.focus-bg-black-900:focus { background-color: var(--mico-color-black-900) !important; }

/* ---------- Transparency ---------- */
.focus-bg-black-trans-100:focus { background-color: var(--mico-color-black-trans-100) !important; }
.focus-bg-black-trans-200:focus { background-color: var(--mico-color-black-trans-200) !important; }
.focus-bg-black-trans-300:focus { background-color: var(--mico-color-black-trans-300) !important; }
.focus-bg-black-trans-400:focus { background-color: var(--mico-color-black-trans-400) !important; }
.focus-bg-black-trans-500:focus { background-color: var(--mico-color-black-trans-500) !important; }
.focus-bg-black-trans-600:focus { background-color: var(--mico-color-black-trans-600) !important; }
.focus-bg-black-trans-700:focus { background-color: var(--mico-color-black-trans-700) !important; }
.focus-bg-black-trans-800:focus { background-color: var(--mico-color-black-trans-800) !important; }
.focus-bg-black-trans-900:focus { background-color: var(--mico-color-black-trans-900) !important; }

/* ---------- State Colors ---------- */
.focus-bg-success:focus { background-color: var(--mico-color-success) !important; }
.focus-bg-warning:focus { background-color: var(--mico-color-warning) !important; }
.focus-bg-error:focus { background-color: var(--mico-color-error) !important; }
.focus-bg-info:focus { background-color: var(--mico-color-info) !important; }

/**
 * TEXT COLOR FOCUS STATES
 *
 * These utilities apply text colors on focus
 */
.focus-text-primary:focus { color: var(--mico-color-primary) !important; }
.focus-text-secondary:focus { color: var(--mico-color-secondary) !important; }
.focus-text-accent:focus { color: var(--mico-color-accent) !important; }

/* ========================================================================== */
/* ACTIVE STATE UTILITIES                                                     */
/* ========================================================================== */

/**
 * BACKGROUND COLOR ACTIVE STATES
 *
 * These utilities apply background colors on active (pressed) state
 */

/* ---------- Brand Colors ---------- */
.active-bg-primary:active { background-color: var(--mico-color-primary) !important; }
.active-bg-secondary:active { background-color: var(--mico-color-secondary) !important; }
.active-bg-accent:active { background-color: var(--mico-color-accent) !important; }

/* ---------- Primary Color Variations ---------- */
/* Tints - Lighter variations */
.active-bg-primary-light:active { background-color: var(--mico-color-primary-light) !important; }
.active-bg-primary-2xlight:active { background-color: var(--mico-color-primary-2xlight) !important; }
.active-bg-primary-3xlight:active { background-color: var(--mico-color-primary-3xlight) !important; }
.active-bg-primary-4xlight:active { background-color: var(--mico-color-primary-4xlight) !important; }
.active-bg-primary-5xlight:active { background-color: var(--mico-color-primary-5xlight) !important; }

/* Shades - Darker variations */
.active-bg-primary-dark:active { background-color: var(--mico-color-primary-dark) !important; }
.active-bg-primary-2xdark:active { background-color: var(--mico-color-primary-2xdark) !important; }
.active-bg-primary-3xdark:active { background-color: var(--mico-color-primary-3xdark) !important; }
.active-bg-primary-4xdark:active { background-color: var(--mico-color-primary-4xdark) !important; }
.active-bg-primary-5xdark:active { background-color: var(--mico-color-primary-5xdark) !important; }

/* ---------- Secondary Color Variations ---------- */
/* Tints - Lighter variations */
.active-bg-secondary-light:active { background-color: var(--mico-color-secondary-light) !important; }
.active-bg-secondary-2xlight:active { background-color: var(--mico-color-secondary-2xlight) !important; }
.active-bg-secondary-3xlight:active { background-color: var(--mico-color-secondary-3xlight) !important; }
.active-bg-secondary-4xlight:active { background-color: var(--mico-color-secondary-4xlight) !important; }
.active-bg-secondary-5xlight:active { background-color: var(--mico-color-secondary-5xlight) !important; }

/* Shades - Darker variations */
.active-bg-secondary-dark:active { background-color: var(--mico-color-secondary-dark) !important; }
.active-bg-secondary-2xdark:active { background-color: var(--mico-color-secondary-2xdark) !important; }
.active-bg-secondary-3xdark:active { background-color: var(--mico-color-secondary-3xdark) !important; }
.active-bg-secondary-4xdark:active { background-color: var(--mico-color-secondary-4xdark) !important; }
.active-bg-secondary-5xdark:active { background-color: var(--mico-color-secondary-5xdark) !important; }

/* ---------- Accent Color Variations ---------- */
/* Tints - Lighter variations */
.active-bg-accent-light:active { background-color: var(--mico-color-accent-light) !important; }
.active-bg-accent-2xlight:active { background-color: var(--mico-color-accent-2xlight) !important; }
.active-bg-accent-3xlight:active { background-color: var(--mico-color-accent-3xlight) !important; }
.active-bg-accent-4xlight:active { background-color: var(--mico-color-accent-4xlight) !important; }
.active-bg-accent-5xlight:active { background-color: var(--mico-color-accent-5xlight) !important; }

/* Shades - Darker variations */
.active-bg-accent-dark:active { background-color: var(--mico-color-accent-dark) !important; }
.active-bg-accent-2xdark:active { background-color: var(--mico-color-accent-2xdark) !important; }
.active-bg-accent-3xdark:active { background-color: var(--mico-color-accent-3xdark) !important; }
.active-bg-accent-4xdark:active { background-color: var(--mico-color-accent-4xdark) !important; }
.active-bg-accent-5xdark:active { background-color: var(--mico-color-accent-5xdark) !important; }

/* ========================================================================== */
/* BORDER STATE UTILITIES                                                    */
/* ========================================================================== */

/**
 * Border Color States
 *
 * These utilities change border colors on hover, focus, and active states.
 */

/* Hover Border Colors */
.hover-border-primary:hover { border-color: var(--mico-color-primary) !important; }
.hover-border-secondary:hover { border-color: var(--mico-color-secondary) !important; }
.hover-border-accent:hover { border-color: var(--mico-color-accent) !important; }
.hover-border-success:hover { border-color: var(--mico-color-success) !important; }
.hover-border-error:hover { border-color: var(--mico-color-error) !important; }
.hover-border-warning:hover { border-color: var(--mico-color-warning) !important; }
.hover-border-info:hover { border-color: var(--mico-color-info) !important; }
.hover-border-gray-300:hover { border-color: var(--mico-color-gray-300) !important; }
.hover-border-gray-500:hover { border-color: var(--mico-color-gray-500) !important; }
.hover-border-gray-700:hover { border-color: var(--mico-color-gray-700) !important; }

/* Focus Border Colors */
.focus-border-primary:focus { border-color: var(--mico-color-primary) !important; }
.focus-border-secondary:focus { border-color: var(--mico-color-secondary) !important; }
.focus-border-accent:focus { border-color: var(--mico-color-accent) !important; }
.focus-border-success:focus { border-color: var(--mico-color-success) !important; }
.focus-border-error:focus { border-color: var(--mico-color-error) !important; }
.focus-border-warning:focus { border-color: var(--mico-color-warning) !important; }
.focus-border-info:focus { border-color: var(--mico-color-info) !important; }

/* Active Border Colors */
.active-border-primary:active { border-color: var(--mico-color-primary-2xdark) !important; }
.active-border-secondary:active { border-color: var(--mico-color-secondary-2xdark) !important; }
.active-border-accent:active { border-color: var(--mico-color-accent-2xdark) !important; }

/* ========================================================================== */
/* SHADOW STATE UTILITIES                                                    */
/* ========================================================================== */

/**
 * Shadow States
 *
 * These utilities change box shadows on hover, focus, and active states.
 */

/* Hover Shadows */

.shadow-primary:hover { box-shadow: var(--mico-shadow-hover-primary) !important; }
.shadow-secondary:hover { box-shadow: var(--mico-shadow-hover-secondary) !important; }
.shadow-accent:hover { box-shadow: var(--mico-shadow-hover-accent) !important; }

.hover-shadow-none:hover { box-shadow: var(--mico-value-none) !important; }
.hover-shadow-light:hover { box-shadow: var(--mico-shadow-xs-light) !important; }
.hover-shadow-dark:hover { box-shadow: var(--mico-shadow-xs-dark) !important; }
.hover-shadow-primary:hover { box-shadow: var(--mico-shadow-hover-primary) !important; }
.hover-shadow-secondary:hover { box-shadow: var(--mico-shadow-hover-secondary) !important; }
.hover-shadow-accent:hover { box-shadow: var(--mico-shadow-hover-accent) !important; }

/* Focus Shadows */

.shadow-primary:focus { box-shadow: var(--mico-shadow-focus-primary) !important; }
.shadow-secondary:focus { box-shadow: var(--mico-shadow-focus-secondary) !important; }
.shadow-accent:focus { box-shadow: var(--mico-shadow-focus-accent) !important; }

.focus-shadow-none:focus { box-shadow: var(--mico-value-none) !important; }
.focus-shadow-light:focus { box-shadow: var(--mico-shadow-sm-light) !important; }
.focus-shadow-dark:focus { box-shadow: var(--mico-shadow-sm-dark) !important; }
.focus-shadow-primary:focus { box-shadow: var(--mico-shadow-focus-primary) !important; }
.focus-shadow-secondary:focus { box-shadow: var(--mico-shadow-focus-secondary) !important; }
.focus-shadow-accent:focus { box-shadow: var(--mico-shadow-focus-accent) !important; }

/* Active Shadows */
.shadow-primary:active { box-shadow: var(--mico-shadow-active-primary) !important; }
.shadow-secondary:active { box-shadow: var(--mico-shadow-active-secondary) !important; }
.shadow-accent:active { box-shadow: var(--mico-shadow-active-accent) !important; }

.active-shadow-none:active { box-shadow: var(--mico-value-none) !important; }
.active-shadow-light:active { box-shadow: var(--mico-shadow-md-light) !important; }
.active-shadow-dark:active { box-shadow: var(--mico-shadow-md-dark) !important; }
.active-shadow-primary:active { box-shadow: var(--mico-shadow-active-primary) !important; }
.active-shadow-secondary:active { box-shadow: var(--mico-shadow-active-secondary) !important; }
.active-shadow-accent:active { box-shadow: var(--mico-shadow-active-accent) !important; }

/* ========================================================================== */
/* TRANSFORM STATE UTILITIES                                                 */
/* ========================================================================== */

/**
 * Transform States
 *
 * These utilities apply transforms on hover, focus, and active states.
 */

/* Hover Effects */
.hover-translate-up-xs:hover { transform: translateY(-1.8px); }
.hover-translate-up-sm:hover { transform: translateY(-2.5px); }
.hover-translate-up-md:hover { transform: translateY(-3.8px); }
.hover-translate-up-lg:hover { transform: translateY(-4.8px); }
.hover-translate-up-xl:hover { transform: translateY(-5.8px); }

.hover-translate-down-xs:hover { transform: translateY(1.8px); }
.hover-translate-down-sm:hover { transform: translateY(2.5px); }
.hover-translate-down-md:hover { transform: translateY(3.8px); }
.hover-translate-down-lg:hover { transform: translateY(4.8px); }
.hover-translate-down-xl:hover { transform: translateY(5.8px); }

.hover-scale-up-xs:hover { transform: scale(0.05); }
.hover-scale-up-sm:hover { transform: scale(1.05); }
.hover-scale-up-md:hover { transform: scale(2.05); }
.hover-scale-up-lg:hover { transform: scale(3.05); }
.hover-scale-up-xl:hover { transform: scale(4.05); }

.hover-scale-down-xs:hover { transform: scale(-0.05); }
.hover-scale-down-sm:hover { transform: scale(-1.05); }
.hover-scale-down-md:hover { transform: scale(-2.05); }
.hover-scale-down-lg:hover { transform: scale(-3.05); }
.hover-scale-down-xl:hover { transform: scale(-4.05); }

.hover-rotate-l-xs:hover { transform: rotate(1deg); }
.hover-rotate-l-sm:hover { transform: rotate(3deg); }
.hover-rotate-l-md:hover { transform: rotate(6deg); }
.hover-rotate-l-lg:hover { transform: rotate(9deg); }
.hover-rotate-l-xl:hover { transform: rotate(12deg); }

.hover-rotate-r-xs:hover { transform: rotate(1deg); }
.hover-rotate-r-sm:hover { transform: rotate(3deg); }
.hover-rotate-r-md:hover { transform: rotate(6deg); }
.hover-rotate-r-lg:hover { transform: rotate(9deg); }
.hover-rotate-r-xl:hover { transform: rotate(12deg); }

.hover-rotate-l-25:hover { transform: rotate(25deg); }
.hover-rotate-l-45:hover { transform: rotate(45deg); }
.hover-rotate-l-90:hover { transform: rotate(90deg); }
.hover-rotate-l-180:hover { transform: rotate(180deg); }
.hover-rotate-l-360:hover { transform: rotate(360deg); }

.hover-rotate-r-25:hover { transform: rotate(25deg); }
.hover-rotate-r-45:hover { transform: rotate(45deg); }
.hover-rotate-r-90:hover { transform: rotate(90deg); }
.hover-rotate-r-180:hover { transform: rotate(180deg); }
.hover-rotate-r-360:hover { transform: rotate(360deg); }


/* Focus Effects */
.focus-translate-up-xs:focus { transform: translateY(-1.8px); }
.focus-translate-up-sm:focus { transform: translateY(-2.5px); }
.focus-translate-up-md:focus { transform: translateY(-3.8px); }
.focus-translate-up-lg:focus { transform: translateY(-4.8px); }
.focus-translate-up-xl:focus { transform: translateY(-5.8px); }

.focus-translate-down-xs:focus { transform: translateY(1.8px); }
.focus-translate-down-sm:focus { transform: translateY(2.5px); }
.focus-translate-down-md:focus { transform: translateY(3.8px); }
.focus-translate-down-lg:focus { transform: translateY(4.8px); }
.focus-translate-down-xl:focus { transform: translateY(5.8px); }

.focus-scale-up-xs:focus { transform: scale(0.05); }
.focus-scale-up-sm:focus { transform: scale(1.05); }
.focus-scale-up-md:focus { transform: scale(2.05); }
.focus-scale-up-lg:focus { transform: scale(3.05); }
.focus-scale-up-xl:focus { transform: scale(4.05); }

.focus-scale-down-xs:focus { transform: scale(-0.05); }
.focus-scale-down-sm:focus { transform: scale(-1.05); }
.focus-scale-down-md:focus { transform: scale(-2.05); }
.focus-scale-down-lg:focus { transform: scale(-3.05); }
.focus-scale-down-xl:focus { transform: scale(-4.05); }

.focus-rotate-l-xs:focus { transform: rotate(1deg); }
.focus-rotate-l-sm:focus { transform: rotate(3deg); }
.focus-rotate-l-md:focus { transform: rotate(6deg); }
.focus-rotate-l-lg:focus { transform: rotate(9deg); }
.focus-rotate-l-xl:focus { transform: rotate(12deg); }

.focus-rotate-r-xs:focus { transform: rotate(1deg); }
.focus-rotate-r-sm:focus { transform: rotate(3deg); }
.focus-rotate-r-md:focus { transform: rotate(6deg); }
.focus-rotate-r-lg:focus { transform: rotate(9deg); }
.focus-rotate-r-xl:focus { transform: rotate(12deg); }

.focus-rotate-l-25:focus { transform: rotate(25deg); }
.focus-rotate-l-45:focus { transform: rotate(45deg); }
.focus-rotate-l-90:focus { transform: rotate(90deg); }
.focus-rotate-l-180:focus { transform: rotate(180deg); }
.focus-rotate-l-360:focus { transform: rotate(360deg); }

.focus-rotate-r-25:focus { transform: rotate(25deg); }
.focus-rotate-r-45:focus { transform: rotate(45deg); }
.focus-rotate-r-90:focus { transform: rotate(90deg); }
.focus-rotate-r-180:focus { transform: rotate(180deg); }
.focus-rotate-r-360:focus { transform: rotate(360deg); }

/* Active Effects */
.active-translate-up-xs:active { transform: translateY(-1.8px); }
.active-translate-up-sm:active { transform: translateY(-2.5px); }
.active-translate-up-md:active { transform: translateY(-3.8px); }
.active-translate-up-lg:active { transform: translateY(-4.8px); }
.active-translate-up-xl:active { transform: translateY(-5.8px); }

.active-translate-down-xs:active { transform: translateY(1.8px); }
.active-translate-down-sm:active { transform: translateY(2.5px); }
.active-translate-down-md:active { transform: translateY(3.8px); }
.active-translate-down-lg:active { transform: translateY(4.8px); }
.active-translate-down-xl:active { transform: translateY(5.8px); }

.active-scale-up-xs:active { transform: scale(0.05); }
.active-scale-up-sm:active { transform: scale(1.05); }
.active-scale-up-md:active { transform: scale(2.05); }
.active-scale-up-lg:active { transform: scale(3.05); }
.active-scale-up-xl:active { transform: scale(4.05); }

.active-scale-down-xs:active { transform: scale(-0.05); }
.active-scale-down-sm:active { transform: scale(-1.05); }
.active-scale-down-md:active { transform: scale(-2.05); }
.active-scale-down-lg:active { transform: scale(-3.05); }
.active-scale-down-xl:active { transform: scale(-4.05); }

.active-rotate-l-xs:active { transform: rotate(1deg); }
.active-rotate-l-sm:active { transform: rotate(3deg); }
.active-rotate-l-md:active { transform: rotate(6deg); }
.active-rotate-l-lg:active { transform: rotate(9deg); }
.active-rotate-l-xl:active { transform: rotate(12deg); }

.active-rotate-r-xs:active { transform: rotate(1deg); }
.active-rotate-r-sm:active { transform: rotate(3deg); }
.active-rotate-r-md:active { transform: rotate(6deg); }
.active-rotate-r-lg:active { transform: rotate(9deg); }
.active-rotate-r-xl:active { transform: rotate(12deg); }

.active-rotate-l-25:active { transform: rotate(25deg); }
.active-rotate-l-45:active { transform: rotate(45deg); }
.active-rotate-l-90:active { transform: rotate(90deg); }
.active-rotate-l-180:active { transform: rotate(180deg); }
.active-rotate-l-360:active { transform: rotate(360deg); }

.active-rotate-r-25:active { transform: rotate(25deg); }
.active-rotate-r-45:active { transform: rotate(45deg); }
.active-rotate-r-90:active { transform: rotate(90deg); }
.active-rotate-r-180:active { transform: rotate(180deg); }
.active-rotate-r-360:active { transform: rotate(360deg); }

/* ========================================================================== */
/* OPACITY STATE UTILITIES                                                   */
/* ========================================================================== */

/**
 * Opacity States
 *
 * These utilities change opacity on hover, focus, and active states.
 */

/* Hover Opacity */
.hover-opacity-0p,.hover-opacity-none:hover { opacity: 0 !important; }
.hover-opacity-10p:hover { opacity: 0.10 !important; }
.hover-opacity-20p:hover { opacity: 0.20 !important; }
.hover-opacity-30p:hover { opacity: 0.30 !important; }
.hover-opacity-50p:hover { opacity: 0.40 !important; }
.hover-opacity-50p:hover { opacity: 0.50 !important; }
.hover-opacity-60p:hover { opacity: 0.60 !important; }
.hover-opacity-70p:hover { opacity: 0.70 !important; }
.hover-opacity-80p:hover { opacity: 0.80 !important; }
.hover-opacity-90p:hover { opacity: 0.90 !important; }
.hover-opacity-100p:hover { opacity: 1 !important; }

/* Focus Opacity */
.focus-opacity-0p,.focus-opacity-none:focus { opacity: 0 !important; }
.focus-opacity-10p:focus { opacity: 0.10 !important; }
.focus-opacity-20p:focus { opacity: 0.20 !important; }
.focus-opacity-30p:focus { opacity: 0.30 !important; }
.focus-opacity-50p:focus { opacity: 0.40 !important; }
.focus-opacity-50p:focus { opacity: 0.50 !important; }
.focus-opacity-60p:focus { opacity: 0.60 !important; }
.focus-opacity-70p:focus { opacity: 0.70 !important; }
.focus-opacity-80p:focus { opacity: 0.80 !important; }
.focus-opacity-90p:focus { opacity: 0.90 !important; }
.focus-opacity-100p:focus { opacity: 1 !important; }

/* Active Opacity */
.active-opacity-0p,.active-opacity-none:active { opacity: 0 !important; }
.active-opacity-10p:active { opacity: 0.10 !important; }
.active-opacity-20p:active { opacity: 0.20 !important; }
.active-opacity-30p:active { opacity: 0.30 !important; }
.active-opacity-50p:active { opacity: 0.40 !important; }
.active-opacity-50p:active { opacity: 0.50 !important; }
.active-opacity-60p:active { opacity: 0.60 !important; }
.active-opacity-70p:active { opacity: 0.70 !important; }
.active-opacity-80p:active { opacity: 0.80 !important; }
.active-opacity-90p:active { opacity: 0.90 !important; }
.active-opacity-100p:active { opacity: 1 !important; }

/* ========================================================================== */
/* RING STATE UTILITIES                                                      */
/* ========================================================================== */


/* ---------- Grayscale ---------- */
.active-bg-gray-100:active { background-color: var(--mico-color-gray-100) !important; }
.active-bg-gray-200:active { background-color: var(--mico-color-gray-200) !important; }
.active-bg-gray-300:active { background-color: var(--mico-color-gray-300) !important; }
.active-bg-gray-400:active { background-color: var(--mico-color-gray-400) !important; }
.active-bg-gray-500:active { background-color: var(--mico-color-gray-500) !important; }
.active-bg-gray-600:active { background-color: var(--mico-color-gray-600) !important; }
.active-bg-gray-700:active { background-color: var(--mico-color-gray-700) !important; }
.active-bg-gray-800:active { background-color: var(--mico-color-gray-800) !important; }
.active-bg-gray-900:active { background-color: var(--mico-color-gray-900) !important; }

/* ---------- Black Scale ---------- */
.active-bg-black-100:active { background-color: var(--mico-color-black-100) !important; }
.active-bg-black-200:active { background-color: var(--mico-color-black-200) !important; }
.active-bg-black-300:active { background-color: var(--mico-color-black-300) !important; }
.active-bg-black-400:active { background-color: var(--mico-color-black-400) !important; }
.active-bg-black-500:active { background-color: var(--mico-color-black-500) !important; }
.active-bg-black-600:active { background-color: var(--mico-color-black-600) !important; }
.active-bg-black-700:active { background-color: var(--mico-color-black-700) !important; }
.active-bg-black-800:active { background-color: var(--mico-color-black-800) !important; }
.active-bg-black-900:active { background-color: var(--mico-color-black-900) !important; }

/* ---------- Transparency ---------- */
.active-bg-black-trans-100:active { background-color: var(--mico-color-black-trans-100) !important; }
.active-bg-black-trans-200:active { background-color: var(--mico-color-black-trans-200) !important; }
.active-bg-black-trans-300:active { background-color: var(--mico-color-black-trans-300) !important; }
.active-bg-black-trans-400:active { background-color: var(--mico-color-black-trans-400) !important; }
.active-bg-black-trans-500:active { background-color: var(--mico-color-black-trans-500) !important; }
.active-bg-black-trans-600:active { background-color: var(--mico-color-black-trans-600) !important; }
.active-bg-black-trans-700:active { background-color: var(--mico-color-black-trans-700) !important; }
.active-bg-black-trans-800:active { background-color: var(--mico-color-black-trans-800) !important; }
.active-bg-black-trans-900:active { background-color: var(--mico-color-black-trans-900) !important; }

/* ---------- State Colors ---------- */
.active-bg-success:active { background-color: var(--mico-color-success) !important; }
.active-bg-warning:active { background-color: var(--mico-color-warning) !important; }
.active-bg-error:active { background-color: var(--mico-color-error) !important; }
.active-bg-info:active { background-color: var(--mico-color-info) !important; }

/**
 * TEXT COLOR ACTIVE STATES
 *
 * These utilities apply text colors on active state
 */
.active-text-primary:active { color: var(--mico-color-primary) !important; }
.active-text-secondary:active { color: var(--mico-color-secondary) !important; }
.active-text-accent:active { color: var(--mico-color-accent) !important; }

/* ========================================================================== */
/* ADDITIONAL INTERACTIVE UTILITIES                                           */
/* ========================================================================== */

/**
 * OPACITY HOVER EFFECTS
 *
 * These utilities apply opacity changes on hover
 */


/**
 * FILTER HOVER EFFECTS
 *
 * These utilities apply filter effects on hover
 */
.hover-blur:hover { filter: blur(4px) !important; }
.hover-brightness-110:hover { filter: brightness(1.1) !important; }
.hover-brightness-125:hover { filter: brightness(1.25) !important; }
.hover-grayscale:hover { filter: grayscale(100%) !important; }
.hover-sepia:hover { filter: sepia(100%) !important; }

/* Complete hover effect for opacity, transforms*/

/* ========================================================================== */
/* USAGE EXAMPLES AND DOCUMENTATION                                           */
/* ========================================================================== */

/**
 * HOW TO USE INTERACTIVE STATE UTILITIES
 *
 * These utilities can be combined to create rich interactive experiences.
 * Here are some common patterns:
 *
 * 1. Basic hover effect for buttons:
 *    <button class="hover-bg-primary hover-text-white">Button</button>
 *
 * 2. Elevated card on hover:
 *    <div class="hover-elevate hover-bg-primary">Card content</div>
 *
 * 3. Interactive link:
 *    <a class="hover-text-primary focus-text-primary active-text-primary">Link</a>
 *
 * 4. Subtle image hover effect:
 *    <img class="hover-brightness-110 hover-scale-sm" src="image.jpg" alt="Image">
 *
 * 5. Button with multiple states:
 *    <button class="hover-bg-primary focus-bg-primary active-bg-primary">Button</button>
 */

