/**
 * Mico CSS Framework - Border, Outline, Shadow & Ring Utilities
 *
 * This file provides comprehensive border and visual enhancement utilities:
 *
 * - Border width, style, and color control
 * - Border radius for rounded corners
 * - Outline utilities for accessibility and focus states
 * - Box shadow utilities for depth and elevation
 * - Ring utilities for focus indicators and highlights
 * - Divide utilities for separating child elements
 *
 * FEATURES:
 * - Complete border control (width, style, color, radius)
 * - Accessible outline and focus management
 * - Flexible shadow system for visual hierarchy
 * - Modern ring utilities using box-shadow
 * - Child element dividers with consistent styling
 *
 * USAGE:
 * Borders: .border, .border-2, .border-primary
 * Radius: .rounded, .rounded-lg, .rounded-full
 * Outlines: .outline, .outline-2, .outline-primary
 * Shadows: .shadow, .shadow-lg, .shadow-none
 * Rings: .ring, .ring-2, .ring-primary
 * Dividers: .divide-y, .divide-x, .divide-gray-200
 */

/* ========================================================================== */
/* BORDER WIDTH UTILITIES                                                    */
/* ========================================================================== */

/**
 * Border Width Control
 *
 * These utilities control the thickness of borders on all sides or specific sides.
 * Use .border-0 to remove borders completely.
 */

/* All sides */
.border-0 { border-width: var(--mico-border-width-0) !important; }
.border-1  { border-width: var(--mico-border-width-1) !important; }
.border-2 { border-width: var(--mico-border-width-2) !important; }
.border-4 { border-width: var(--mico-border-width-4) !important; }
.border-8 { border-width: var(--mico-border-width-8) !important; }

.border-s-0 { border-width: var(--mico-border-width-0) !important; border-style: var(--mico-border-solid) !important; }
.border-s-1  { border-width: var(--mico-border-width-1) !important; border-style: var(--mico-border-solid) !important; }
.border-s-2 { border-width: var(--mico-border-width-2) !important; border-style: var(--mico-border-solid) !important; }
.border-s-4 { border-width: var(--mico-border-width-4) !important; border-style: var(--mico-border-solid) !important; }
.border-s-8 { border-width: var(--mico-border-width-8) !important; border-style: var(--mico-border-solid) !important; }

/* Individual sides */
.border-t-0 { border-top-width: var(--mico-border-width-0) !important; }
.border-t-1 { border-top-width: var(--mico-border-width-1) !important; }
.border-t-2 { border-top-width: var(--mico-border-width-2) !important; }
.border-t-4 { border-top-width: var(--mico-border-width-4) !important; }
.border-t-8 { border-top-width: var(--mico-border-width-8) !important; }

.border-r-0 { border-right-width: var(--mico-border-width-0) !important; }
.border-r-1 { border-right-width: var(--mico-border-width-1) !important; }
.border-r-2 { border-right-width: var(--mico-border-width-2) !important; }
.border-r-4 { border-right-width: var(--mico-border-width-4) !important; }
.border-r-8 { border-right-width: var(--mico-border-width-8) !important; }

.border-b-0 { border-bottom-width: var(--mico-border-width-0) !important; }
.border-b-1  { border-bottom-width: var(--mico-border-width-1) !important; }
.border-b-2 { border-bottom-width: var(--mico-border-width-2) !important; }
.border-b-4 { border-bottom-width: var(--mico-border-width-4) !important; }
.border-b-8 { border-bottom-width: var(--mico-border-width-8) !important; }

.border-l-0 { border-left-width: var(--mico-border-width-0) !important; }
.border-l-1  { border-left-width: var(--mico-border-width-1) !important; }
.border-l-2 { border-left-width: var(--mico-border-width-2) !important; }
.border-l-4 { border-left-width: var(--mico-border-width-4) !important; }
.border-l-8 { border-left-width: var(--mico-border-width-8) !important; }

/* Horizontal and vertical */
.border-x-0 { border-left-width: var(--mico-border-width-0) !important; border-right-width: var(--mico-border-width-0) !important; }
.border-x-1   { border-left-width: var(--mico-border-width-1) !important; border-right-width: var(--mico-border-width-1) !important; }
.border-x-2 { border-left-width: var(--mico-border-width-2) !important; border-right-width: var(--mico-border-width-2) !important; }
.border-x-4 { border-left-width: var(--mico-border-width-4) !important; border-right-width: var(--mico-border-width-4) !important; }
.border-x-8 { border-left-width: var(--mico-border-width-8) !important; border-right-width: var(--mico-border-width-8) !important; }

.border-y-0 { border-top-width: var(--mico-border-width-0) !important; border-bottom-width: var(--mico-border-width-0) !important; }
.border-y-1  { border-top-width: var(--mico-border-width-1) !important; border-bottom-width: var(--mico-border-width-1) !important; }
.border-y-2 { border-top-width: var(--mico-border-width-2) !important; border-bottom-width: var(--mico-border-width-2) !important; }
.border-y-4 { border-top-width: var(--mico-border-width-4) !important; border-bottom-width: var(--mico-border-width-4) !important; }
.border-y-8 { border-top-width: var(--mico-border-width-8) !important; border-bottom-width: var(--mico-border-width-8) !important; }

/* ========================================================================== */
/* BORDER STYLE UTILITIES                                                    */
/* ========================================================================== */

/**
 * Border Style Control
 *
 * These utilities control the style of borders (solid, dashed, dotted, etc.).
 */
.border-solid  { border-style: var(--mico-border-solid) !important; }
.border-dashed { border-style: var(--mico-border-dashed) !important; }
.border-dotted { border-style: var(--mico-border-dotted) !important; }
.border-double { border-style: var(--mico-border-double) !important; }
.border-none   { border-style: var(--mico-border-none) !important; }

/* ========================================================================== */
/* BORDER COLOR UTILITIES                                                    */
/* ========================================================================== */

/**
 * Border Color Control
 *
 * These utilities control the color of borders using the framework's color system.
 */

/* Brand Colors */
.border-primary   { border-color: var(--mico-color-primary) !important; }
.border-secondary { border-color: var(--mico-color-secondary) !important; }
.border-accent    { border-color: var(--mico-color-accent) !important; }

/* Primary Color Variations */
.border-primary-light { border-color: var(--mico-color-primary-light) !important; }
.border-primary-2xlight { border-color: var(--mico-color-primary-2xlight) !important; }
.border-primary-3xlight { border-color: var(--mico-color-primary-3xlight) !important; }
.border-primary-4xlight { border-color: var(--mico-color-primary-4xlight) !important; }
.border-primary-5xlight { border-color: var(--mico-color-primary-5xlight) !important; }
.border-primary-6xlight { border-color: var(--mico-color-primary-6xlight) !important; }
.border-primary-7xlight { border-color: var(--mico-color-primary-7xlight) !important; }
.border-primary-8xlight { border-color: var(--mico-color-primary-8xlight) !important; }

.border-primary-dark { border-color: var(--mico-color-primary-dark) !important; }
.border-primary-2xdark { border-color: var(--mico-color-primary-2xdark) !important; }
.border-primary-3xdark { border-color: var(--mico-color-primary-3xdark) !important; }
.border-primary-4xdark { border-color: var(--mico-color-primary-4xdark) !important; }
.border-primary-5xdark { border-color: var(--mico-color-primary-5xdark) !important; }
.border-primary-6xdark { border-color: var(--mico-color-primary-6xdark) !important; }
.border-primary-7xdark { border-color: var(--mico-color-primary-7xdark) !important; }
.border-primary-8xdark { border-color: var(--mico-color-primary-8xdark) !important; }

/* Secondary Color Variations */
.border-secondary-light { border-color: var(--mico-color-secondary-light) !important; }
.border-secondary-2xlight { border-color: var(--mico-color-secondary-2xlight) !important; }
.border-secondary-3xlight { border-color: var(--mico-color-secondary-3xlight) !important; }
.border-secondary-4xlight { border-color: var(--mico-color-secondary-4xlight) !important; }
.border-secondary-5xlight { border-color: var(--mico-color-secondary-5xlight) !important; }
.border-secondary-6xlight { border-color: var(--mico-color-secondary-6xlight) !important; }
.border-secondary-7xlight { border-color: var(--mico-color-secondary-7xlight) !important; }
.border-secondary-8xlight { border-color: var(--mico-color-secondary-8xlight) !important; }

.border-secondary-dark { border-color: var(--mico-color-secondary-dark) !important; }
.border-secondary-2xdark { border-color: var(--mico-color-secondary-2xdark) !important; }
.border-secondary-3xdark { border-color: var(--mico-color-secondary-3xdark) !important; }
.border-secondary-4xdark { border-color: var(--mico-color-secondary-4xdark) !important; }
.border-secondary-5xdark { border-color: var(--mico-color-secondary-5xdark) !important; }
.border-secondary-6xdark { border-color: var(--mico-color-secondary-6xdark) !important; }
.border-secondary-7xdark { border-color: var(--mico-color-secondary-7xdark) !important; }
.border-secondary-8xdark { border-color: var(--mico-color-secondary-8xdark) !important; }

/* Accent Color Variations */
.border-accent-light { border-color: var(--mico-color-accent-light) !important; }
.border-accent-2xlight { border-color: var(--mico-color-accent-2xlight) !important; }
.border-accent-3xlight { border-color: var(--mico-color-accent-3xlight) !important; }
.border-accent-4xlight { border-color: var(--mico-color-accent-4xlight) !important; }
.border-accent-5xlight { border-color: var(--mico-color-accent-5xlight) !important; }
.border-accent-6xlight { border-color: var(--mico-color-accent-6xlight) !important; }
.border-accent-7xlight { border-color: var(--mico-color-accent-7xlight) !important; }
.border-accent-8xlight { border-color: var(--mico-color-accent-8xlight) !important; }

.border-accent-dark { border-color: var(--mico-color-accent-dark) !important; }
.border-accent-2xdark { border-color: var(--mico-color-accent-2xdark) !important; }
.border-accent-3xdark { border-color: var(--mico-color-accent-3xdark) !important; }
.border-accent-4xdark { border-color: var(--mico-color-accent-4xdark) !important; }
.border-accent-5xdark { border-color: var(--mico-color-accent-5xdark) !important; }
.border-accent-6xdark { border-color: var(--mico-color-accent-6xdark) !important; }
.border-accent-7xdark { border-color: var(--mico-color-accent-7xdark) !important; }
.border-accent-8xdark { border-color: var(--mico-color-accent-8xdark) !important; }

/* State Colors */
.border-success { border-color: var(--mico-color-success) !important; }
.border-error   { border-color: var(--mico-color-error) !important; }
.border-warning { border-color: var(--mico-color-warning) !important; }
.border-info    { border-color: var(--mico-color-info) !important; }

/* Neutral Colors - OKLCH System */
.border-white { border-color: var(--mico-color-white) !important; }
.border-black { border-color: var(--mico-color-black) !important; }

/* Gray Scale - OKLCH System */
.border-gray { border-color: var(--mico-color-gray) !important; }
.border-gray-2xlight { border-color: var(--mico-color-gray-2xlight) !important; }
.border-gray-3xlight { border-color: var(--mico-color-gray-3xlight) !important; }
.border-gray-4xlight { border-color: var(--mico-color-gray-4xlight) !important; }
.border-gray-5xlight { border-color: var(--mico-color-gray-5xlight) !important; }
.border-gray-2xdark { border-color: var(--mico-color-gray-2xdark) !important; }
.border-gray-3xdark { border-color: var(--mico-color-gray-3xdark) !important; }
.border-gray-4xdark { border-color: var(--mico-color-gray-4xdark) !important; }
.border-gray-5xdark { border-color: var(--mico-color-gray-5xdark) !important; }

/* Extended Color Palette - OKLCH System */
.border-red { border-color: var(--mico-color-red) !important; }
.border-yellow { border-color: var(--mico-color-yellow) !important; }
.border-green { border-color: var(--mico-color-green) !important; }
.border-blue { border-color: var(--mico-color-blue) !important; }
.border-indigo { border-color: var(--mico-color-indigo) !important; }
.border-purple { border-color: var(--mico-color-purple) !important; }
.border-pink { border-color: var(--mico-color-pink) !important; }

/* Red Color Variations */
.border-red-light { border-color: var(--mico-color-red-light) !important; }
.border-red-2xlight { border-color: var(--mico-color-red-2xlight) !important; }
.border-red-3xlight { border-color: var(--mico-color-red-3xlight) !important; }
.border-red-4xlight { border-color: var(--mico-color-red-4xlight) !important; }
.border-red-5xlight { border-color: var(--mico-color-red-5xlight) !important; }
.border-red-6xlight { border-color: var(--mico-color-red-6xlight) !important; }
.border-red-7xlight { border-color: var(--mico-color-red-7xlight) !important; }
.border-red-8xlight { border-color: var(--mico-color-red-8xlight) !important; }

.border-red-dark { border-color: var(--mico-color-red-dark) !important; }
.border-red-2xdark { border-color: var(--mico-color-red-2xdark) !important; }
.border-red-3xdark { border-color: var(--mico-color-red-3xdark) !important; }
.border-red-4xdark { border-color: var(--mico-color-red-4xdark) !important; }
.border-red-5xdark { border-color: var(--mico-color-red-5xdark) !important; }
.border-red-6xdark { border-color: var(--mico-color-red-6xdark) !important; }
.border-red-7xdark { border-color: var(--mico-color-red-7xdark) !important; }
.border-red-8xdark { border-color: var(--mico-color-red-8xdark) !important; }

/* Special Colors */
.border-transparent { border-color: var(--mico-value-transparent) !important; }
.border-current     { border-color: currentColor !important; }

/* ========================================================================== */
/* BORDER RADIUS UTILITIES                                                   */
/* ========================================================================== */

/**
 * Border Radius Control
 *
 * These utilities control the roundness of corners for visual design.
 */

/* All corners */
.rounded-none { border-radius: var(--mico-radius-none); }
.rounded-sm   { border-radius: var(--mico-radius-sm); }
.rounded-md   { border-radius: var(--mico-radius-md); }
.rounded-lg   { border-radius: var(--mico-radius-lg); }
.rounded-xl   { border-radius: var(--mico-radius-xl); }
.rounded-2xl  { border-radius: var(--mico-radius-2xl); }
.rounded-3xl  { border-radius: var(--mico-radius-3xl); }
.rounded-full { border-radius: var(--mico-radius-full); }

/* Individual corners */
.rounded-t-none { border-top-left-radius: var(--mico-radius-none); border-top-right-radius: var(--mico-radius-none); }
.rounded-t-sm   { border-top-left-radius: var(--mico-radius-sm); border-top-right-radius: var(--mico-radius-sm); }
.rounded-t-md   { border-top-left-radius: var(--mico-radius-md); border-top-right-radius: var(--mico-radius-md); }
.rounded-t-lg   { border-top-left-radius: var(--mico-radius-lg); border-top-right-radius: var(--mico-radius-lg); }
.rounded-t-xl   { border-top-left-radius: var(--mico-radius-xl); border-top-right-radius: var(--mico-radius-xl); }
.rounded-t-2xl  { border-top-left-radius: var(--mico-radius-2xl); border-top-right-radius: var(--mico-radius-2xl); }
.rounded-t-3xl  { border-top-left-radius: var(--mico-radius-3xl); border-top-right-radius: var(--mico-radius-3xl); }
.rounded-t-full { border-top-left-radius: var(--mico-radius-full); border-top-right-radius: var(--mico-radius-full); }

.rounded-r-none { border-top-right-radius: var(--mico-radius-none); border-bottom-right-radius: var(--mico-radius-none); }
.rounded-r-sm   { border-top-right-radius: var(--mico-radius-sm); border-bottom-right-radius: var(--mico-radius-sm); }
.rounded-r-md   { border-top-right-radius: var(--mico-radius-md); border-bottom-right-radius: var(--mico-radius-md); }
.rounded-r-lg   { border-top-right-radius: var(--mico-radius-lg); border-bottom-right-radius: var(--mico-radius-lg); }
.rounded-r-xl   { border-top-right-radius: var(--mico-radius-xl); border-bottom-right-radius: var(--mico-radius-xl); }
.rounded-r-2xl  { border-top-right-radius: var(--mico-radius-2xl); border-bottom-right-radius: var(--mico-radius-2xl); }
.rounded-r-3xl  { border-top-right-radius: var(--mico-radius-3xl); border-bottom-right-radius: var(--mico-radius-3xl); }
.rounded-r-full { border-top-right-radius: var(--mico-radius-full); border-bottom-right-radius: var(--mico-radius-full); }

.rounded-b-none { border-bottom-left-radius: var(--mico-radius-none); border-bottom-right-radius: var(--mico-radius-none); }
.rounded-b-sm   { border-bottom-left-radius: var(--mico-radius-sm); border-bottom-right-radius: var(--mico-radius-sm); }
.rounded-b-md   { border-bottom-left-radius: var(--mico-radius-md); border-bottom-right-radius: var(--mico-radius-md); }
.rounded-b-lg   { border-bottom-left-radius: var(--mico-radius-lg); border-bottom-right-radius: var(--mico-radius-lg); }
.rounded-b-xl   { border-bottom-left-radius: var(--mico-radius-xl); border-bottom-right-radius: var(--mico-radius-xl); }
.rounded-b-2xl  { border-bottom-left-radius: var(--mico-radius-2xl); border-bottom-right-radius: var(--mico-radius-2xl); }
.rounded-b-3xl  { border-bottom-left-radius: var(--mico-radius-3xl); border-bottom-right-radius: var(--mico-radius-3xl); }
.rounded-b-full { border-bottom-left-radius: var(--mico-radius-full); border-bottom-right-radius: var(--mico-radius-full); }

.rounded-l-none { border-top-left-radius: var(--mico-radius-none); border-bottom-left-radius: var(--mico-radius-none); }
.rounded-l-sm   { border-top-left-radius: var(--mico-radius-sm); border-bottom-left-radius: var(--mico-radius-sm); }
.rounded-l-md   { border-top-left-radius: var(--mico-radius-md); border-bottom-left-radius: var(--mico-radius-md); }
.rounded-l-lg   { border-top-left-radius: var(--mico-radius-lg); border-bottom-left-radius: var(--mico-radius-lg); }
.rounded-l-xl   { border-top-left-radius: var(--mico-radius-xl); border-bottom-left-radius: var(--mico-radius-xl); }
.rounded-l-2xl  { border-top-left-radius: var(--mico-radius-2xl); border-bottom-left-radius: var(--mico-radius-2xl); }
.rounded-l-3xl  { border-top-left-radius: var(--mico-radius-3xl); border-bottom-left-radius: var(--mico-radius-3xl); }
.rounded-l-full { border-top-left-radius: var(--mico-radius-full); border-bottom-left-radius: var(--mico-radius-full); }

/* ========================================================================== */
/* OUTLINE UTILITIES                                                         */
/* ========================================================================== */

/**
 * Outline Control
 *
 * These utilities control outlines for accessibility and focus states.
 * Outlines are essential for keyboard navigation and accessibility compliance.
 */

/* Outline Width */
.outline-0 { outline-width: var(--mico-outline-width-0); }
.outline-1 { outline-width: var(--mico-outline-width-1); }
.outline-2 { outline-width: var(--mico-outline-width-2); }
.outline-4 { outline-width: var(--mico-outline-width-4); }
.outline-8 { outline-width: var(--mico-outline-width-8); }

.outline-s-0 { outline-width: var(--mico-outline-width-0); outline-style: var(--mico-outline-style-solid); }
.outline-s-1 { outline-width: var(--mico-outline-width-1); outline-style: var(--mico-outline-style-solid); }
.outline-s-2 { outline-width: var(--mico-outline-width-2); outline-style: var(--mico-outline-style-solid); }
.outline-s-4 { outline-width: var(--mico-outline-width-4); outline-style: var(--mico-outline-style-solid); }
.outline-s-8 { outline-width: var(--mico-outline-width-8); outline-style: var(--mico-outline-style-solid); }

/* Outline Style */
.outline-none   { outline-style: var(--mico-outline-style-none); }
.outline-solid  { outline-style: var(--mico-outline-style-solid); }
.outline-dashed { outline-style: var(--mico-outline-style-dashed); }
.outline-dotted { outline-style: var(--mico-outline-style-dotted); }
.outline-double { outline-style: var(--mico-outline-style-double); }

/* Outline Color - OKLCH System */
.outline-primary   { outline-color: var(--mico-color-primary); }
.outline-secondary { outline-color: var(--mico-color-secondary); }
.outline-accent    { outline-color: var(--mico-color-accent); }
.outline-success   { outline-color: var(--mico-color-success); }
.outline-error     { outline-color: var(--mico-color-error); }
.outline-warning   { outline-color: var(--mico-color-warning); }
.outline-info      { outline-color: var(--mico-color-info); }
.outline-white     { outline-color: var(--mico-color-white); }
.outline-black     { outline-color: var(--mico-color-black); }
.outline-gray      { outline-color: var(--mico-color-gray); }
.outline-gray-3xlight { outline-color: var(--mico-color-gray-3xlight); }
.outline-gray-3xdark  { outline-color: var(--mico-color-gray-3xdark); }

/* Outline Offset */
.outline-offset-0 { outline-offset: var(--mico-outline-offset-0); }
.outline-offset-1 { outline-offset: var(--mico-outline-offset-1); }
.outline-offset-2 { outline-offset: var(--mico-outline-offset-2); }
.outline-offset-4 { outline-offset: var(--mico-outline-offset-4); }
.outline-offset-8 { outline-offset: var(--mico-outline-offset-8); }


/* ========================================================================== */
/* BOX SHADOW UTILITIES                                                      */
/* ========================================================================== */

/**
 * Box Shadow Control
 *
 * These utilities control box shadows for depth, elevation, and visual hierarchy.
 */

/* Light Shadow Presets */
.shadow-none { box-shadow: var(--mico-value-none) !important; }
.shadow-xs-light   { box-shadow: var(--mico-shadow-xs-light) !important; }
.shadow-sm-light   { box-shadow: var(--mico-shadow-sm-light) !important; }
.shadow-md-light   { box-shadow: var(--mico-shadow-md-light) !important; }
.shadow-lg-light   { box-shadow: var(--mico-shadow-lg-light) !important; }
.shadow-xl-light   { box-shadow: var(--mico-shadow-xl-light) !important; }
.shadow-2xl-light  { box-shadow: var(--mico-shadow-2xl-light) !important; }
.shadow-3xl-light  { box-shadow: var(--mico-shadow-3xl-light) !important; }

/* Dark Mode Shadow Presets */
.shadow-xs-dark   { box-shadow: var(--mico-shadow-xs-dark) !important; }
.shadow-sm-dark   { box-shadow: var(--mico-shadow-sm-dark) !important; }
.shadow-md-dark   { box-shadow: var(--mico-shadow-md-dark) !important; }
.shadow-lg-dark   { box-shadow: var(--mico-shadow-lg-dark) !important; }
.shadow-xl-dark   { box-shadow: var(--mico-shadow-xl-dark) !important; }
.shadow-2xl-dark  { box-shadow: var(--mico-shadow-2xl-dark) !important; }
.shadow-3xl-dark  { box-shadow: var(--mico-shadow-3xl-dark) !important; }

/* Inner Shadow */
.shadow-inner-light { box-shadow: var(--mico-shadow-inset-sm-light); }
.shadow-inner-dark { box-shadow: var(--mico-shadow-inset-sm-dark); }

/* Colored Shadows */
.shadow-primary { box-shadow: var(--mico-shadow-primary) !important; }
.shadow-secondary { box-shadow: var(--mico-shadow-secondary) !important; }
.shadow-accent { box-shadow: var(--mico-shadow-accent) !important; }
.shadow-success   { box-shadow: var(--mico-shadow-success) !important; }
.shadow-error     { box-shadow: var(--mico-shadow-error) !important; }
.shadow-warning   { box-shadow: var(--mico-shadow-warning) !important; }
.shadow-info      { box-shadow: var(--mico-shadow-info) !important; }


/* ========================================================================== */
/* DIVIDE UTILITIES                                                          */
/* ========================================================================== */

/**
 * Divide Control
 *
 * These utilities add borders between child elements for visual separation.
 * Perfect for lists, navigation items, and grouped content.
 */

/* Divide Y (Horizontal borders between children) */
.divide-y-0 > * + * { border-top-width: var(--mico-divide-width-0); }
.divide-y-1  > * + * { border-top-width: var(--mico-divide-width-1); }
.divide-y-2 > * + * { border-top-width: var(--mico-divide-width-2); }
.divide-y-4 > * + * { border-top-width: var(--mico-divide-width-4); }
.divide-y-8 > * + * { border-top-width: var(--mico-divide-width-8); }

/* Divide X (Vertical borders between children) */
.divide-x-0 > * + * { border-left-width: var(--mico-divide-width-0); }
.divide-x-1  > * + * { border-left-width: var(--mico-divide-width-1); }
.divide-x-2 > * + * { border-left-width: var(--mico-divide-width-2); }
.divide-x-4 > * + * { border-left-width: var(--mico-divide-width-4); }
.divide-x-8 > * + * { border-left-width: var(--mico-divide-width-8); }

/* Divide Colors - OKLCH System */
.divide-primary   > * + * { border-color: var(--mico-color-primary); }
.divide-secondary > * + * { border-color: var(--mico-color-secondary); }
.divide-accent    > * + * { border-color: var(--mico-color-accent); }
.divide-gray      > * + * { border-color: var(--mico-color-gray); }
.divide-gray-2xlight > * + * { border-color: var(--mico-color-gray-2xlight); }
.divide-gray-3xlight > * + * { border-color: var(--mico-color-gray-3xlight); }
.divide-gray-2xdark  > * + * { border-color: var(--mico-color-gray-2xdark); }

/* Divide Styles */
.divide-solid  > * + * { border-style: var(--mico-border-solid); }
.divide-dashed > * + * { border-style: var(--mico-border-dashed); }
.divide-dotted > * + * { border-style: var(--mico-border-dotted); }

/* ========================================================================== */
/* ACCESSIBILITY & BROWSER SUPPORT                                           */
/* ========================================================================== */

/**
 * High Contrast Mode Support
 *
 * Enhanced visibility for high contrast environments.
 */
@media (prefers-contrast: high) {
  .border-1,
  .border-2,
  .border-4,
  .border-8 {
    border-color: currentColor;
  }

  .outline-1,
  .outline-2,
  .outline-4,
  .outline-8 {
    outline-color: currentColor;
  }

}

/**
 * Reduced Motion Support
 *
 * Respects user preferences for reduced motion.
 */
@media (prefers-reduced-motion: reduce) {
  .shadow-xs,
  .shadow-sm,
  .shadow-md,
  .shadow-lg,
  .shadow-xl,
  .shadow-2xl,
  .shadow-3xl {
    transition: var(--mico-value-none);
  }
}

/**
 * Print Media Support
 *
 * Optimized styles for print media.
 */
@media print {
  .shadow-xs,
  .shadow-sm,
  .shadow-md,
  .shadow-lg,
  .shadow-xl,
  .shadow-2xl,
  .shadow-3xl {
    box-shadow: var(--mico-value-none) !important;
  }

}

/* ========================================================================== */
/* USAGE EXAMPLES AND DOCUMENTATION                                          */
/* ========================================================================== */

/**
 * USAGE EXAMPLES:
 *
 * 1. Basic border:
 *    <div class="border border-gray-300 rounded">Content</div>
 *
 * 2. Focus ring:
 *    <button class="ring ring-primary ring-offset-2">Button</button>
 *
 * 3. Card with shadow:
 *    <div class="border border-gray-200 rounded-lg shadow-md">Card</div>
 *
 * 4. Divided list:
 *    <ul class="divide-y divide-gray-200">
 *      <li>Item 1</li>
 *      <li>Item 2</li>
 *    </ul>
 *
 * 5. Accessible outline:
 *    <input class="outline outline-2 outline-primary outline-offset-2">
 *
 * 6. Complex border styling:
 *    <div class="border-2 border-dashed border-primary rounded-xl">
 *
 * ACCESSIBILITY NOTES:
 * - Always provide sufficient contrast for borders and outlines
 * - Use outline utilities for focus indicators
 * - Test with high contrast mode
 * - Ensure ring utilities are visible for keyboard navigation
 */