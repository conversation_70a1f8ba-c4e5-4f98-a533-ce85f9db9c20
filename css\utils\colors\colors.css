/**
 * Color Utility Classes
 *
 * Comprehensive color utilities for the Mico CSS Framework.
 * All color variables are defined in css/base/variables.css
 *
 * This file contains only utility classes that reference those variables.
 * Classes are organized by: Brand Colors, Neutral Colors, Extended Palette,
 * Transparency Colors, and Semantic Colors.
 */


/* ====================================================================== */
/* BRAND COLOR UTILITIES                                                  */
/* ====================================================================== */

/**
 * Base Brand Colors
 * Primary, secondary, and accent colors for brand consistency
 */

/* Base Brand Background Colors */
.bg-primary { background-color: var(--mico-color-primary) !important; }
.bg-secondary { background-color: var(--mico-color-secondary) !important; }
.bg-accent { background-color: var(--mico-color-accent) !important; }

/* Base Brand Text Colors */
.text-primary { color: var(--mico-color-primary) !important; }
.text-secondary { color: var(--mico-color-secondary) !important; }
.text-accent { color: var(--mico-color-accent) !important; }

/**
 * Primary Color Variations
 * Shades (darker) and tones (lighter) of the primary brand color
 */

/* Primary Tint Text Colors (lighter) */
.text-primary-2xlight { color: var(--mico-color-primary-2xlight) !important; }
.text-primary-3xlight { color: var(--mico-color-primary-3xlight) !important; }
.text-primary-4xlight { color: var(--mico-color-primary-4xlight) !important; }
.text-primary-5xlight { color: var(--mico-color-primary-5xlight) !important; }

/* Primary Shade Text Colors (darker) */
.text-primary-2xdark { color: var(--mico-color-primary-2xdark) !important; }
.text-primary-3xdark { color: var(--mico-color-primary-3xdark) !important; }
.text-primary-4xdark { color: var(--mico-color-primary-4xdark) !important; }
.text-primary-5xdark { color: var(--mico-color-primary-5xdark) !important; }

/* Primary Tint Background Colors (lighter) */
.bg-primary-2xlight { background-color: var(--mico-color-primary-2xlight) !important; }
.bg-primary-3xlight { background-color: var(--mico-color-primary-3xlight) !important; }
.bg-primary-4xlight { background-color: var(--mico-color-primary-4xlight) !important; }
.bg-primary-5xlight { background-color: var(--mico-color-primary-5xlight) !important; }

/* Primary Shade Background Colors (darker) */
.bg-primary-2xdark { background-color: var(--mico-color-primary-2xdark) !important; }
.bg-primary-3xdark { background-color: var(--mico-color-primary-3xdark) !important; }
.bg-primary-4xdark { background-color: var(--mico-color-primary-4xdark) !important; }
.bg-primary-5xdark { background-color: var(--mico-color-primary-5xdark) !important; }

/**
 * Secondary Color Variations
 * Shades (darker) and tones (lighter) of the secondary brand color
 */

/* Secondary Tint Text Colors (lighter) */
.text-secondary-2xlight { color: var(--mico-color-secondary-2xlight) !important; }
.text-secondary-3xlight { color: var(--mico-color-secondary-3xlight) !important; }
.text-secondary-4xlight { color: var(--mico-color-secondary-4xlight) !important; }
.text-secondary-5xlight { color: var(--mico-color-secondary-5xlight) !important; }

/* Secondary Shade Text Colors (darker) */
.text-secondary-2xdark { color: var(--mico-color-secondary-2xdark) !important; }
.text-secondary-3xdark { color: var(--mico-color-secondary-3xdark) !important; }
.text-secondary-4xdark { color: var(--mico-color-secondary-4xdark) !important; }
.text-secondary-5xdark { color: var(--mico-color-secondary-5xdark) !important; }

/* Secondary Tint Background Colors (lighter) */
.bg-secondary-2xlight { background-color: var(--mico-color-secondary-2xlight) !important; }
.bg-secondary-3xlight { background-color: var(--mico-color-secondary-3xlight) !important; }
.bg-secondary-4xlight { background-color: var(--mico-color-secondary-4xlight) !important; }
.bg-secondary-5xlight { background-color: var(--mico-color-secondary-5xlight) !important; }

/* Secondary Shade Background Colors (darker) */
.bg-secondary-2xdark { background-color: var(--mico-color-secondary-2xdark) !important; }
.bg-secondary-3xdark { background-color: var(--mico-color-secondary-3xdark) !important; }
.bg-secondary-4xdark { background-color: var(--mico-color-secondary-4xdark) !important; }
.bg-secondary-5xdark { background-color: var(--mico-color-secondary-5xdark) !important; }

/**
 * Accent Color Variations
 * Shades (darker) and tones (lighter) of the accent brand color
 */

/* Accent Tint Text Colors (lighter) */
.text-accent-2xlight { color: var(--mico-color-accent-2xlight) !important; }
.text-accent-3xlight { color: var(--mico-color-accent-3xlight) !important; }
.text-accent-4xlight { color: var(--mico-color-accent-4xlight) !important; }
.text-accent-5xlight { color: var(--mico-color-accent-5xlight) !important; }

/* Accent Shade Text Colors (darker) */
.text-accent-2xdark { color: var(--mico-color-accent-2xdark) !important; }
.text-accent-3xdark { color: var(--mico-color-accent-3xdark) !important; }
.text-accent-4xdark { color: var(--mico-color-accent-4xdark) !important; }
.text-accent-5xdark { color: var(--mico-color-accent-5xdark) !important; }

/* Accent Tint Background Colors (lighter) */
.bg-accent-2xlight { background-color: var(--mico-color-accent-2xlight) !important; }
.bg-accent-3xlight { background-color: var(--mico-color-accent-3xlight) !important; }
.bg-accent-4xlight { background-color: var(--mico-color-accent-4xlight) !important; }
.bg-accent-5xlight { background-color: var(--mico-color-accent-5xlight) !important; }

/* Accent Shade Background Colors (darker) */
.bg-accent-2xdark { background-color: var(--mico-color-accent-2xdark) !important; }
.bg-accent-3xdark { background-color: var(--mico-color-accent-3xdark) !important; }
.bg-accent-4xdark { background-color: var(--mico-color-accent-4xdark) !important; }
.bg-accent-5xdark { background-color: var(--mico-color-accent-5xdark) !important; }

/* ====================================================================== */
/* NEUTRAL COLOR UTILITIES                                                */
/* ====================================================================== */

/**
 * Black Color System - OKLCH
 * Base black with 5 tints and 5 shades for dark themes and high contrast
 */

/* Black Base Color */
.bg-black { background-color: var(--mico-color-black) !important; }
.text-black { color: var(--mico-color-black) !important; }

/* Black Tint Background Colors (lighter) */
.bg-black-2xlight { background-color: var(--mico-color-black-2xlight) !important; }
.bg-black-3xlight { background-color: var(--mico-color-black-3xlight) !important; }
.bg-black-4xlight { background-color: var(--mico-color-black-4xlight) !important; }
.bg-black-5xlight { background-color: var(--mico-color-black-5xlight) !important; }

/* Black Tint Text Colors (lighter) */
.text-black-2xlight { color: var(--mico-color-black-2xlight) !important; }
.text-black-3xlight { color: var(--mico-color-black-3xlight) !important; }
.text-black-4xlight { color: var(--mico-color-black-4xlight) !important; }
.text-black-5xlight { color: var(--mico-color-black-5xlight) !important; }

/* Black Shade Background Colors (darker) */
.bg-black-2xdark { background-color: var(--mico-color-black-2xdark) !important; }
.bg-black-3xdark { background-color: var(--mico-color-black-3xdark) !important; }
.bg-black-4xdark { background-color: var(--mico-color-black-4xdark) !important; }
.bg-black-5xdark { background-color: var(--mico-color-black-5xdark) !important; }

/* Black Shade Text Colors (darker) */
.text-black-2xdark { color: var(--mico-color-black-2xdark) !important; }
.text-black-3xdark { color: var(--mico-color-black-3xdark) !important; }
.text-black-4xdark { color: var(--mico-color-black-4xdark) !important; }
.text-black-5xdark { color: var(--mico-color-black-5xdark) !important; }

/**
 * Gray Color System - OKLCH
 * Base gray with 5 tints and 5 shades for UI elements, text, and backgrounds
 * Optimized for contrast and accessibility with perceptual uniformity
 */

/* Gray Base Color */
.bg-gray { background-color: var(--mico-color-gray) !important; }
.text-gray { color: var(--mico-color-gray) !important; }

/* Gray Tint Background Colors (lighter) */
.bg-gray-2xlight { background-color: var(--mico-color-gray-2xlight) !important; }
.bg-gray-3xlight { background-color: var(--mico-color-gray-3xlight) !important; }
.bg-gray-4xlight { background-color: var(--mico-color-gray-4xlight) !important; }
.bg-gray-5xlight { background-color: var(--mico-color-gray-5xlight) !important; }

/* Gray Tint Text Colors (lighter) */
.text-gray-2xlight { color: var(--mico-color-gray-2xlight) !important; }
.text-gray-3xlight { color: var(--mico-color-gray-3xlight) !important; }
.text-gray-4xlight { color: var(--mico-color-gray-4xlight) !important; }
.text-gray-5xlight { color: var(--mico-color-gray-5xlight) !important; }

/* Gray Shade Background Colors (darker) */
.bg-gray-2xdark { background-color: var(--mico-color-gray-2xdark) !important; }
.bg-gray-3xdark { background-color: var(--mico-color-gray-3xdark) !important; }
.bg-gray-4xdark { background-color: var(--mico-color-gray-4xdark) !important; }
.bg-gray-5xdark { background-color: var(--mico-color-gray-5xdark) !important; }

/* Gray Shade Text Colors (darker) */
.text-gray-2xdark { color: var(--mico-color-gray-2xdark) !important; }
.text-gray-3xdark { color: var(--mico-color-gray-3xdark) !important; }
.text-gray-4xdark { color: var(--mico-color-gray-4xdark) !important; }
.text-gray-5xdark { color: var(--mico-color-gray-5xdark) !important; }

/**
 * White Color System - OKLCH
 * Base white with 5 tints and 5 shades for clean, minimal aesthetics
 */

/* White Base Color */
.bg-white { background-color: var(--mico-color-white) !important; }
.text-white { color: var(--mico-color-white) !important; }

/* White Tint Background Colors (lighter) */
.bg-white-2xlight { background-color: var(--mico-color-white-2xlight) !important; }
.bg-white-3xlight { background-color: var(--mico-color-white-3xlight) !important; }
.bg-white-4xlight { background-color: var(--mico-color-white-4xlight) !important; }
.bg-white-5xlight { background-color: var(--mico-color-white-5xlight) !important; }

/* White Tint Text Colors (lighter) */
.text-white-2xlight { color: var(--mico-color-white-2xlight) !important; }
.text-white-3xlight { color: var(--mico-color-white-3xlight) !important; }
.text-white-4xlight { color: var(--mico-color-white-4xlight) !important; }
.text-white-5xlight { color: var(--mico-color-white-5xlight) !important; }

/* White Shade Background Colors (darker) */
.bg-white-2xdark { background-color: var(--mico-color-white-2xdark) !important; }
.bg-white-3xdark { background-color: var(--mico-color-white-3xdark) !important; }
.bg-white-4xdark { background-color: var(--mico-color-white-4xdark) !important; }
.bg-white-5xdark { background-color: var(--mico-color-white-5xdark) !important; }

/* White Shade Text Colors (darker) */
.text-white-2xdark { color: var(--mico-color-white-2xdark) !important; }
.text-white-3xdark { color: var(--mico-color-white-3xdark) !important; }
.text-white-4xdark { color: var(--mico-color-white-4xdark) !important; }
.text-white-5xdark { color: var(--mico-color-white-5xdark) !important; }


/* ====================================================================== */
/* EXTENDED COLOR PALETTE UTILITIES                                       */
/* ====================================================================== */

/**
 * Red Color System - OKLCH
 * Base red with 5 tints and 5 shades for error states, alerts, danger
 */

/* Red Base Color */
.bg-red { background-color: var(--mico-color-red) !important; }
.text-red { color: var(--mico-color-red) !important; }

/* Red Tint Background Colors (lighter) */
.bg-red-2xlight { background-color: var(--mico-color-red-2xlight) !important; }
.bg-red-3xlight { background-color: var(--mico-color-red-3xlight) !important; }
.bg-red-4xlight { background-color: var(--mico-color-red-4xlight) !important; }
.bg-red-5xlight { background-color: var(--mico-color-red-5xlight) !important; }

/* Red Tint Text Colors (lighter) */
.text-red-2xlight { color: var(--mico-color-red-2xlight) !important; }
.text-red-3xlight { color: var(--mico-color-red-3xlight) !important; }
.text-red-4xlight { color: var(--mico-color-red-4xlight) !important; }
.text-red-5xlight { color: var(--mico-color-red-5xlight) !important; }

/* Red Shade Background Colors (darker) */
.bg-red-2xdark { background-color: var(--mico-color-red-2xdark) !important; }
.bg-red-3xdark { background-color: var(--mico-color-red-3xdark) !important; }
.bg-red-4xdark { background-color: var(--mico-color-red-4xdark) !important; }
.bg-red-5xdark { background-color: var(--mico-color-red-5xdark) !important; }

/* Red Shade Text Colors (darker) */
.text-red-2xdark { color: var(--mico-color-red-2xdark) !important; }
.text-red-3xdark { color: var(--mico-color-red-3xdark) !important; }
.text-red-4xdark { color: var(--mico-color-red-4xdark) !important; }
.text-red-5xdark { color: var(--mico-color-red-5xdark) !important; }

/**
 * Yellow Color System - OKLCH
 * Base yellow with 5 tints and 5 shades for warning states, highlights
 */

/* Yellow Base Color */
.bg-yellow { background-color: var(--mico-color-yellow) !important; }
.text-yellow { color: var(--mico-color-yellow) !important; }

/* Yellow Tint Background Colors (lighter) */
.bg-yellow-2xlight { background-color: var(--mico-color-yellow-2xlight) !important; }
.bg-yellow-3xlight { background-color: var(--mico-color-yellow-3xlight) !important; }
.bg-yellow-4xlight { background-color: var(--mico-color-yellow-4xlight) !important; }
.bg-yellow-5xlight { background-color: var(--mico-color-yellow-5xlight) !important; }

/* Yellow Tint Text Colors (lighter) */
.text-yellow-2xlight { color: var(--mico-color-yellow-2xlight) !important; }
.text-yellow-3xlight { color: var(--mico-color-yellow-3xlight) !important; }
.text-yellow-4xlight { color: var(--mico-color-yellow-4xlight) !important; }
.text-yellow-5xlight { color: var(--mico-color-yellow-5xlight) !important; }

/* Yellow Shade Background Colors (darker) */
.bg-yellow-2xdark { background-color: var(--mico-color-yellow-2xdark) !important; }
.bg-yellow-3xdark { background-color: var(--mico-color-yellow-3xdark) !important; }
.bg-yellow-4xdark { background-color: var(--mico-color-yellow-4xdark) !important; }
.bg-yellow-5xdark { background-color: var(--mico-color-yellow-5xdark) !important; }

/* Yellow Shade Text Colors (darker) */
.text-yellow-2xdark { color: var(--mico-color-yellow-2xdark) !important; }
.text-yellow-3xdark { color: var(--mico-color-yellow-3xdark) !important; }
.text-yellow-4xdark { color: var(--mico-color-yellow-4xdark) !important; }
.text-yellow-5xdark { color: var(--mico-color-yellow-5xdark) !important; }

/**
 * Green Color System - OKLCH
 * Base green with 5 tints and 5 shades for success states, positive actions
 */

/* Green Base Color */
.bg-green { background-color: var(--mico-color-green) !important; }
.text-green { color: var(--mico-color-green) !important; }

/* Green Tint Background Colors (lighter) */
.bg-green-2xlight { background-color: var(--mico-color-green-2xlight) !important; }
.bg-green-3xlight { background-color: var(--mico-color-green-3xlight) !important; }
.bg-green-4xlight { background-color: var(--mico-color-green-4xlight) !important; }
.bg-green-5xlight { background-color: var(--mico-color-green-5xlight) !important; }

/* Green Tint Text Colors (lighter) */
.text-green-2xlight { color: var(--mico-color-green-2xlight) !important; }
.text-green-3xlight { color: var(--mico-color-green-3xlight) !important; }
.text-green-4xlight { color: var(--mico-color-green-4xlight) !important; }
.text-green-5xlight { color: var(--mico-color-green-5xlight) !important; }

/* Green Shade Background Colors (darker) */
.bg-green-2xdark { background-color: var(--mico-color-green-2xdark) !important; }
.bg-green-3xdark { background-color: var(--mico-color-green-3xdark) !important; }
.bg-green-4xdark { background-color: var(--mico-color-green-4xdark) !important; }
.bg-green-5xdark { background-color: var(--mico-color-green-5xdark) !important; }

/* Green Shade Text Colors (darker) */
.text-green-2xdark { color: var(--mico-color-green-2xdark) !important; }
.text-green-3xdark { color: var(--mico-color-green-3xdark) !important; }
.text-green-4xdark { color: var(--mico-color-green-4xdark) !important; }
.text-green-5xdark { color: var(--mico-color-green-5xdark) !important; }

/**
 * Blue Color System - OKLCH
 * Base blue with 5 tints and 5 shades for information, links, primary actions
 */

/* Blue Base Color */
.bg-blue { background-color: var(--mico-color-blue) !important; }
.text-blue { color: var(--mico-color-blue) !important; }

/* Blue Tint Background Colors (lighter) */
.bg-blue-2xlight { background-color: var(--mico-color-blue-2xlight) !important; }
.bg-blue-3xlight { background-color: var(--mico-color-blue-3xlight) !important; }
.bg-blue-4xlight { background-color: var(--mico-color-blue-4xlight) !important; }
.bg-blue-5xlight { background-color: var(--mico-color-blue-5xlight) !important; }

/* Blue Tint Text Colors (lighter) */
.text-blue-2xlight { color: var(--mico-color-blue-2xlight) !important; }
.text-blue-3xlight { color: var(--mico-color-blue-3xlight) !important; }
.text-blue-4xlight { color: var(--mico-color-blue-4xlight) !important; }
.text-blue-5xlight { color: var(--mico-color-blue-5xlight) !important; }

/* Blue Shade Background Colors (darker) */
.bg-blue-2xdark { background-color: var(--mico-color-blue-2xdark) !important; }
.bg-blue-3xdark { background-color: var(--mico-color-blue-3xdark) !important; }
.bg-blue-4xdark { background-color: var(--mico-color-blue-4xdark) !important; }
.bg-blue-5xdark { background-color: var(--mico-color-blue-5xdark) !important; }

/* Blue Shade Text Colors (darker) */
.text-blue-2xdark { color: var(--mico-color-blue-2xdark) !important; }
.text-blue-3xdark { color: var(--mico-color-blue-3xdark) !important; }
.text-blue-4xdark { color: var(--mico-color-blue-4xdark) !important; }
.text-blue-5xdark { color: var(--mico-color-blue-5xdark) !important; }

/**
 * Indigo Color System - OKLCH
 * Base indigo with 5 tints and 5 shades for deep blues, professional themes
 */

/* Indigo Base Color */
.bg-indigo { background-color: var(--mico-color-indigo) !important; }
.text-indigo { color: var(--mico-color-indigo) !important; }

/* Indigo Tint Background Colors (lighter) */
.bg-indigo-2xlight { background-color: var(--mico-color-indigo-2xlight) !important; }
.bg-indigo-3xlight { background-color: var(--mico-color-indigo-3xlight) !important; }
.bg-indigo-4xlight { background-color: var(--mico-color-indigo-4xlight) !important; }
.bg-indigo-5xlight { background-color: var(--mico-color-indigo-5xlight) !important; }

/* Indigo Tint Text Colors (lighter) */
.text-indigo-2xlight { color: var(--mico-color-indigo-2xlight) !important; }
.text-indigo-3xlight { color: var(--mico-color-indigo-3xlight) !important; }
.text-indigo-4xlight { color: var(--mico-color-indigo-4xlight) !important; }
.text-indigo-5xlight { color: var(--mico-color-indigo-5xlight) !important; }

/* Indigo Shade Background Colors (darker) */
.bg-indigo-2xdark { background-color: var(--mico-color-indigo-2xdark) !important; }
.bg-indigo-3xdark { background-color: var(--mico-color-indigo-3xdark) !important; }
.bg-indigo-4xdark { background-color: var(--mico-color-indigo-4xdark) !important; }
.bg-indigo-5xdark { background-color: var(--mico-color-indigo-5xdark) !important; }

/* Indigo Shade Text Colors (darker) */
.text-indigo-2xdark { color: var(--mico-color-indigo-2xdark) !important; }
.text-indigo-3xdark { color: var(--mico-color-indigo-3xdark) !important; }
.text-indigo-4xdark { color: var(--mico-color-indigo-4xdark) !important; }
.text-indigo-5xdark { color: var(--mico-color-indigo-5xdark) !important; }

/**
 * Purple Color System - OKLCH
 * Base purple with 5 tints and 5 shades for creative themes, luxury
 */

/* Purple Base Color */
.bg-purple { background-color: var(--mico-color-purple) !important; }
.text-purple { color: var(--mico-color-purple) !important; }

/* Purple Tint Background Colors (lighter) */
.bg-purple-2xlight { background-color: var(--mico-color-purple-2xlight) !important; }
.bg-purple-3xlight { background-color: var(--mico-color-purple-3xlight) !important; }
.bg-purple-4xlight { background-color: var(--mico-color-purple-4xlight) !important; }
.bg-purple-5xlight { background-color: var(--mico-color-purple-5xlight) !important; }

/* Purple Tint Text Colors (lighter) */
.text-purple-2xlight { color: var(--mico-color-purple-2xlight) !important; }
.text-purple-3xlight { color: var(--mico-color-purple-3xlight) !important; }
.text-purple-4xlight { color: var(--mico-color-purple-4xlight) !important; }
.text-purple-5xlight { color: var(--mico-color-purple-5xlight) !important; }

/* Purple Shade Background Colors (darker) */
.bg-purple-2xdark { background-color: var(--mico-color-purple-2xdark) !important; }
.bg-purple-3xdark { background-color: var(--mico-color-purple-3xdark) !important; }
.bg-purple-4xdark { background-color: var(--mico-color-purple-4xdark) !important; }
.bg-purple-5xdark { background-color: var(--mico-color-purple-5xdark) !important; }

/* Purple Shade Text Colors (darker) */
.text-purple-2xdark { color: var(--mico-color-purple-2xdark) !important; }
.text-purple-3xdark { color: var(--mico-color-purple-3xdark) !important; }
.text-purple-4xdark { color: var(--mico-color-purple-4xdark) !important; }
.text-purple-5xdark { color: var(--mico-color-purple-5xdark) !important; }

/**
 * Pink Color System - OKLCH
 * Base pink with 5 tints and 5 shades for feminine themes, highlights
 */

/* Pink Base Color */
.bg-pink { background-color: var(--mico-color-pink) !important; }
.text-pink { color: var(--mico-color-pink) !important; }

/* Pink Tint Background Colors (lighter) */
.bg-pink-2xlight { background-color: var(--mico-color-pink-2xlight) !important; }
.bg-pink-3xlight { background-color: var(--mico-color-pink-3xlight) !important; }
.bg-pink-4xlight { background-color: var(--mico-color-pink-4xlight) !important; }
.bg-pink-5xlight { background-color: var(--mico-color-pink-5xlight) !important; }

/* Pink Tint Text Colors (lighter) */
.text-pink-2xlight { color: var(--mico-color-pink-2xlight) !important; }
.text-pink-3xlight { color: var(--mico-color-pink-3xlight) !important; }
.text-pink-4xlight { color: var(--mico-color-pink-4xlight) !important; }
.text-pink-5xlight { color: var(--mico-color-pink-5xlight) !important; }

/* Pink Shade Background Colors (darker) */
.bg-pink-2xdark { background-color: var(--mico-color-pink-2xdark) !important; }
.bg-pink-3xdark { background-color: var(--mico-color-pink-3xdark) !important; }
.bg-pink-4xdark { background-color: var(--mico-color-pink-4xdark) !important; }
.bg-pink-5xdark { background-color: var(--mico-color-pink-5xdark) !important; }

/* Pink Shade Text Colors (darker) */
.text-pink-2xdark { color: var(--mico-color-pink-2xdark) !important; }
.text-pink-3xdark { color: var(--mico-color-pink-3xdark) !important; }
.text-pink-4xdark { color: var(--mico-color-pink-4xdark) !important; }
.text-pink-5xdark { color: var(--mico-color-pink-5xdark) !important; }

/* ====================================================================== */
/* TRANSPARENCY COLOR UTILITIES - OKLCH ALPHA SYSTEM                     */
/* ====================================================================== */

/**
 * Creative transparency variations using OKLCH with alpha channels
 * These provide sophisticated overlay and background effects with superior color mixing
 */

/* Black Transparency Background Colors */
.bg-black-whisper { background-color: var(--mico-color-black-whisper) !important; }
.bg-black-breath { background-color: var(--mico-color-black-breath) !important; }
.bg-black-mist { background-color: var(--mico-color-black-mist) !important; }
.bg-black-veil { background-color: var(--mico-color-black-veil) !important; }
.bg-black-shadow { background-color: var(--mico-color-black-shadow) !important; }
.bg-black-shroud { background-color: var(--mico-color-black-shroud) !important; }
.bg-black-cloak { background-color: var(--mico-color-black-cloak) !important; }
.bg-black-eclipse { background-color: var(--mico-color-black-eclipse) !important; }
.bg-black-void { background-color: var(--mico-color-black-void) !important; }

/* Gray Transparency Background Colors */
.bg-gray-whisper { background-color: var(--mico-color-gray-whisper) !important; }
.bg-gray-breath { background-color: var(--mico-color-gray-breath) !important; }
.bg-gray-mist { background-color: var(--mico-color-gray-mist) !important; }
.bg-gray-veil { background-color: var(--mico-color-gray-veil) !important; }
.bg-gray-shadow { background-color: var(--mico-color-gray-shadow) !important; }
.bg-gray-shroud { background-color: var(--mico-color-gray-shroud) !important; }
.bg-gray-cloak { background-color: var(--mico-color-gray-cloak) !important; }
.bg-gray-eclipse { background-color: var(--mico-color-gray-eclipse) !important; }
.bg-gray-void { background-color: var(--mico-color-gray-void) !important; }

/* White Transparency Background Colors */
.bg-white-whisper { background-color: var(--mico-color-white-whisper) !important; }
.bg-white-breath { background-color: var(--mico-color-white-breath) !important; }
.bg-white-mist { background-color: var(--mico-color-white-mist) !important; }
.bg-white-veil { background-color: var(--mico-color-white-veil) !important; }
.bg-white-shadow { background-color: var(--mico-color-white-shadow) !important; }
.bg-white-shroud { background-color: var(--mico-color-white-shroud) !important; }
.bg-white-cloak { background-color: var(--mico-color-white-cloak) !important; }
.bg-white-eclipse { background-color: var(--mico-color-white-eclipse) !important; }
.bg-white-void { background-color: var(--mico-color-white-void) !important; }

/* Brand Color Transparency Background Colors */
.bg-primary-whisper { background-color: var(--mico-color-primary-whisper) !important; }
.bg-primary-breath { background-color: var(--mico-color-primary-breath) !important; }
.bg-primary-mist { background-color: var(--mico-color-primary-mist) !important; }
.bg-primary-veil { background-color: var(--mico-color-primary-veil) !important; }
.bg-primary-shadow { background-color: var(--mico-color-primary-shadow) !important; }

.bg-secondary-whisper { background-color: var(--mico-color-secondary-whisper) !important; }
.bg-secondary-breath { background-color: var(--mico-color-secondary-breath) !important; }
.bg-secondary-mist { background-color: var(--mico-color-secondary-mist) !important; }
.bg-secondary-veil { background-color: var(--mico-color-secondary-veil) !important; }
.bg-secondary-shadow { background-color: var(--mico-color-secondary-shadow) !important; }

.bg-accent-whisper { background-color: var(--mico-color-accent-whisper) !important; }
.bg-accent-breath { background-color: var(--mico-color-accent-breath) !important; }
.bg-accent-mist { background-color: var(--mico-color-accent-mist) !important; }
.bg-accent-veil { background-color: var(--mico-color-accent-veil) !important; }
.bg-accent-shadow { background-color: var(--mico-color-accent-shadow) !important; }

/* ====================================================================== */
/* SEMANTIC COLOR UTILITIES - OKLCH SYSTEM                               */
/* ====================================================================== */

/**
 * Colors that convey meaning and state information with OKLCH variations
 * Each semantic color includes 5 tints and 5 shades for comprehensive usage
 * All colors maintain WCAG AA contrast ratios for accessibility
 */

/* Semantic Base Colors */
.bg-success { background-color: var(--mico-color-success) !important; }
.bg-warning { background-color: var(--mico-color-warning) !important; }
.bg-error { background-color: var(--mico-color-error) !important; }
.bg-info { background-color: var(--mico-color-info) !important; }
.bg-visited { background-color: var(--mico-color-visited) !important; }

.text-success { color: var(--mico-color-success) !important; }
.text-warning { color: var(--mico-color-warning) !important; }
.text-error { color: var(--mico-color-error) !important; }
.text-info { color: var(--mico-color-info) !important; }
.text-visited { color: var(--mico-color-visited) !important; }

/* Success Color Variations */
.bg-success-2xlight { background-color: var(--mico-color-success-2xlight) !important; }
.bg-success-3xlight { background-color: var(--mico-color-success-3xlight) !important; }
.bg-success-4xlight { background-color: var(--mico-color-success-4xlight) !important; }
.bg-success-5xlight { background-color: var(--mico-color-success-5xlight) !important; }
.bg-success-2xdark { background-color: var(--mico-color-success-2xdark) !important; }
.bg-success-3xdark { background-color: var(--mico-color-success-3xdark) !important; }
.bg-success-4xdark { background-color: var(--mico-color-success-4xdark) !important; }
.bg-success-5xdark { background-color: var(--mico-color-success-5xdark) !important; }

.text-success-2xlight { color: var(--mico-color-success-2xlight) !important; }
.text-success-3xlight { color: var(--mico-color-success-3xlight) !important; }
.text-success-4xlight { color: var(--mico-color-success-4xlight) !important; }
.text-success-5xlight { color: var(--mico-color-success-5xlight) !important; }
.text-success-2xdark { color: var(--mico-color-success-2xdark) !important; }
.text-success-3xdark { color: var(--mico-color-success-3xdark) !important; }
.text-success-4xdark { color: var(--mico-color-success-4xdark) !important; }
.text-success-5xdark { color: var(--mico-color-success-5xdark) !important; }

/* Warning Color Variations */
.bg-warning-2xlight { background-color: var(--mico-color-warning-2xlight) !important; }
.bg-warning-3xlight { background-color: var(--mico-color-warning-3xlight) !important; }
.bg-warning-4xlight { background-color: var(--mico-color-warning-4xlight) !important; }
.bg-warning-5xlight { background-color: var(--mico-color-warning-5xlight) !important; }
.bg-warning-2xdark { background-color: var(--mico-color-warning-2xdark) !important; }
.bg-warning-3xdark { background-color: var(--mico-color-warning-3xdark) !important; }
.bg-warning-4xdark { background-color: var(--mico-color-warning-4xdark) !important; }
.bg-warning-5xdark { background-color: var(--mico-color-warning-5xdark) !important; }

.text-warning-2xlight { color: var(--mico-color-warning-2xlight) !important; }
.text-warning-3xlight { color: var(--mico-color-warning-3xlight) !important; }
.text-warning-4xlight { color: var(--mico-color-warning-4xlight) !important; }
.text-warning-5xlight { color: var(--mico-color-warning-5xlight) !important; }
.text-warning-2xdark { color: var(--mico-color-warning-2xdark) !important; }
.text-warning-3xdark { color: var(--mico-color-warning-3xdark) !important; }
.text-warning-4xdark { color: var(--mico-color-warning-4xdark) !important; }
.text-warning-5xdark { color: var(--mico-color-warning-5xdark) !important; }

/* Error Color Variations */
.bg-error-2xlight { background-color: var(--mico-color-error-2xlight) !important; }
.bg-error-3xlight { background-color: var(--mico-color-error-3xlight) !important; }
.bg-error-4xlight { background-color: var(--mico-color-error-4xlight) !important; }
.bg-error-5xlight { background-color: var(--mico-color-error-5xlight) !important; }
.bg-error-2xdark { background-color: var(--mico-color-error-2xdark) !important; }
.bg-error-3xdark { background-color: var(--mico-color-error-3xdark) !important; }
.bg-error-4xdark { background-color: var(--mico-color-error-4xdark) !important; }
.bg-error-5xdark { background-color: var(--mico-color-error-5xdark) !important; }

.text-error-2xlight { color: var(--mico-color-error-2xlight) !important; }
.text-error-3xlight { color: var(--mico-color-error-3xlight) !important; }
.text-error-4xlight { color: var(--mico-color-error-4xlight) !important; }
.text-error-5xlight { color: var(--mico-color-error-5xlight) !important; }
.text-error-2xdark { color: var(--mico-color-error-2xdark) !important; }
.text-error-3xdark { color: var(--mico-color-error-3xdark) !important; }
.text-error-4xdark { color: var(--mico-color-error-4xdark) !important; }
.text-error-5xdark { color: var(--mico-color-error-5xdark) !important; }

/* Info Color Variations */
.bg-info-2xlight { background-color: var(--mico-color-info-2xlight) !important; }
.bg-info-3xlight { background-color: var(--mico-color-info-3xlight) !important; }
.bg-info-4xlight { background-color: var(--mico-color-info-4xlight) !important; }
.bg-info-5xlight { background-color: var(--mico-color-info-5xlight) !important; }
.bg-info-2xdark { background-color: var(--mico-color-info-2xdark) !important; }
.bg-info-3xdark { background-color: var(--mico-color-info-3xdark) !important; }
.bg-info-4xdark { background-color: var(--mico-color-info-4xdark) !important; }
.bg-info-5xdark { background-color: var(--mico-color-info-5xdark) !important; }

.text-info-2xlight { color: var(--mico-color-info-2xlight) !important; }
.text-info-3xlight { color: var(--mico-color-info-3xlight) !important; }
.text-info-4xlight { color: var(--mico-color-info-4xlight) !important; }
.text-info-5xlight { color: var(--mico-color-info-5xlight) !important; }
.text-info-2xdark { color: var(--mico-color-info-2xdark) !important; }
.text-info-3xdark { color: var(--mico-color-info-3xdark) !important; }
.text-info-4xdark { color: var(--mico-color-info-4xdark) !important; }
.text-info-5xdark { color: var(--mico-color-info-5xdark) !important; }

/* Visited Color Variations */
.bg-visited-2xlight { background-color: var(--mico-color-visited-2xlight) !important; }
.bg-visited-3xlight { background-color: var(--mico-color-visited-3xlight) !important; }
.bg-visited-4xlight { background-color: var(--mico-color-visited-4xlight) !important; }
.bg-visited-5xlight { background-color: var(--mico-color-visited-5xlight) !important; }
.bg-visited-2xdark { background-color: var(--mico-color-visited-2xdark) !important; }
.bg-visited-3xdark { background-color: var(--mico-color-visited-3xdark) !important; }
.bg-visited-4xdark { background-color: var(--mico-color-visited-4xdark) !important; }
.bg-visited-5xdark { background-color: var(--mico-color-visited-5xdark) !important; }

.text-visited-2xlight { color: var(--mico-color-visited-2xlight) !important; }
.text-visited-3xlight { color: var(--mico-color-visited-3xlight) !important; }
.text-visited-4xlight { color: var(--mico-color-visited-4xlight) !important; }
.text-visited-5xlight { color: var(--mico-color-visited-5xlight) !important; }
.text-visited-2xdark { color: var(--mico-color-visited-2xdark) !important; }
.text-visited-3xdark { color: var(--mico-color-visited-3xdark) !important; }
.text-visited-4xdark { color: var(--mico-color-visited-4xdark) !important; }
.text-visited-5xdark { color: var(--mico-color-visited-5xdark) !important; }
