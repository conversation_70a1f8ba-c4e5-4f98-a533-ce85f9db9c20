/**
 * Mico CSS Framework
 * A lightweight and versatile CSS framework focused on utility classes
 * Version: 1.0.0
 * Author: <PERSON>
 */

/* Base and Theming */

/**
 * Mico CSS Framework - Core Variables
 *
 * This file defines the foundational CSS variables that power the Mico CSS framework.
 * These variables create a consistent design system that can be used throughout your project.
 *
 * USAGE:
 * Variables are accessed using the var() function:
 * Example: font-size: var(--mico-text-md);
 */

/* ========================================================================== */

/* RESPONSIVE BREAKPOINTS                                                     */

/* ========================================================================== */

:root {

--mico-shadow-hover-primary: 0px 4.5px 4px -3.8px hsl(from var(--mico-color-primary) h calc(s * 0.9) l) !important; 
--mico-shadow-hover-secondary: 0px 4.5px 4px -3.8px hsl(from var(--mico-color-secondary) h calc(s * 0.9) l) !important; 
--mico-shadow-hover-accent: 0px 4.5px 4px -3.8px hsl(from var(--mico-color-accent) h calc(s * 0.9) l) !important; 
  
--mico-shadow-focus-primary: 0px 4.6px 4px -3.8px hsl(from var(--mico-color-primary) h calc(s * 0.8) l) !important; 
--mico-shadow-focus-secondary: 0px 4.6px 4px -3.8px hsl(from var(--mico-color-secondary) h calc(s * 0.8) l) !important; 
--mico-shadow-focus-accent: 0px 4.6px 4px -3.8px hsl(from var(--mico-color-accent) h calc(s * 0.8) l) !important; 
  
--mico-shadow-active-primary: 0px 4.8px 4px -2.8px hsl(from var(--mico-color-primary) h calc(s * 0.7) l) !important; 
--mico-shadow-active-secondary: 0px 4.8px 4px -2.8px hsl(from var(--mico-color-secondary) h calc(s * 0.7) l) !important; 
--mico-shadow-active-accent: 0px 4.8px 4px -2.8px hsl(from var(--mico-color-accent) h calc(s * 0.7) l) !important;
}

:root {
  /**
   * Breakpoints define the viewport width thresholds for responsive design.
   * These values align with common device sizes and provide a consistent
   * foundation for responsive layouts.
   *
   * Usage: @media (min-width: var(--mico-breakpoint-md)) { ... }
   */
  --mico-breakpoint-xs: 320px;   /* Extra small devices (phones) */
  --mico-breakpoint-sm: 576px;   /* Small devices (large phones, portrait tablets) */
  --mico-breakpoint-md: 768px;   /* Medium devices (tablets) */
  --mico-breakpoint-lg: 992px;   /* Large devices (desktops) */
  --mico-breakpoint-xl: 1280px;  /* Extra large devices (large desktops) */
  --mico-breakpoint-2xl: 1440px; /* Ultra large devices (wide screens) */
  --mico-breakpoint-3xl: 1600px;

  /**
   * Common Values
   *
   * These variables define frequently used values to ensure consistency
   * and reduce repetition throughout the framework.
   */
  --mico-value-auto: auto;
  --mico-value-none: none;
  --mico-value-normal: normal;
  --mico-value-inherit: inherit;
  --mico-value-initial: initial;
  --mico-value-unset: unset;
  --mico-value-current: currentColor;
  --mico-value-transparent: transparent;
  --mico-value-0: 0;

  /**
   * Font Sizes using clamp() for responsive scaling
   *
   * The clamp() function takes three values: minimum, preferred, and maximum
   * This creates text that scales smoothly between viewport sizes while
   * maintaining readable minimum and maximum sizes.
   *
   * Format: clamp(min-size, viewport-based-size, max-size)
   */
  --mico-fs-xs: max(0.995rem, min(1.5vw, 0.975rem)); /* Extra small text, captions */
  --mico-fs-sm: max(0.980rem, min(2vw, 1.2rem));    /* Small text */
  --mico-fs-md: max(1.051rem, min(2.5vw, 1.30rem));  /* Standard body text */
  --mico-fs-lg: max(1.25rem, min(3vw, 1.675rem)); /* Small headings (h4) */
  --mico-fs-xl: max(1.5rem, min(4vw, 2rem));   /* Medium headings (h3) */
  --mico-fs-2xl: max(1.875rem, min(5vw, 3rem));   /* Large headings (h2) */
  --mico-fs-3xl: max(2.25rem, min(5.5vw, 3.5rem)); /* Large headings (h1) */
  --mico-fs-4xl: max(2.5rem, min(6vw, 4rem));     /* Large headings */
  --mico-fs-5xl: max(3rem, min(6.5vw, 4.5rem));   /* Large headings */
  --mico-fs-6xl: max(3.5rem, min(7vw, 5rem));     /* Large headings */
  --mico-fs-7xl: max(3.75rem, min(7.5vw, 5.5rem)); /* Very large headings */
  --mico-fs-8xl: max(4rem, min(8vw, 6rem));       /* Extra massive headings */
  --mico-fs-9xl: max(4.5rem, min(9vw, 7rem));

  /**
   * Font Weights
   *
   * Standard font weight values from 100 (thinnest) to 900 (boldest)
   * Using consistent naming pattern with descriptive suffixes
   */
  --mico-fw-100: 100;  /* Thinnest */
  --mico-fw-200: 200;  /* Extra light */
  --mico-fw-300: 300;  /* Light */
  --mico-fw-400: 400;  /* Normal/Regular */
  --mico-fw-500: 500;  /* Medium */
  --mico-fw-600: 600;  /* Semi bold */
  --mico-fw-700: 700;  /* Bold */
  --mico-fw-800: 800;  /* Extra bold */
  --mico-fw-900: 900;

  /**
   * Font Stretch Properties
   *
   * Controls the width of glyphs if the font family has variable widths.
   * Values represent percentage of normal width.
   */
  --mico-font-stretch-ultra-condensed: ultra-condensed; /* 50% */
  --mico-font-stretch-extra-condensed: extra-condensed; /* 62.5% */
  --mico-font-stretch-condensed: condensed;             /* 75% */
  --mico-font-stretch-semi-condensed: semi-condensed;   /* 87.5% */
  --mico-font-stretch-normal: var(--mico-value-normal); /* 100% */
  --mico-font-stretch-semi-expanded: semi-expanded;     /* 112.5% */
  --mico-font-stretch-expanded: expanded;               /* 125% */
  --mico-font-stretch-extra-expanded: extra-expanded;   /* 150% */
  --mico-font-stretch-ultra-expanded: ultra-expanded;

  /**
   * Font Style Properties
   *
   * Standard font style values for consistent usage.
   */
  --mico-font-style-normal: var(--mico-value-normal);
  --mico-font-style-italic: italic;

  /**
   * Font Variant Numeric Properties
   *
   * Controls numeric font features for enhanced typography.
   */
  --mico-font-variant-numeric-normal: var(--mico-value-normal);
  --mico-font-variant-numeric-ordinal: ordinal;
  --mico-font-variant-numeric-slashed-zero: slashed-zero;
  --mico-font-variant-numeric-lining-nums: lining-nums;
  --mico-font-variant-numeric-oldstyle-nums: oldstyle-nums;
  --mico-font-variant-numeric-proportional-nums: proportional-nums;
  --mico-font-variant-numeric-tabular-nums: tabular-nums;
  --mico-font-variant-numeric-diagonal-fractions: diagonal-fractions;
  --mico-font-variant-numeric-stacked-fractions: stacked-fractions;

  /**
   * Font Variant Ligatures Properties
   *
   * Controls ligature rendering for enhanced typography.
   */
  --mico-font-variant-ligatures-common: common-ligatures;
  --mico-font-variant-ligatures-no-common: no-common-ligatures;
  --mico-font-variant-ligatures-discretionary: discretionary-ligatures;
  --mico-font-variant-ligatures-no-discretionary: no-discretionary-ligatures;
  --mico-font-variant-ligatures-historical: historical-ligatures;
  --mico-font-variant-ligatures-no-historical: no-historical-ligatures;
  --mico-font-variant-ligatures-contextual: contextual;
  --mico-font-variant-ligatures-no-contextual: no-contextual;

  /**
   * Font Variant Caps Properties
   *
   * Controls capitalization rendering for enhanced typography.
   */
  --mico-font-variant-caps-normal: var(--mico-value-normal);
  --mico-font-variant-caps-small-caps: small-caps;
  --mico-font-variant-caps-all-small-caps: all-small-caps;
  --mico-font-variant-caps-petite-caps: petite-caps;
  --mico-font-variant-caps-all-petite-caps: all-petite-caps;
  --mico-font-variant-caps-unicase: unicase;
  --mico-font-variant-caps-titling-caps: titling-caps;

  /**
   * Line Heights
   *
   * Line height controls the vertical spacing between lines of text.
   * - Values below 1.5 are good for headings
   * - Values 1.5-1.7 are ideal for body text for readability
   * - Larger values create more spacing for easier reading
   */
  --mico-lh-xs: 1;       /* Compact (headings) */
  --mico-lh-sm: 1.25;    /* Slightly compact */
  --mico-lh-md: 1.5;     /* Standard body text */
  --mico-lh-lg: 1.625;   /* Slightly relaxed */
  --mico-lh-xl: 2;       /* Loose (easy reading) */
  --mico-lh-2xl: 0.75rem; /* Fixed line height for small text */
  --mico-lh-3xl: 1rem;    /* Fixed line height */
  --mico-lh-4xl: 1.25rem; /* Fixed line height */
  --mico-lh-5xl: 1.5rem;  /* Fixed line height */
  --mico-lh-6xl: 1.75rem; /* Fixed line height */
  --mico-lh-7xl: 2rem;    /* Fixed line height */
  --mico-lh-8xl: 2.25rem; /* Fixed line height */
  --mico-lh-9xl: 2.5rem;


  /**
   * Letter Spacing
   *
   * Controls the horizontal spacing between characters.
   * - Negative values bring letters closer together
   * - Positive values spread letters apart
   * - 'em' units scale with the font size
   */
  --mico-ls-xs: -0.05em;   /* Tighter spacing */
  --mico-ls-sm: -0.025em;  /* Slightly tighter */
  --mico-ls-md: 0em;       /* Normal spacing */
  --mico-ls-lg: 0.025em;   /* Slightly wider */
  --mico-ls-xl: 0.05em;    /* Wider spacing */
  --mico-ls-2xl: 0.1em;

  /**
   * Text Decoration Properties
   *
   * Controls the appearance of underlines and other text decorations
   */
  --mico-underline-offset-auto: var(--mico-value-auto);
  --mico-underline-offset-0: var(--mico-value-0);
  --mico-underline-offset-1: 1px;
  --mico-underline-offset-2: 2px;
  --mico-underline-offset-4: 4px;
  --mico-underline-offset-8: 8px;
  --mico-underline-offset: 0.15em;  /* Default distance between text and underline */

  --mico-decoration-thickness-auto: var(--mico-value-auto);
  --mico-decoration-thickness-from-font: from-font;
  --mico-decoration-thickness-0: 0px;
  --mico-decoration-thickness-1: 1px;
  --mico-decoration-thickness-2: 2px;
  --mico-decoration-thickness-4: 4px;
  --mico-decoration-thickness-8: 8px;
  --mico-underline-thickness: 0.05em; /* Default thickness of the underline */

  --mico-decoration-style-solid: solid;
  --mico-decoration-style-double: double;
  --mico-decoration-style-dotted: dotted;
  --mico-decoration-style-dashed: dashed;
  --mico-decoration-style-wavy: wavy;

  --mico-underline-position-auto: var(--mico-value-auto);
  --mico-underline-position-under: under;
  --mico-underline-position-left: left;
  --mico-underline-position-right: right;

  /**
   * Text Transform Properties
   *
   * Controls text case transformation
   */
  --mico-text-transform-uppercase: uppercase;
  --mico-text-transform-lowercase: lowercase;
  --mico-text-transform-capitalize: capitalize;
  --mico-text-transform-none: var(--mico-value-none);

  /**
   * Text Alignment Properties
   *
   * These variables define text alignment values for consistent usage.
   */
  --mico-text-align-left: left;
  --mico-text-align-center: center;
  --mico-text-align-right: right;
  --mico-text-align-justify: justify;
  --mico-text-align-start: start;
  --mico-text-align-end: end;

  /**
   * Text Overflow Properties
   *
   * Controls how overflowing text is handled
   */
  --mico-text-overflow-ellipsis: ellipsis;
  --mico-text-overflow-clip: clip;

  /**
   * White Space Properties
   *
   * Controls how whitespace is handled
   */
  --mico-whitespace-normal: var(--mico-value-normal);
  --mico-whitespace-nowrap: nowrap;
  --mico-whitespace-pre: pre;
  --mico-whitespace-pre-line: pre-line;
  --mico-whitespace-pre-wrap: pre-wrap;
  --mico-whitespace-break-spaces: break-spaces;



  /**
   * Text Indent Properties
   *
   * Controls the indentation of the first line of text
   */
  --mico-indent-0: var(--mico-value-0);
  --mico-indent-xs: 1px;
  --mico-indent-sm: 0.25rem;
  --mico-indent-md: 0.5rem;
  --mico-indent-lg: 1rem;
  --mico-indent-xl: 2rem;

  /**
   * Text Shadow Properties
   *
   * Controls text shadow effects
   */
  --mico-text-shadow-none: var(--mico-value-none);
  --mico-text-shadow-xs: 1px 1px 2px rgba(0, 0, 0, 0.1);
  --mico-text-shadow-sm: 2px 2px 4px rgba(0, 0, 0, 0.1);
  --mico-text-shadow-md: 4px 4px 6px rgba(0, 0, 0, 0.1);
  --mico-text-shadow-lg: 6px 6px 8px rgba(0, 0, 0, 0.15);

  /**
   * Text Stroke Properties
   *
   * Controls text stroke (outline) effects
   */
  --mico-text-stroke-xs: 1px;
  --mico-text-stroke-sm: 2px;
  --mico-text-stroke-md: 4px;

  /**
   * List Style Properties
   *
   * Controls list styling
   */
  --mico-list-style-type-none: var(--mico-value-none);
  --mico-list-style-type-disc: disc;
  --mico-list-style-type-decimal: decimal;
  --mico-list-style-type-square: square;
  --mico-list-style-type-upper-roman: upper-roman;
  --mico-list-style-type-lower-roman: lower-roman;
  --mico-list-style-type-upper-alpha: upper-alpha;
  --mico-list-style-type-lower-alpha: lower-alpha;

  --mico-list-style-position-inside: inside;
  --mico-list-style-position-outside: outside;

  /**
   * Text Direction Properties
   *
   * Controls text direction for internationalization
   */
  --mico-text-direction-ltr: ltr;
  --mico-text-direction-rtl: rtl;

  /**
   * Writing Mode Properties
   *
   * Controls text writing direction
   */
  --mico-writing-mode-horizontal-tb: horizontal-tb;
  --mico-writing-mode-vertical-rl: vertical-rl;
  --mico-writing-mode-vertical-lr: vertical-lr;

  /**
   * Text Orientation Properties
   *
   * Controls text orientation in vertical writing modes
   */
  --mico-text-orientation-mixed: mixed;
  --mico-text-orientation-upright: upright;
  --mico-text-orientation-sideways: sideways;

  /**
   * Hyphens Properties
   *
   * Controls automatic hyphenation
   */
  --mico-hyphens-none: var(--mico-value-none);
  --mico-hyphens-manual: manual;
  --mico-hyphens-auto: var(--mico-value-auto);

  /**
   * Text Align Last Properties
   *
   * Controls alignment of the last line in a block
   */
  --mico-text-align-last-auto: var(--mico-value-auto);
  --mico-text-align-last-start: start;
  --mico-text-align-last-end: end;
  --mico-text-align-last-left: left;
  --mico-text-align-last-right: right;
  --mico-text-align-last-center: center;
  --mico-text-align-last-justify: justify;

  /**
   * Text Justify Properties
   *
   * Controls how justified text is spaced
   */
  --mico-text-justify-auto: var(--mico-value-auto);
  --mico-text-justify-inter-word: inter-word;
  --mico-text-justify-inter-character: inter-character;
  --mico-text-justify-none: var(--mico-value-none);

  /**
   * User Select Properties
   *
   * Controls whether text can be selected
   */
  --mico-user-select-none: var(--mico-value-none);
  --mico-user-select-text: text;
  --mico-user-select-all: all;
  --mico-user-select-auto: var(--mico-value-auto);

  /**
   * Word Break Properties
   *
   * Controls how words break at line endings
   */
  --mico-word-break-normal: var(--mico-value-normal);
  --mico-word-break-break-all: break-all;
  --mico-word-break-keep-all: keep-all;
  --mico-overflow-wrap-normal: var(--mico-value-normal);
  --mico-overflow-wrap-break-word: break-word;

  /**
   * Spacing Scale
   *
   * A comprehensive spacing system based on a 4px unit.
   * This creates a consistent rhythm throughout the interface.
   *
   * The base unit (--mico-size-unit) is 4px, and all other spacing
   * values are multiples of this unit, making it easy to maintain
   * consistent proportional spacing.
   */
  --mico-size-unit: 4px;

  /* Core spacing values (most commonly used) */
  --mico-size-0: 0;                 /* No spacing */
  --mico-size-1: 1px;               /* Pixel-perfect adjustments */
  --mico-size-2: 2px;               /* Minimal spacing */
  --mico-size-4: calc(var(--mico-size-unit) * 1);    /* 4px - Tiny spacing */
  --mico-size-8: calc(var(--mico-size-unit) * 2);    /* 8px - Extra small spacing */
  --mico-size-12: calc(var(--mico-size-unit) * 3);   /* 12px - Small spacing */
  --mico-size-16: calc(var(--mico-size-unit) * 4);   /* 16px - Default spacing */
  --mico-size-20: calc(var(--mico-size-unit) * 5);   /* 20px - Medium spacing */
  --mico-size-24: calc(var(--mico-size-unit) * 6);   /* 24px - Medium spacing */
  --mico-size-28: calc(var(--mico-size-unit) * 7);   /* 28px - Medium spacing */
  --mico-size-32: calc(var(--mico-size-unit) * 8);   /* 32px - Large spacing */
  --mico-size-36: calc(var(--mico-size-unit) * 9);   /* 36px - Large spacing */
  --mico-size-40: calc(var(--mico-size-unit) * 10);  /* 40px - Extra large spacing */
  --mico-size-44: calc(var(--mico-size-unit) * 11);  /* 44px - Extra large spacing */
  --mico-size-48: calc(var(--mico-size-unit) * 12);  /* 48px - Extra large spacing */
  --mico-size-52: calc(var(--mico-size-unit) * 13);  /* 52px - Extra large spacing */
  --mico-size-56: calc(var(--mico-size-unit) * 14);  /* 56px - Huge spacing */
  --mico-size-60: calc(var(--mico-size-unit) * 15);  /* 60px - Huge spacing */
  --mico-size-64: calc(var(--mico-size-unit) * 16);  /* 64px - Huge spacing */
  --mico-size-72: calc(var(--mico-size-unit) * 18);  /* 72px - Huge spacing */
  --mico-size-80: calc(var(--mico-size-unit) * 20);  /* 80px - Huge spacing */
  --mico-size-96: calc(var(--mico-size-unit) * 24);  /* 96px - Giant spacing */
  --mico-size-100: calc(var(--mico-size-unit) * 25); /* 100px - Giant spacing */
  --mico-size-112: calc(var(--mico-size-unit) * 28); /* 112px - Section spacing */
  --mico-size-128: calc(var(--mico-size-unit) * 32); /* 128px - Section spacing */
  --mico-size-144: calc(var(--mico-size-unit) * 36); /* 144px - Large section spacing */
  --mico-size-160: calc(var(--mico-size-unit) * 40); /* 160px - Large section spacing */
  --mico-size-176: calc(var(--mico-size-unit) * 44); /* 176px - Extra large section spacing */
  --mico-size-192: calc(var(--mico-size-unit) * 48); /* 192px - Extra large section spacing */
  --mico-size-208: calc(var(--mico-size-unit) * 52); /* 208px - Huge section spacing */
  --mico-size-224: calc(var(--mico-size-unit) * 56); /* 224px - Huge section spacing */
  --mico-size-240: calc(var(--mico-size-unit) * 60); /* 240px - Maximum spacing */
  --mico-size-256: calc(var(--mico-size-unit) * 64); /* 256px - Maximum spacing */
  --mico-size-260: calc(var(--mico-size-unit) * 68); /* 260px - */
  --mico-size-264: calc(var(--mico-size-unit) * 72); /* 264px - */
  --mico-size-268: calc(var(--mico-size-unit) * 76); /* 268px - */
  --mico-size-272: calc(var(--mico-size-unit) * 80); /* 272px - */
  --mico-size-276: calc(var(--mico-size-unit) * 84); /* 276px - */
  --mico-size-280: calc(var(--mico-size-unit) * 88); /* 280px - */
  --mico-size-284: calc(var(--mico-size-unit) * 92); /* 284px - */
  --mico-size-288: calc(var(--mico-size-unit) * 96); /* 288px - */
  --mico-size-292: calc(var(--mico-size-unit) * 100); /* 292px - */
  --mico-size-296: calc(var(--mico-size-unit) * 104); /* 296px - */
  --mico-size-300: calc(var(--mico-size-unit) * 108); /* 300px - */
  --mico-size-304: calc(var(--mico-size-unit) * 112); /* 304px - */
  --mico-size-308: calc(var(--mico-size-unit) * 116); /* 308px - */
  --mico-size-312: calc(var(--mico-size-unit) * 120); /* 312px - */
  --mico-size-316: calc(var(--mico-size-unit) * 124); /* 316px - */
  --mico-size-320: calc(var(--mico-size-unit) * 128); /* 320px - */
  --mico-size-324: calc(var(--mico-size-unit) * 132); /* 324px - */
  --mico-size-328: calc(var(--mico-size-unit) * 136); /* 328px - */
  --mico-size-332: calc(var(--mico-size-unit) * 140); /* 332px - */
  --mico-size-336: calc(var(--mico-size-unit) * 144); /* 336px - */
  --mico-size-340: calc(var(--mico-size-unit) * 148); /* 340px - */
  --mico-size-344: calc(var(--mico-size-unit) * 152); /* 344px - */
  --mico-size-348: calc(var(--mico-size-unit) * 156); /* 348px - */
  --mico-size-352: calc(var(--mico-size-unit) * 160); /* 352px - */
  --mico-size-356: calc(var(--mico-size-unit) * 164); /* 356px - */
  --mico-size-360: calc(var(--mico-size-unit) * 168); /* 360px - */
  --mico-size-364: calc(var(--mico-size-unit) * 172); /* 364px - */
  --mico-size-368: calc(var(--mico-size-unit) * 176); /* 368px - */
  --mico-size-372: calc(var(--mico-size-unit) * 180); /* 372px - */
  --mico-size-376: calc(var(--mico-size-unit) * 184); /* 376px - */
  --mico-size-380: calc(var(--mico-size-unit) * 188); /* 380px - */
  --mico-size-384: calc(var(--mico-size-unit) * 192); /* 384px - */
  --mico-size-388: calc(var(--mico-size-unit) * 196); /* 388px - */
  --mico-size-392: calc(var(--mico-size-unit) * 200); /* 392px - */
  --mico-size-396: calc(var(--mico-size-unit) * 204); /* 396px - */
  --mico-size-400: calc(var(--mico-size-unit) * 208);



  /**
   * Main & Section Fluid Spacing Gutter
   *
   * Responsive spacing that adapts to viewport size.
   * Uses clamp() to create spacing that scales between a minimum and maximum value.
   *
   * Format: clamp(min-size, viewport-based-size, max-size)
   */
  --mico-size-fluid-xs: max(var(--mico-size-16), min(3vw, var(--mico-size-32)));
  --mico-size-fluid-sm: max(var(--mico-size-16), min(4vw, var(--mico-size-56)));
  --mico-size-fluid-md: max(var(--mico-size-16), min(6vw, var(--mico-size-80)));
  --mico-size-fluid-lg: max(var(--mico-size-16), min(8vw, var(--mico-size-100)));
  --mico-size-fluid-xl: max(var(--mico-size-16), min(10vw, var(--mico-size-128)));
  --mico-size-fluid-2xl: max(var(--mico-size-16), min(12vw, var(--mico-size-192)));

  /**
   * Border Radius
   *
   * Controls the roundness of element corners.
   * Consistent border radius values create a cohesive design language.
   */
  --mico-radius-none: 0;            /* No rounding */
  --mico-radius-xs: 1px;            /* Barely visible rounding */
  --mico-radius-sm: 2px;            /* Subtle rounding */
  --mico-radius-md: 4px;            /* Standard rounding */
  --mico-radius-lg: 8px;            /* Prominent rounding */
  --mico-radius-xl: 12px;           /* Very rounded corners */
  --mico-radius-2xl: 16px;          /* Extra rounded corners */
  --mico-radius-full: 9999px;

  /**
   * Border Styles
   *
   * Standard CSS border styles for consistent usage.
   * These variables make it easier to maintain consistent border styles.
   */
   
  --mico-border-none: none;
  --mico-border-solid: solid;
  --mico-border-dashed: dashed;
  --mico-border-dotted: dotted;
  --mico-border-double: double;
  --mico-border-groove: groove;
  --mico-border-ridge: ridge;
  --mico-border-inset: inset;
  --mico-border-outset: outset;

  /**
   * Border Widths
   *
   * Standard border thickness values.
   * These follow the same scale pattern as other properties.
   */
  --mico-border-width-0: 0px;       /* No border */
  --mico-border-width-1: 1px;       /* Thin border (standard) */
  --mico-border-width-2: 2px;       /* Medium border */
  --mico-border-width-4: 4px;       /* Thick border */
  --mico-border-width-8: 8px;
  

  /**
   * Outline Properties
   *
   * These variables define outline styles for accessibility and focus states.
   */
  --mico-outline-width-0: 0px;      /* No outline */
  --mico-outline-width-1: 1px;      /* Thin outline */
  --mico-outline-width-2: 2px;      /* Medium outline */
  --mico-outline-width-4: 4px;      /* Thick outline */
  --mico-outline-width-8: 8px;      /* Very thick outline */

  --mico-outline-style-none: none;  /* No outline */
  --mico-outline-style-solid: solid; /* Solid outline */
  --mico-outline-style-dashed: dashed; /* Dashed outline */
  --mico-outline-style-dotted: dotted; /* Dotted outline */
  --mico-outline-style-double: double; /* Double outline */

  --mico-outline-offset-0: 0px;     /* No offset */
  --mico-outline-offset-1: 1px;     /* Small offset */
  --mico-outline-offset-2: 2px;     /* Medium offset */
  --mico-outline-offset-4: 4px;     /* Large offset */
  --mico-outline-offset-8: 8px;

  /**
   * Divide Properties
   *
   * These variables define border styles for dividing child elements.
   */
  --mico-divide-width-0: 0px;       /* No divide border */
  --mico-divide-width-1: 1px;       /* Thin divide border */
  --mico-divide-width-2: 2px;       /* Medium divide border */
  --mico-divide-width-4: 4px;       /* Thick divide border */
  --mico-divide-width-8: 8px;

  /**
   * Box Shadows
   *
   * Creates depth and elevation in the interface.
   * Different shadow intensities represent different elevation levels.
   *
   * Light mode shadows use black with opacity for a subtle effect.
   * Dark mode shadows use white with opacity for a subtle effect.
   */
--mico-shadow-primary: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-primary) h calc(s * 0.55) l);
--mico-shadow-secondary: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-secondary) h calc(s * 0.55) l);
--mico-shadow-accent: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-accent) h calc(s * 0.55) l);
--mico-shadow-success: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-success) h calc(s * 0.55) l);
--mico-shadow-error: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-error) h calc(s * 0.55) l);
--mico-shadow-warning: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-warning) h calc(s * 0.55) l);
--mico-shadow-info: 0px 4.5px 4px -2.8px hsl(from var(--mico-color-info) h calc(s * 0.55) l); 

  
   /* Light Mode Shadows */
  --mico-shadow-none-light: var(--mico-value-none);
  --mico-shadow-xs-light: 0 2px 2px rgba(0, 0, 0, 0.05);  /* Subtle shadow */
  --mico-shadow-sm-light: 0 2px 2px rgba(0, 0, 0, 0.05);  /* Subtle shadow */
  --mico-shadow-md-light: 0 4px 6px rgba(0, 0, 0, 0.1);   /* Medium shadow */
  --mico-shadow-lg-light: 0 10px 15px rgba(0, 0, 0, 0.1); /* Large shadow */
  --mico-shadow-xl-light: 0 20px 25px rgba(0, 0, 0, 0.15); /* Extra large shadow */
  --mico-shadow-2xl-light: ;
  --mico-shadow-3xl-light: ;



  /* Inset Shadows (for pressed/inset effects) */
  --mico-shadow-inset-none-light: inset var(--mico-value-none);
  --mico-shadow-inset-xs-light: inset 0 2px 2px rgba(0, 0, 0, 0.05);  /* Subtle shadow */
  --mico-shadow-inset-sm-light: inset 0 1px 2px rgba(0, 0, 0, 0.05);
  --mico-shadow-inset-md-light: inset 0 4px 6px rgba(0, 0, 0, 0.1);
  --mico-shadow-inset-lg-light: inset 0 20px 25px rgba(0, 0, 0, 0.15); /* Extra large shadow */
  --mico-shadow-inset-xl-light: inset 0 20px 25px rgba(0, 0, 0, 0.20); /* Extra large shadow */
  --mico-shadow-inset-2xl-light: inset ;
  --mico-shadow-inset-3xl-light: inset ;

  /* Focus Shadow for accessibility */
  --mico-shadow-focus: 0 0 0 3px rgba(66, 153, 225, 0.5);

  /**
   * Position Properties
   *
   * These variables define CSS position values for consistent usage.
   * Position determines how an element is positioned in the document flow.
   */
  --mico-position-static: static;     /* Default positioning in document flow */
  --mico-position-relative: relative; /* Positioned relative to normal position */
  --mico-position-absolute: absolute; /* Positioned relative to nearest positioned ancestor */
  --mico-position-fixed: fixed;       /* Positioned relative to viewport */
  --mico-position-sticky: sticky;

  /**
   * Display Properties
   *
   * These variables define CSS display values for consistent usage.
   * Display determines how an element is rendered in the layout.
   */
  --mico-display-block: block;           /* Element generates a block box */
  --mico-display-inline: inline;         /* Element generates an inline box */
  --mico-display-inline-block: inline-block; /* Inline-level block container */
  --mico-display-flex: flex;             /* Flexible box layout */
  --mico-display-inline-flex: inline-flex; /* Inline-level flex container */
  --mico-display-grid: grid;             /* Grid layout */
  --mico-display-none: none;

  /**
   * Box Model Properties
   *
   * These variables define box model behavior for consistent usage.
   */
  --mico-box-sizing-border: border-box;  /* Width/height includes padding and border */
  --mico-box-sizing-content: content-box; /* Width/height excludes padding and border */
  --mico-box-decoration-slice: slice;    /* Background doesn't extend across fragments */
  --mico-box-decoration-clone: clone;

  /**
   * Overflow Properties
   *
   * These variables define how content that overflows the element's box is handled.
   */
  --mico-overflow-auto: auto;           /* Add scrollbars when needed */
  --mico-overflow-hidden: hidden;       /* Clip overflow content */
  --mico-overflow-visible: visible;     /* Content not clipped, may overflow */
  --mico-overflow-scroll: scroll;       /* Always show scrollbars */
  --mico-overscroll-auto: auto;         /* Default overscroll behavior */
  --mico-overscroll-contain: contain;   /* Prevent scroll chaining */
  --mico-overscroll-none: none;

  /**
   * Aspect Ratio Properties
   *
   * These variables define common aspect ratios for responsive elements.
   * Useful for maintaining proportional dimensions for images, videos, etc.
   */
  --mico-aspect-ratio-square: 1 / 1;        /* 1:1 ratio (square) */
  --mico-aspect-ratio-video: 16 / 9;        /* 16:9 ratio (standard video) */
  --mico-aspect-ratio-portrait: 3 / 4;      /* 3:4 ratio (portrait) */
  --mico-aspect-ratio-landscape: 4 / 3;     /* 4:3 ratio (landscape) */
  --mico-aspect-ratio-widescreen: 21 / 9;   /* 21:9 ratio (ultrawide) */
  --mico-aspect-ratio-golden: 1.618 / 1;

  /**
   * Float and Clear Properties
   *
   * These variables define float and clear values for consistent usage.
   * Float allows elements to be placed to the left or right of their container.
   */
  --mico-float-left: left;              /* Float element to the left */
  --mico-float-right: right;            /* Float element to the right */
  --mico-float-none: none;              /* Do not float element */
  --mico-clear-left: left;              /* Clear left floats */
  --mico-clear-right: right;            /* Clear right floats */
  --mico-clear-both: both;

  /**
   * Object Fit Properties
   *
   * These variables define how replaced elements (like images) should be resized.
   */
  --mico-object-fit-contain: contain;    /* Preserve aspect ratio, fit within box */
  --mico-object-fit-cover: cover;        /* Fill box, may crop image */
  --mico-object-fit-fill: fill;          /* Stretch to fill box */
  --mico-object-fit-scale-down: scale-down; /* Smaller of contain or none */
  --mico-object-position-center: center;

  /**
   * Visibility and Isolation Properties
   *
   * These variables define visibility and stacking context behavior.
   */
  --mico-visibility-visible: visible;    /* Element is visible */
  --mico-visibility-hidden: hidden;      /* Element is hidden but takes up space */
  --mico-visibility-collapse: collapse;  /* Element is hidden (for table rows/columns) */
  --mico-isolation-isolate: isolate;     /* Create new stacking context */
  --mico-isolation-auto: auto;

  /**
   * Positioning Properties
   *
   * These variables define common inset values for positioned elements.
   */
  --mico-inset-0: 0;                     /* No offset from container edges */
  --mico-inset-auto: auto;

  /* Flex Direction */
  --mico-flex-row: row;               /* Items arranged in a row */
  --mico-flex-row-reverse: row-reverse; /* Items arranged in a row, reversed */
  --mico-flex-col: column;            /* Items arranged in a column */
  --mico-flex-col-reverse: column-reverse;

  /* Flex Wrap */
  --mico-flex-wrap: wrap;             /* Items wrap to multiple lines */
  --mico-flex-nowrap: nowrap;         /* Items forced into a single line */
  --mico-flex-wrap-reverse: wrap-reverse;

  /* Justify Content (main axis) */
  --mico-justify-start: flex-start;   /* Items packed at start of container */
  --mico-justify-end: flex-end;       /* Items packed at end of container */
  --mico-justify-center: center;      /* Items centered in container */
  --mico-justify-between: space-between; /* Items evenly distributed with space between */
  --mico-justify-around: space-around; /* Items evenly distributed with space around */
  --mico-justify-evenly: space-evenly;

  /* Align Items (cross axis) */
  --mico-items-start: flex-start;     /* Items aligned at start of cross axis */
  --mico-items-end: flex-end;         /* Items aligned at end of cross axis */
  --mico-items-center: center;        /* Items centered on cross axis */
  --mico-items-baseline: baseline;    /* Items aligned by text baseline */
  --mico-items-stretch: stretch;
  /**
   * Grid Auto Properties
   *
   * These variables define how grid items are automatically placed and sized.
   * Auto-fit and auto-fill create responsive layouts without media queries.
   */
  --mico-grid-auto-fit: auto-fit;    /* Expands items to fill available space */
  --mico-grid-auto-fill: auto-fill;

  /**
   * Grid Placement Properties
   *
   * These variables define how grid items are placed within the grid container.
   * They control both individual items and groups of items.
   */
  --mico-place-items-start: start;    /* Items placed at start of their area */
  --mico-place-items-end: end;        /* Items placed at end of their area */
  --mico-place-items-center: center;  /* Items placed at center of their area */
  --mico-place-items-stretch: stretch; /* Items stretched to fill their area */

  --mico-place-content-start: start;    /* Content placed at start of grid */
  --mico-place-content-end: end;        /* Content placed at end of grid */
  --mico-place-content-center: center;  /* Content placed at center of grid */
  --mico-place-content-stretch: stretch; /* Content stretched to fill grid */
  --mico-place-content-around: space-around; /* Content evenly distributed with space around */
  --mico-place-content-between: space-between; /* Content evenly distributed with space between */
  --mico-place-content-evenly: space-evenly;

  /* Grid Configuration */
  --mico-grid-column-count: 12;       /* Default number of columns */
  --mico-grid-min-column-width: 200px; /* Minimum column width for responsive grids */
  --mico-grid-row-count: 1;           /* Default number of rows */
  --mico-grid-min-row-height: 100px;

  /* Grid Item Placement */
  --mico-column-span: 1;              /* Default column span for grid items */
  --mico-row-span: 1;                 /* Default row span for grid items */
  --mico-min-column-width: 0;         /* Minimum width for auto columns */
  --mico-max-column-width: 1fr;       /* Maximum width for auto columns */
  --mico-min-row-height: 0;           /* Minimum height for auto rows */
  --mico-max-row-height: 1fr;

  /* Grid Templates */
  --mico-grid-cols: repeat(var(--mico-grid-column-count, 12), minmax(0, 1fr)); /* Equal columns */
  --mico-grid-cols-auto-fit: repeat(auto-fit, minmax(var(--mico-grid-min-column-width, 200px), 1fr)); /* Responsive columns */
  --mico-grid-rows: repeat(var(--mico-grid-row-count, 1), minmax(0, 1fr));

  /* Grid Item Placement */
  --mico-col-span: span var(--mico-column-span, 1); /* Column span for grid items */
  --mico-row-span: span var(--mico-row-span, 1);

  /* Grid Flow */
  --mico-grid-flow-row: row;          /* Grid auto-placement by row */
  --mico-grid-flow-col: column;       /* Grid auto-placement by column */
  --mico-grid-flow-dense: dense;

  /* Grid Auto Columns and Rows */
  --mico-auto-cols: minmax(var(--mico-min-column-width, 0), var(--mico-max-column-width, 1fr));
  --mico-auto-rows: minmax(var(--mico-min-row-height, 0), var(--mico-max-row-height, 1fr));

  /* Grid Gap */
  --mico-gap-xs: var(--mico-size-4);  /* Extra small gap */
  --mico-gap-sm: var(--mico-size-8);  /* Small gap */
  --mico-gap-md: var(--mico-size-16); /* Medium gap (default) */
  --mico-gap-lg: var(--mico-size-24); /* Large gap */
  --mico-gap-xl: var(--mico-size-32);

  /* Background Repeat */
  --mico-bg-none: none;               /* No background image */
  --mico-bg-repeat: repeat;           /* Repeat in both directions */
  --mico-bg-no-repeat: no-repeat;     /* No repetition */
  --mico-bg-repeat-x: repeat-x;       /* Repeat horizontally only */
  --mico-bg-repeat-y: repeat-y;

  /* Background Attachment */
  --mico-bg-fixed: fixed;             /* Fixed to viewport */
  --mico-bg-local: local;             /* Scrolls with content */
  --mico-bg-scroll: scroll;

  /* Background Clip */
  --mico-bg-clip-border: border-box;  /* Extend to outer border edge */
  --mico-bg-clip-padding: padding-box; /* Extend to outer padding edge */
  --mico-bg-clip-content: content-box;

  /**
   * Filter Properties
   *
   * These variables define CSS filter effects for visual manipulation.
   * Filters can be combined to create complex visual effects.
   */
  --mico-filter-blur: blur(8px);           /* Blurs the element */
  --mico-filter-brightness: brightness(1.5); /* Adjusts brightness */
  --mico-filter-contrast: contrast(1.2);    /* Adjusts contrast */
  --mico-filter-grayscale: grayscale(100%); /* Converts to grayscale */
  --mico-filter-hue-rotate: hue-rotate(90deg); /* Shifts colors */
  --mico-filter-invert: invert(100%);      /* Inverts colors */
  --mico-filter-saturate: saturate(2);     /* Adjusts color saturation */
  --mico-filter-sepia: sepia(100%);

  /**
   * Opacity Properties
   *
   * These variables define standard opacity values for consistent usage.
   * Opacity controls the transparency of elements.
   */
  --mico-opacity-0p: 0;    
  --mico-opacity-10p: 0.10;               
  --mico-opacity-20p: 0.20; 
  --mico-opacity-30p: 0.30; 
  --mico-opacity-40p: 0.40; 
  --mico-opacity-50p: 0.5;    
  --mico-opacity-60p: 0.60;    
  --mico-opacity-70p: 0.70;  
  --mico-opacity-80p: 0.80;  
  --mico-opacity-90p: 0.90;  
  --mico-opacity-100p: 1;

  /* Scale Transforms */
  --mico-scale-100: scale(1);         /* Original size (no scaling) */
  --mico-scale-75: scale(0.75);       /* 75% of original size */
  --mico-scale-50: scale(0.5);

  /* Rotation Transforms */
  --mico-rotate-45: rotate(45deg);    /* 45-degree rotation */
  --mico-rotate-90: rotate(90deg);

  /* Translation Transforms */
  --mico-translate-x-full: translateX(100%); /* Move 100% right */
  --mico-translate-y-full: translateY(100%);

  /**
   * Table Properties
   *
   * These variables define table layout algorithms for consistent usage.
   * Table layout affects how tables calculate column widths.
   */
  --mico-table-auto: auto;            /* Automatic table layout algorithm */
  --mico-table-fixed: fixed;

  /**
   * SVG Properties
   *
   * These variables define common SVG property values for consistent usage.
   * These help maintain consistent styling between HTML and SVG elements.
   */
  --mico-fill-current: currentColor;  /* Use current text color for fill */
  --mico-stroke-current: currentColor;

  /**
   * Button Size Variables
   *
   * These variables define consistent button sizing across the framework.
   */
  --mico-btn-padding-xs: var(--mico-size-8) var(--mico-size-12);       /* Extra small button padding */
  --mico-btn-padding-sm: var(--mico-size-12) var(--mico-size-16);      /* Small button padding */
  --mico-btn-padding-md: var(--mico-size-16) var(--mico-size-24);      /* Medium button padding (default) */
  --mico-btn-padding-lg: var(--mico-size-20) var(--mico-size-32);      /* Large button padding */
  --mico-btn-padding-xl: var(--mico-size-24) var(--mico-size-40);      /* Extra large button padding */

  --mico-btn-font-size-xs: var(--mico-fs-xs);                          /* Extra small button font size */
  --mico-btn-font-size-sm: var(--mico-fs-sm);                          /* Small button font size */
  --mico-btn-font-size-md: var(--mico-fs-md);                          /* Medium button font size (default) */
  --mico-btn-font-size-lg: var(--mico-fs-lg);                          /* Large button font size */
  --mico-btn-font-size-xl: var(--mico-fs-xl);

  /**
   * Button Border Radius Variables
   *
   * These variables define button border radius options.
   */
  --mico-btn-radius-square: var(--mico-radius-none);                    /* Square buttons (no radius) */
  --mico-btn-radius-sm: var(--mico-radius-sm);                         /* Small radius */
  --mico-btn-radius-md: var(--mico-radius-md);                         /* Medium radius (default) */
  --mico-btn-radius-lg: var(--mico-radius-lg);                         /* Large radius */
  --mico-btn-radius-pill: var(--mico-radius-full);                     /* Pill-shaped buttons */
  --mico-btn-radius-circle: var(--mico-radius-full);

  /**
   * Button Shadow Variables
   *
   * These variables define button shadow options for depth and elevation.
   */
  --mico-btn-shadow-none: none;                                         /* No shadow */
  --mico-btn-shadow-sm: var(--mico-shadow-sm);                         /* Small shadow */
  --mico-btn-shadow-md: var(--mico-shadow-md);                         /* Medium shadow (default) */
  --mico-btn-shadow-lg: var(--mico-shadow-lg);

  /**
   * Button Icon Variables
   *
   * These variables define spacing and sizing for buttons with icons.
   */
  --mico-btn-icon-gap: var(--mico-size-8);                             /* Gap between icon and text */
  --mico-btn-icon-only-size: var(--mico-size-40);

  /**
   * Cursor Properties
   *
   * These variables define cursor styles for different interactive states.
   * Cursors provide visual feedback about what the user can do.
   */
  --mico-cursor-auto: auto;           /* Browser default cursor */
  --mico-cursor-default: default;     /* Default cursor (arrow) */
  --mico-cursor-pointer: pointer;     /* Pointing hand (for links) */
  --mico-cursor-wait: wait;           /* Waiting (hourglass) */
  --mico-cursor-text: text;           /* Text selection (I-beam) */
  --mico-cursor-move: move;           /* Movement indicator */
  --mico-cursor-not-allowed: not-allowed; /* Forbidden action */
  --mico-cursor-grab: grab;           /* Grabbable element */
  --mico-cursor-grabbing: grabbing;   /* Element being grabbed */
  --mico-cursor-help: help;

  /**
   * Appearance Variables
   *
   * These variables control the appearance property for form elements.
   */
  --mico-appearance-none: none;       /* Remove default browser styling */
  --mico-appearance-auto: auto;

  /**
   * Pointer Events Variables
   *
   * These variables control pointer event behavior.
   */
  --mico-pointer-events-none: none;   /* Element cannot be target of pointer events */
  --mico-pointer-events-auto: auto;

  /**
   * Will Change Variables
   *
   * These variables provide performance hints to the browser.
   * Use with caution as they can consume more resources if overused.
   */
  --mico-will-change-auto: auto;      /* Browser decides what to optimize */
  --mico-will-change-transform: transform; /* Optimize for transform changes */
  --mico-will-change-opacity: opacity; /* Optimize for opacity changes */
  --mico-will-change-scroll: scroll-position;

  /**
   * Bleed Utility Variables
   *
   * These variables control full-bleed and column-bleed effects.
   */
  --mico-bleed-offset-sm: 10vw;       /* Small bleed offset */
  --mico-bleed-offset-md: 20vw;       /* Medium bleed offset (default) */
  --mico-bleed-offset-lg: 30vw;

  /**
   * Mask Variables
   *
   * These variables define CSS mask gradients for fade effects.
   */
  --mico-mask-fade-to-top: linear-gradient(to top, black 50%, transparent 100%);
  --mico-mask-fade-to-bottom: linear-gradient(to bottom, black 50%, transparent 100%);
  --mico-mask-fade-to-left: linear-gradient(to left, black 50%, transparent 100%);
  --mico-mask-fade-to-right: linear-gradient(to right, black 50%, transparent 100%);

  /* Fade intensity variations */
  --mico-mask-fade-short: linear-gradient(to bottom, black 80%, transparent 100%);
  --mico-mask-fade-long: linear-gradient(to bottom, black 20%, transparent 100%);

  /* Content-based Sizing */
  --mico-fit-content: fit-content;    /* Size based on content with constraints */
  --mico-min-content: min-content;    /* Smallest size that fits content */
  --mico-max-content: max-content;

  /* Percentage-based Sizing */
  --mico-width-full: 100%;            /* Full width of container */
  --mico-height-full: 100%;           /* Full height of container */
  --mico-width-half: 50%;             /* Half width of container */
  --mico-height-half: 50%;            /* Half height of container */
  --mico-width-quarter: 25%;           /* Quarter width of container */
  --mico-height-quarter: 25%;         /* Quarter height of container */
  --mico-width-third: 33.33%;         /* Third width of container */
  --mico-height-third: 33.33%;

  /* Viewport-based Sizing */
  --mico-width-screen: 100vw;         /* Full viewport width */
  --mico-height-screen: 100vh;

  /* Min/Max Constraints */
  --mico-min-width-0: 0;              /* No minimum width */
  --mico-min-height-0: 0;             /* No minimum height */
  --mico-max-width-full: 100%;        /* Maximum width of container */
  --mico-max-height-full: 100%;

  /* Basic Vertical Alignment */
  --mico-align-baseline: baseline;    /* Align to text baseline */
  --mico-align-top: top;              /* Align to top */
  --mico-align-middle: middle;        /* Align to middle */
  --mico-align-bottom: bottom;        /* Align to bottom */
  --mico-align-text-top: text-top;    /* Align to top of text */
  --mico-align-text-bottom: text-bottom;

  /* Extended Vertical Alignment */
  --mico-vertical-align-sub: sub;     /* Subscript alignment */
  --mico-vertical-align-super: super;

  /**
   * Animation Duration Variables
   *
   * These variables define animation durations for consistent timing across the framework.
   * Used by both transitions and animations for unified motion design.
   */
  --mico-duration-xs: 0.2s;          /* Extra fast animations */
  --mico-duration-sm: 0.5s;          /* Fast animations */
  --mico-duration-md: 0.8s;          /* Standard animations */
  --mico-duration-lg: 1.2s;          /* Slow animations */
  --mico-duration-xl: 2s;

  /**
   * Animation Delay Variables
   *
   * These variables define animation delays for staggered effects.
   */
  --mico-delay-xs: 0.1s;             /* Minimal delay */
  --mico-delay-sm: 0.2s;             /* Small delay */
  --mico-delay-md: 0.4s;             /* Medium delay */
  --mico-delay-lg: 0.6s;             /* Large delay */
  --mico-delay-xl: 0.8s;

  /**
   * Animation Engine Variables
   *
   * These variables control the default behavior of the animation engine.
   */
  --mico-anim-default-duration: var(--mico-duration-md);        /* Default animation duration */
  --mico-anim-default-timing: ease-out;                         /* Default timing function */
  --mico-anim-default-fill-mode: both;

  /**
   * Interactive Animation Variables
   *
   * These variables control interactive elements like ripples and typewriter effects.
   */
  --mico-ripple-bg-default: rgba(255, 255, 255, 0.3);          /* Default ripple background */
  --mico-ripple-bg-dark: rgba(0, 0, 0, 0.2);                   /* Dark ripple background */
  --mico-typewriter-cursor-color: currentColor;

  /**
   * Animation Properties
   *
   * These variables define common animation presets for consistent usage.
   * Animations create movement and visual interest in the interface.
   */
  --mico-animation-none: none;        /* No animation */
  --mico-animation-spin: spin 1s linear infinite; /* Spinning animation */
  --mico-animation-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite; /* Ping/pulse effect */
  --mico-animation-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; /* Subtle pulse */
  --mico-animation-bounce: bounce 1s infinite;

  /* Standard Easing Functions */
  --mico-ease: cubic-bezier(0.25, 0.1, 0.25, 1.0); /* Standard ease */
  --mico-ease-in: cubic-bezier(0.42, 0, 1.0, 1.0); /* Slow start, fast end */
  --mico-ease-out: cubic-bezier(0, 0, 0.58, 1.0); /* Fast start, slow end */
  --mico-ease-in-out: cubic-bezier(0.42, 0, 0.58, 1.0);

  /* Expressive Easing Functions */
  --mico-ease-elastic: cubic-bezier(0.68, -0.55, 0.265, 1.55); /* Elastic/bouncy */
  --mico-ease-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bouncy end */
  --mico-ease-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Physics-inspired Easing */
  --mico-ease-spring: cubic-bezier(0.5, 0.1, 0.1, 1); /* Spring-like motion */
  --mico-ease-gravity: cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Gravity effect */
  --mico-ease-snappy: cubic-bezier(0.1, 0.9, 0.2, 1);

  /* Transition Durations */
  --mico-transition-duration-fast: 150ms;  /* Fast transitions */
  --mico-transition-duration-normal: 300ms; /* Standard transitions */
  --mico-transition-duration-slow: 500ms;

  /* Common Transitions */
  --mico-transition-all: all .4s var(--mico-ease); /* All properties */
  --mico-transition-color: color .4s var(--mico-ease); /* Color only */
  --mico-transition-background: background .4s var(--mico-ease); /* Background only */
  --mico-transition-border: border .4s var(--mico-ease); /* Border only */
  --mico-transition-opacity: opacity .4s var(--mico-ease); /* Opacity only */
  --mico-transition-transform: transform .4s var(--mico-ease); /* Transform only */
  --mico-transition-box-shadow: box-shadow .4s var(--mico-ease);

  /**
   * Brand Colors - Primary
   * Base color defined in styleguide.css, variations auto-generated here
   */
  --mico-color-primary: rgb(39, 132, 213);

  /* Primary Color Tints (lighter variations) */
  --mico-color-primary-2xlight: oklch(from var(--mico-color-primary) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft tint */
  --mico-color-primary-3xlight: oklch(from var(--mico-color-primary) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium tint */
  --mico-color-primary-4xlight: oklch(from var(--mico-color-primary) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light tint */
  --mico-color-primary-5xlight: oklch(from var(--mico-color-primary) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);

  /* Primary Color Shades (darker variations) */
  --mico-color-primary-2xdark: oklch(from var(--mico-color-primary) calc(l * 0.8) calc(c * 1.1) h); /* Soft shade */
  --mico-color-primary-3xdark: oklch(from var(--mico-color-primary) calc(l * 0.6) calc(c * 1.2) h); /* Medium shade */
  --mico-color-primary-4xdark: oklch(from var(--mico-color-primary) calc(l * 0.4) calc(c * 1.3) h); /* Dark shade */
  --mico-color-primary-5xdark: oklch(from var(--mico-color-primary) calc(l * 0.25) calc(c * 1.4) h);

  /**
   * Brand Colors - Secondary
   * Base color defined in styleguide.css, variations auto-generated here
   */
  --mico-color-secondary: rgb(81, 97, 145);

  /* Secondary Color Tints (lighter variations) */
  --mico-color-secondary-2xlight: oklch(from var(--mico-color-secondary) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft tint */
  --mico-color-secondary-3xlight: oklch(from var(--mico-color-secondary) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium tint */
  --mico-color-secondary-4xlight: oklch(from var(--mico-color-secondary) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light tint */
  --mico-color-secondary-5xlight: oklch(from var(--mico-color-secondary) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);

  /* Secondary Color Shades (darker variations) */
  --mico-color-secondary-2xdark: oklch(from var(--mico-color-secondary) calc(l * 0.8) calc(c * 1.1) h); /* Soft shade */
  --mico-color-secondary-3xdark: oklch(from var(--mico-color-secondary) calc(l * 0.6) calc(c * 1.2) h); /* Medium shade */
  --mico-color-secondary-4xdark: oklch(from var(--mico-color-secondary) calc(l * 0.4) calc(c * 1.3) h); /* Dark shade */
  --mico-color-secondary-5xdark: oklch(from var(--mico-color-secondary) calc(l * 0.25) calc(c * 1.4) h);

  /**
   * Brand Colors - Accent
   * Base color defined in styleguide.css, variations auto-generated here
   */
  --mico-color-accent: rgb(236, 125, 0);

  /* Accent Color Tints (lighter variations) */
  --mico-color-accent-2xlight: oklch(from var(--mico-color-accent) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft tint */
  --mico-color-accent-3xlight: oklch(from var(--mico-color-accent) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium tint */
  --mico-color-accent-4xlight: oklch(from var(--mico-color-accent) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light tint */
  --mico-color-accent-5xlight: oklch(from var(--mico-color-accent) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);

  /* Accent Color Shades (darker variations) */
  --mico-color-accent-2xdark: oklch(from var(--mico-color-accent) calc(l * 0.8) calc(c * 1.1) h); /* Soft shade */
  --mico-color-accent-3xdark: oklch(from var(--mico-color-accent) calc(l * 0.6) calc(c * 1.2) h); /* Medium shade */
  --mico-color-accent-4xdark: oklch(from var(--mico-color-accent) calc(l * 0.4) calc(c * 1.3) h); /* Dark shade */
  --mico-color-accent-5xdark: oklch(from var(--mico-color-accent) calc(l * 0.25) calc(c * 1.4) h);

  /* Success Color System */
  --mico-color-success: rgb(58, 168, 91);

  /* Success Tints (lighter variations) */
  --mico-color-success-2xlight: oklch(from var(--mico-color-success) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft success tint */
  --mico-color-success-3xlight: oklch(from var(--mico-color-success) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium success tint */
  --mico-color-success-4xlight: oklch(from var(--mico-color-success) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light success tint */
  --mico-color-success-5xlight: oklch(from var(--mico-color-success) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);

  /* Success Shades (darker variations) */
  --mico-color-success-2xdark: oklch(from var(--mico-color-success) calc(l * 0.8) calc(c * 1.1) h); /* Soft success shade */
  --mico-color-success-3xdark: oklch(from var(--mico-color-success) calc(l * 0.6) calc(c * 1.2) h); /* Medium success shade */
  --mico-color-success-4xdark: oklch(from var(--mico-color-success) calc(l * 0.4) calc(c * 1.3) h); /* Dark success shade */
  --mico-color-success-5xdark: oklch(from var(--mico-color-success) calc(l * 0.25) calc(c * 1.4) h);

  /* Warning Color System */
  --mico-color-warning: rgb(223, 161, 26);

  /* Warning Tints (lighter variations) */
  --mico-color-warning-2xlight: oklch(from var(--mico-color-warning) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft warning tint */
  --mico-color-warning-3xlight: oklch(from var(--mico-color-warning) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium warning tint */
  --mico-color-warning-4xlight: oklch(from var(--mico-color-warning) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light warning tint */
  --mico-color-warning-5xlight: oklch(from var(--mico-color-warning) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);

  /* Warning Shades (darker variations) */
  --mico-color-warning-2xdark: oklch(from var(--mico-color-warning) calc(l * 0.8) calc(c * 1.1) h); /* Soft warning shade */
  --mico-color-warning-3xdark: oklch(from var(--mico-color-warning) calc(l * 0.6) calc(c * 1.2) h); /* Medium warning shade */
  --mico-color-warning-4xdark: oklch(from var(--mico-color-warning) calc(l * 0.4) calc(c * 1.3) h); /* Dark warning shade */
  --mico-color-warning-5xdark: oklch(from var(--mico-color-warning) calc(l * 0.25) calc(c * 1.4) h);

  /* Error Color System */
  --mico-color-error: rgb(215, 71, 69);

  /* Error Tints (lighter variations) */
  --mico-color-error-2xlight: oklch(from var(--mico-color-error) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft error tint */
  --mico-color-error-3xlight: oklch(from var(--mico-color-error) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium error tint */
  --mico-color-error-4xlight: oklch(from var(--mico-color-error) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light error tint */
  --mico-color-error-5xlight: oklch(from var(--mico-color-error) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);

  /* Error Shades (darker variations) */
  --mico-color-error-2xdark: oklch(from var(--mico-color-error) calc(l * 0.8) calc(c * 1.1) h); /* Soft error shade */
  --mico-color-error-3xdark: oklch(from var(--mico-color-error) calc(l * 0.6) calc(c * 1.2) h); /* Medium error shade */
  --mico-color-error-4xdark: oklch(from var(--mico-color-error) calc(l * 0.4) calc(c * 1.3) h); /* Dark error shade */
  --mico-color-error-5xdark: oklch(from var(--mico-color-error) calc(l * 0.25) calc(c * 1.4) h);

  /* Info Color System */
  --mico-color-info: rgb(39, 132, 213);

  /* Info Tints (lighter variations) */
  --mico-color-info-2xlight: oklch(from var(--mico-color-info) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft info tint */
  --mico-color-info-3xlight: oklch(from var(--mico-color-info) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium info tint */
  --mico-color-info-4xlight: oklch(from var(--mico-color-info) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light info tint */
  --mico-color-info-5xlight: oklch(from var(--mico-color-info) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);

  /* Info Shades (darker variations) */
  --mico-color-info-2xdark: oklch(from var(--mico-color-info) calc(l * 0.8) calc(c * 1.1) h); /* Soft info shade */
  --mico-color-info-3xdark: oklch(from var(--mico-color-info) calc(l * 0.6) calc(c * 1.2) h); /* Medium info shade */
  --mico-color-info-4xdark: oklch(from var(--mico-color-info) calc(l * 0.4) calc(c * 1.3) h); /* Dark info shade */
  --mico-color-info-5xdark: oklch(from var(--mico-color-info) calc(l * 0.25) calc(c * 1.4) h);

  /* Visited Color System */
  --mico-color-visited: rgb(128, 89, 187);

  /* Visited Tints (lighter variations) */
  --mico-color-visited-2xlight: oklch(from var(--mico-color-visited) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft visited tint */
  --mico-color-visited-3xlight: oklch(from var(--mico-color-visited) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium visited tint */
  --mico-color-visited-4xlight: oklch(from var(--mico-color-visited) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light visited tint */
  --mico-color-visited-5xlight: oklch(from var(--mico-color-visited) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);

  /* Visited Shades (darker variations) */
  --mico-color-visited-2xdark: oklch(from var(--mico-color-visited) calc(l * 0.8) calc(c * 1.1) h); /* Soft visited shade */
  --mico-color-visited-3xdark: oklch(from var(--mico-color-visited) calc(l * 0.6) calc(c * 1.2) h); /* Medium visited shade */
  --mico-color-visited-4xdark: oklch(from var(--mico-color-visited) calc(l * 0.4) calc(c * 1.3) h); /* Dark visited shade */
  --mico-color-visited-5xdark: oklch(from var(--mico-color-visited) calc(l * 0.25) calc(c * 1.4) h);

  /**
   * Neutral Colors - Black System (OKLCH)
   *
   * Black color variations with 5 tints and 5 shades.
   * These provide depth and contrast in dark themes with superior perceptual uniformity.
   */
  --mico-color-black: rgb(3, 3, 4);

  /* Black Tints (lighter variations) */
  --mico-color-black-2xlight: oklch(from var(--mico-color-black) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft black tint */
  --mico-color-black-3xlight: oklch(from var(--mico-color-black) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium black tint */
  --mico-color-black-4xlight: oklch(from var(--mico-color-black) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light black tint */
  --mico-color-black-5xlight: oklch(from var(--mico-color-black) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);

  /* Black Shades (darker variations) */
  --mico-color-black-2xdark: oklch(from var(--mico-color-black) calc(l * 0.8) calc(c * 1.1) h); /* Soft black shade */
  --mico-color-black-3xdark: oklch(from var(--mico-color-black) calc(l * 0.6) calc(c * 1.2) h); /* Medium black shade */
  --mico-color-black-4xdark: oklch(from var(--mico-color-black) calc(l * 0.4) calc(c * 1.3) h); /* Dark black shade */
  --mico-color-black-5xdark: oklch(from var(--mico-color-black) calc(l * 0.25) calc(c * 1.4) h);

  /**
   * Neutral Colors - Gray System (OKLCH)
   *
   * Comprehensive gray palette with 5 tints and 5 shades for UI elements, text, and backgrounds.
   * These colors provide optimal contrast and accessibility with perceptual uniformity.
   */
  --mico-color-gray: rgb(97, 99, 105);

  /* Gray Tints (lighter variations) */
  --mico-color-gray-2xlight: oklch(from var(--mico-color-gray) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft gray tint */
  --mico-color-gray-3xlight: oklch(from var(--mico-color-gray) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium gray tint */
  --mico-color-gray-4xlight: oklch(from var(--mico-color-gray) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light gray tint */
  --mico-color-gray-5xlight: oklch(from var(--mico-color-gray) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);

  /* Gray Shades (darker variations) */
  --mico-color-gray-2xdark: oklch(from var(--mico-color-gray) calc(l * 0.8) calc(c * 1.1) h); /* Soft gray shade */
  --mico-color-gray-3xdark: oklch(from var(--mico-color-gray) calc(l * 0.6) calc(c * 1.2) h); /* Medium gray shade */
  --mico-color-gray-4xdark: oklch(from var(--mico-color-gray) calc(l * 0.4) calc(c * 1.3) h); /* Dark gray shade */
  --mico-color-gray-5xdark: oklch(from var(--mico-color-gray) calc(l * 0.25) calc(c * 1.4) h);

  /**
   * Neutral Colors - White System (OKLCH)
   *
   * White color variations with 5 tints and 5 shades for subtle backgrounds and overlays.
   * These provide clean, minimal aesthetics in light themes with superior color precision.
   */
  --mico-color-white: rgb(237, 238, 242);

  /* White Tints (lighter variations) */
  --mico-color-white-2xlight: oklch(from var(--mico-color-white) calc(l + (1 - l) * 0.3) calc(c * 0.8) h); /* Soft white tint */
  --mico-color-white-3xlight: oklch(from var(--mico-color-white) calc(l + (1 - l) * 0.5) calc(c * 0.6) h); /* Medium white tint */
  --mico-color-white-4xlight: oklch(from var(--mico-color-white) calc(l + (1 - l) * 0.7) calc(c * 0.4) h); /* Light white tint */
  --mico-color-white-5xlight: oklch(from var(--mico-color-white) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);

  /* White Shades (darker variations) */
  --mico-color-white-2xdark: oklch(from var(--mico-color-white) calc(l * 0.8) calc(c * 1.1) h); /* Soft white shade */
  --mico-color-white-3xdark: oklch(from var(--mico-color-white) calc(l * 0.6) calc(c * 1.2) h); /* Medium white shade */
  --mico-color-white-4xdark: oklch(from var(--mico-color-white) calc(l * 0.4) calc(c * 1.3) h); /* Dark white shade */
  --mico-color-white-5xdark: oklch(from var(--mico-color-white) calc(l * 0.25) calc(c * 1.4) h);

  /* Black Transparency Variations */
  --mico-color-black-whisper: oklch(from var(--mico-color-black) l c h / 0.1);    /* Subtle black overlay */
  --mico-color-black-breath: oklch(from var(--mico-color-black) l c h / 0.2);     /* Light black overlay */
  --mico-color-black-mist: oklch(from var(--mico-color-black) l c h / 0.3);       /* Gentle black overlay */
  --mico-color-black-veil: oklch(from var(--mico-color-black) l c h / 0.4);       /* Moderate black overlay */
  --mico-color-black-shadow: oklch(from var(--mico-color-black) l c h / 0.5);     /* Balanced black overlay */
  --mico-color-black-shroud: oklch(from var(--mico-color-black) l c h / 0.6);     /* Strong black overlay */
  --mico-color-black-cloak: oklch(from var(--mico-color-black) l c h / 0.7);      /* Heavy black overlay */
  --mico-color-black-eclipse: oklch(from var(--mico-color-black) l c h / 0.8);    /* Deep black overlay */
  --mico-color-black-void: oklch(from var(--mico-color-black) l c h / 0.9);

  /* Gray Transparency Variations */
  --mico-color-gray-whisper: oklch(from var(--mico-color-gray) l c h / 0.1);      /* Subtle gray overlay */
  --mico-color-gray-breath: oklch(from var(--mico-color-gray) l c h / 0.2);       /* Light gray overlay */
  --mico-color-gray-mist: oklch(from var(--mico-color-gray) l c h / 0.3);         /* Gentle gray overlay */
  --mico-color-gray-veil: oklch(from var(--mico-color-gray) l c h / 0.4);         /* Moderate gray overlay */
  --mico-color-gray-shadow: oklch(from var(--mico-color-gray) l c h / 0.5);       /* Balanced gray overlay */
  --mico-color-gray-shroud: oklch(from var(--mico-color-gray) l c h / 0.6);       /* Strong gray overlay */
  --mico-color-gray-cloak: oklch(from var(--mico-color-gray) l c h / 0.7);        /* Heavy gray overlay */
  --mico-color-gray-eclipse: oklch(from var(--mico-color-gray) l c h / 0.8);      /* Deep gray overlay */
  --mico-color-gray-void: oklch(from var(--mico-color-gray) l c h / 0.9);

  /* White Transparency Variations */
  --mico-color-white-whisper: oklch(from var(--mico-color-white) l c h / 0.1);    /* Subtle white overlay */
  --mico-color-white-breath: oklch(from var(--mico-color-white) l c h / 0.2);     /* Light white overlay */
  --mico-color-white-mist: oklch(from var(--mico-color-white) l c h / 0.3);       /* Gentle white overlay */
  --mico-color-white-veil: oklch(from var(--mico-color-white) l c h / 0.4);       /* Moderate white overlay */
  --mico-color-white-shadow: oklch(from var(--mico-color-white) l c h / 0.5);     /* Balanced white overlay */
  --mico-color-white-shroud: oklch(from var(--mico-color-white) l c h / 0.6);     /* Strong white overlay */
  --mico-color-white-cloak: oklch(from var(--mico-color-white) l c h / 0.7);      /* Heavy white overlay */
  --mico-color-white-eclipse: oklch(from var(--mico-color-white) l c h / 0.8);    /* Deep white overlay */
  --mico-color-white-void: oklch(from var(--mico-color-white) l c h / 0.9);

  /* Brand Color Transparency Variations */
  --mico-color-primary-whisper: oklch(from var(--mico-color-primary) l c h / 0.1);   /* Subtle primary overlay */
  --mico-color-primary-breath: oklch(from var(--mico-color-primary) l c h / 0.2);    /* Light primary overlay */
  --mico-color-primary-mist: oklch(from var(--mico-color-primary) l c h / 0.3);      /* Gentle primary overlay */
  --mico-color-primary-veil: oklch(from var(--mico-color-primary) l c h / 0.4);      /* Moderate primary overlay */
  --mico-color-primary-shadow: oklch(from var(--mico-color-primary) l c h / 0.5);    /* Balanced primary overlay */

  --mico-color-secondary-whisper: oklch(from var(--mico-color-secondary) l c h / 0.1); /* Subtle secondary overlay */
  --mico-color-secondary-breath: oklch(from var(--mico-color-secondary) l c h / 0.2);  /* Light secondary overlay */
  --mico-color-secondary-mist: oklch(from var(--mico-color-secondary) l c h / 0.3);    /* Gentle secondary overlay */
  --mico-color-secondary-veil: oklch(from var(--mico-color-secondary) l c h / 0.4);    /* Moderate secondary overlay */
  --mico-color-secondary-shadow: oklch(from var(--mico-color-secondary) l c h / 0.5);  /* Balanced secondary overlay */

  --mico-color-accent-whisper: oklch(from var(--mico-color-accent) l c h / 0.1);     /* Subtle accent overlay */
  --mico-color-accent-breath: oklch(from var(--mico-color-accent) l c h / 0.2);      /* Light accent overlay */
  --mico-color-accent-mist: oklch(from var(--mico-color-accent) l c h / 0.3);        /* Gentle accent overlay */
  --mico-color-accent-veil: oklch(from var(--mico-color-accent) l c h / 0.4);        /* Moderate accent overlay */
  --mico-color-accent-shadow: oklch(from var(--mico-color-accent) l c h / 0.5);

  /* Red Color System - Error states, alerts, danger */
  --mico-color-red: rgb(212, 12, 26);

  /* Red Tints (lighter variations) - Proper UI progression */
  --mico-color-red-2xlight: oklch(from var(--mico-color-red) calc(l + (1 - l) * 0.4) calc(c * 0.7) h); /* Coral red */
  --mico-color-red-3xlight: oklch(from var(--mico-color-red) calc(l + (1 - l) * 0.6) calc(c * 0.5) h); /* Salmon red */
  --mico-color-red-4xlight: oklch(from var(--mico-color-red) calc(l + (1 - l) * 0.75) calc(c * 0.3) h); /* Rose red */
  --mico-color-red-5xlight: oklch(from var(--mico-color-red) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);

  /* Red Shades (darker variations) - Rich, deep progression */
  --mico-color-red-2xdark: oklch(from var(--mico-color-red) calc(l * 0.85) calc(c * 1.05) h); /* Deep red */
  --mico-color-red-3xdark: oklch(from var(--mico-color-red) calc(l * 0.7) calc(c * 1.1) h); /* Burgundy red */
  --mico-color-red-4xdark: oklch(from var(--mico-color-red) calc(l * 0.5) calc(c * 1.15) h); /* Wine red */
  --mico-color-red-5xdark: oklch(from var(--mico-color-red) calc(l * 0.35) calc(c * 1.2) h);

  /* Yellow Color System - Warning states, highlights */
  --mico-color-yellow: rgb(212, 167, 62);

  /* Yellow Tints (lighter variations) - Warm, inviting progression */
  --mico-color-yellow-2xlight: oklch(from var(--mico-color-yellow) calc(l + (1 - l) * 0.4) calc(c * 0.7) h); /* Cream yellow */
  --mico-color-yellow-3xlight: oklch(from var(--mico-color-yellow) calc(l + (1 - l) * 0.6) calc(c * 0.5) h); /* Butter yellow */
  --mico-color-yellow-4xlight: oklch(from var(--mico-color-yellow) calc(l + (1 - l) * 0.75) calc(c * 0.3) h); /* Vanilla yellow */
  --mico-color-yellow-5xlight: oklch(from var(--mico-color-yellow) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);

  /* Yellow Shades (darker variations) - Rich, earthy progression (NOT brown!) */
  --mico-color-yellow-2xdark: oklch(from var(--mico-color-yellow) calc(l * 0.85) calc(c * 1.05) calc(h - 5)); /* Golden yellow */
  --mico-color-yellow-3xdark: oklch(from var(--mico-color-yellow) calc(l * 0.7) calc(c * 1.1) calc(h - 8)); /* Mustard yellow */
  --mico-color-yellow-4xdark: oklch(from var(--mico-color-yellow) calc(l * 0.55) calc(c * 1.15) calc(h - 10)); /* Ochre yellow */
  --mico-color-yellow-5xdark: oklch(from var(--mico-color-yellow) calc(l * 0.4) calc(c * 1.2) calc(h - 12));

  /* Green Color System - Success states, positive actions */
  --mico-color-green: rgb(61, 151, 53);

  /* Green Tints (lighter variations) - Fresh, natural progression */
  --mico-color-green-2xlight: oklch(from var(--mico-color-green) calc(l + (1 - l) * 0.4) calc(c * 0.7) h); /* Mint green */
  --mico-color-green-3xlight: oklch(from var(--mico-color-green) calc(l + (1 - l) * 0.6) calc(c * 0.5) h); /* Sage green */
  --mico-color-green-4xlight: oklch(from var(--mico-color-green) calc(l + (1 - l) * 0.75) calc(c * 0.3) h); /* Seafoam green */
  --mico-color-green-5xlight: oklch(from var(--mico-color-green) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);

  /* Green Shades (darker variations) - Rich, forest progression */
  --mico-color-green-2xdark: oklch(from var(--mico-color-green) calc(l * 0.85) calc(c * 1.05) h); /* Forest green */
  --mico-color-green-3xdark: oklch(from var(--mico-color-green) calc(l * 0.7) calc(c * 1.1) h); /* Hunter green */
  --mico-color-green-4xdark: oklch(from var(--mico-color-green) calc(l * 0.5) calc(c * 1.15) h); /* Pine green */
  --mico-color-green-5xdark: oklch(from var(--mico-color-green) calc(l * 0.35) calc(c * 1.2) h);

  /* Blue Color System - Information, links, primary actions */
  --mico-color-blue: rgb(0, 127, 218);

  /* Blue Tints (lighter variations) - Clean, professional progression */
  --mico-color-blue-2xlight: oklch(from var(--mico-color-blue) calc(l + (1 - l) * 0.4) calc(c * 0.7) h); /* Sky blue */
  --mico-color-blue-3xlight: oklch(from var(--mico-color-blue) calc(l + (1 - l) * 0.6) calc(c * 0.5) h); /* Powder blue */
  --mico-color-blue-4xlight: oklch(from var(--mico-color-blue) calc(l + (1 - l) * 0.75) calc(c * 0.3) h); /* Ice blue */
  --mico-color-blue-5xlight: oklch(from var(--mico-color-blue) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);

  /* Blue Shades (darker variations) - Deep, trustworthy progression */
  --mico-color-blue-2xdark: oklch(from var(--mico-color-blue) calc(l * 0.85) calc(c * 1.05) h); /* Royal blue */
  --mico-color-blue-3xdark: oklch(from var(--mico-color-blue) calc(l * 0.7) calc(c * 1.1) h); /* Navy blue */
  --mico-color-blue-4xdark: oklch(from var(--mico-color-blue) calc(l * 0.5) calc(c * 1.15) h); /* Midnight blue */
  --mico-color-blue-5xdark: oklch(from var(--mico-color-blue) calc(l * 0.35) calc(c * 1.2) h);

  /* Indigo Color System - Deep blues, professional themes */
  --mico-color-indigo: rgb(80, 90, 200);

  /* Indigo Tints (lighter variations) - Elegant, sophisticated progression */
  --mico-color-indigo-2xlight: oklch(from var(--mico-color-indigo) calc(l + (1 - l) * 0.4) calc(c * 0.7) h); /* Periwinkle */
  --mico-color-indigo-3xlight: oklch(from var(--mico-color-indigo) calc(l + (1 - l) * 0.6) calc(c * 0.5) h); /* Lavender blue */
  --mico-color-indigo-4xlight: oklch(from var(--mico-color-indigo) calc(l + (1 - l) * 0.75) calc(c * 0.3) h); /* Thistle */
  --mico-color-indigo-5xlight: oklch(from var(--mico-color-indigo) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);

  /* Indigo Shades (darker variations) - Rich, professional progression */
  --mico-color-indigo-2xdark: oklch(from var(--mico-color-indigo) calc(l * 0.85) calc(c * 1.05) h); /* Dark slate blue */
  --mico-color-indigo-3xdark: oklch(from var(--mico-color-indigo) calc(l * 0.7) calc(c * 1.1) h); /* Dark indigo */
  --mico-color-indigo-4xdark: oklch(from var(--mico-color-indigo) calc(l * 0.5) calc(c * 1.15) h); /* Midnight indigo */
  --mico-color-indigo-5xdark: oklch(from var(--mico-color-indigo) calc(l * 0.35) calc(c * 1.2) h);

  /* Purple Color System - Creative themes, luxury */
  --mico-color-purple: rgb(150, 74, 198);

  /* Purple Tints (lighter variations) - Luxurious, creative progression */
  --mico-color-purple-2xlight: oklch(from var(--mico-color-purple) calc(l + (1 - l) * 0.4) calc(c * 0.7) h); /* Orchid */
  --mico-color-purple-3xlight: oklch(from var(--mico-color-purple) calc(l + (1 - l) * 0.6) calc(c * 0.5) h); /* Plum */
  --mico-color-purple-4xlight: oklch(from var(--mico-color-purple) calc(l + (1 - l) * 0.75) calc(c * 0.3) h); /* Lavender */
  --mico-color-purple-5xlight: oklch(from var(--mico-color-purple) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);

  /* Purple Shades (darker variations) - Rich, luxurious progression */
  --mico-color-purple-2xdark: oklch(from var(--mico-color-purple) calc(l * 0.85) calc(c * 1.05) h); /* Dark orchid */
  --mico-color-purple-3xdark: oklch(from var(--mico-color-purple) calc(l * 0.7) calc(c * 1.1) h); /* Dark magenta */
  --mico-color-purple-4xdark: oklch(from var(--mico-color-purple) calc(l * 0.5) calc(c * 1.15) h); /* Eggplant */
  --mico-color-purple-5xdark: oklch(from var(--mico-color-purple) calc(l * 0.35) calc(c * 1.2) h);

  /* Pink Color System - Warm themes, highlights */
  --mico-color-pink: rgb(231, 104, 139);

  /* Pink Tints (lighter variations) - Soft, warm progression */
  --mico-color-pink-2xlight: oklch(from var(--mico-color-pink) calc(l + (1 - l) * 0.4) calc(c * 0.7) h); /* Rose pink */
  --mico-color-pink-3xlight: oklch(from var(--mico-color-pink) calc(l + (1 - l) * 0.6) calc(c * 0.5) h); /* Blush pink */
  --mico-color-pink-4xlight: oklch(from var(--mico-color-pink) calc(l + (1 - l) * 0.75) calc(c * 0.3) h); /* Baby pink */
  --mico-color-pink-5xlight: oklch(from var(--mico-color-pink) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);

  /* Pink Shades (darker variations) - Rich, warm progression */
  --mico-color-pink-2xdark: oklch(from var(--mico-color-pink) calc(l * 0.85) calc(c * 1.05) h); /* Deep rose */
  --mico-color-pink-3xdark: oklch(from var(--mico-color-pink) calc(l * 0.7) calc(c * 1.1) h); /* Dusty rose */
  --mico-color-pink-4xdark: oklch(from var(--mico-color-pink) calc(l * 0.5) calc(c * 1.15) h); /* Mauve */
  --mico-color-pink-5xdark: oklch(from var(--mico-color-pink) calc(l * 0.35) calc(c * 1.2) h); /* Deep mauve */  /* Super large devices (wider screens) */


  /* ====================================================================== */
  /* COMMON VALUES SYSTEM                                                   */
  /* ====================================================================== */

  /* ====================================================================== */
  /* TYPOGRAPHY SYSTEM                                                      */
  /* ====================================================================== */

  /**
   * Font Families
   *
   * Standard font stacks for different text purposes.
   * These provide fallbacks for consistent typography across platforms.
   */     /* Massive headings */  /* Black/Heaviest */   /* 200% */  /* Fixed line height */    /* Widest spacing */

  /* ====================================================================== */
  /* SPACING SYSTEM                                                         */
  /* ====================================================================== */            /* Base unit for spacing system */ /* 400px - */


  /* ====================================================================== */
  /* BORDER SYSTEM                                                          */
  /* ====================================================================== */       /* Fully rounded (circles/pills) */       /* Very thick border */     /* Extra large offset */       /* Very thick divide border */

  /* ====================================================================== */
  /* SHADOW SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Media Queries for Adaptive Shadows
   *
   * These media queries automatically adjust shadow styles based on user preferences.
   * - Dark mode: Uses lighter shadows on dark backgrounds
   * - High contrast mode: Uses more visible focus indicators
   */
}

@supports (color: color(display-p3 0 0 0)) {
:root {
  --mico-color-accent: color(display-p3 0.86926 0.51255 0.09455);
  --mico-color-blue: color(display-p3 0.0875 0.49016 0.83544);
}
}

@media (prefers-color-scheme: dark) {

:root {
  /* Dark Mode Shadows */
  --mico-shadow-none-dark: var(--mico-value-none);
  --mico-shadow-xs-dark: 0 1px 2px rgba(255, 255, 255, 0.05);
  --mico-shadow-sm-dark: 0 1px 2px rgba(255, 255, 255, 0.05);
  --mico-shadow-md-dark: 0 4px 6px rgba(255, 255, 255, 0.1);
  --mico-shadow-lg-dark: 0 10px 15px rgba(255, 255, 255, 0.1);
  --mico-shadow-xl-dark: 0 20px 25px rgba(255, 255, 255, 0.15);
  --mico-shadow-2xl-light: ;
  --mico-shadow-3xl-light: ;

  --mico-shadow-inset-none-dark: inset var(--mico-value-none);
  --mico-shadow-inset-xs-dark: inset 0 1px 2px rgba(255, 255, 255, 0.05);
  --mico-shadow-inset-sm-dark: inset 0 1px 2px rgba(255, 255, 255, 0.05);
  --mico-shadow-inset-md-dark: inset 0 4px 6px rgba(255, 255, 255, 0.1);
  --mico-shadow-inset-lg-light: inset 0 20px 25px rgba(0, 0, 0, 0.15); /* Still shadow for light mode, change them to match dark mode */
  --mico-shadow-inset-xl-light: inset 0 20px 25px rgba(0, 0, 0, 0.20); /* Extra large shadow */
  --mico-shadow-inset-2xl-light: inset ;
  --mico-shadow-inset-3xl-light: inset ;
  --mico-shadow-focus: 0 0 0 3px rgba(191, 219, 254, 0.6);
}
  }

/* Seashell pink */

@media (prefers-contrast: high) {

:root {
    --mico-shadow-focus: 0 0 0 4px rgba(0, 0, 0, 1);
    --mico-shadow-sm-contrast: 0 0 0 1px currentColor;
    --mico-shadow-md-contrast: 0 0 0 2px currentColor;
    --mico-shadow-lg-contrast: 0 0 0 3px currentColor;
    --mico-shadow-xl-contrast: 0 0 0 4px currentColor;
}
  }

/* ====================================================================== */

/* LAYOUT SYSTEM                                                          */

/* ====================================================================== */

/**
   * Layout Properties
   *
   * These variables define common layout values for consistent usage.
   * Using variables for these properties ensures consistency across the codebase.
   */

/* Positioned based on scroll position */

/* Element is not displayed */

/* Background extends across fragments */

/* Prevent overscroll effects */

/* Golden ratio (aesthetically pleasing) */

/* Clear both left and right floats */

/* Center the object within its box */

/* Default stacking context behavior */

/* Automatic positioning */

/* ====================================================================== */

/* FLEXBOX SYSTEM                                                          */

/* ====================================================================== */

/**
   * Flexbox Properties
   *
   * These variables define common flexbox values for consistent usage.
   * Flexbox is a one-dimensional layout method for arranging items.
   */

/* Items arranged in a column, reversed */

/* Items wrap to multiple lines, reversed */

/* Items evenly distributed with equal space */

/* Items stretched to fill container */

/* ====================================================================== */

/* GRID SYSTEM                                                            */

/* ====================================================================== */

/* Creates as many tracks as possible */

/* Content evenly distributed with equal space */

/**
   * Grid Properties
   *
   * These variables define common grid layout values for consistent usage.
   * CSS Grid is a two-dimensional layout system for complex layouts.
   */

/* Minimum row height for responsive grids */

/* Maximum height for auto rows */

/* Equal rows */

/* Row span for grid items */

/* Dense packing algorithm */

/* Extra large gap */

/* ====================================================================== */

/* BACKGROUND SYSTEM                                                       */

/* ====================================================================== */

/**
   * Background Properties
   *
   * These variables define common background property values for consistent usage.
   * Background properties control how backgrounds are displayed.
   */

/* Repeat vertically only */

/* Scrolls with element */

/* Extend to content edge */

/* ====================================================================== */

/* FILTER SYSTEM                                                          */

/* ====================================================================== */

/* Applies sepia tone */

/* ====================================================================== */

/* OPACITY SYSTEM                                                          */

/* ====================================================================== */

/* ====================================================================== */

/* TRANSFORM SYSTEM                                                       */

/* ====================================================================== */

/**
   * Transform Properties
   *
   * These variables define common transform functions for consistent usage.
   * Transforms allow elements to be visually manipulated in 2D or 3D space.
   */

/* 50% of original size */

/* 90-degree rotation */

/* Move 100% down */

/* ====================================================================== */

/* TABLE SYSTEM                                                           */

/* ====================================================================== */

/* Fixed table layout algorithm */

/* ====================================================================== */

/* SVG SYSTEM                                                             */

/* ====================================================================== */

/* Use current text color for stroke */

/* ====================================================================== */

/* BUTTON SYSTEM                                                          */

/* ====================================================================== */

/* Extra large button font size */

/* Circular buttons */

/* Large shadow */

/* Size for icon-only buttons */

/* ====================================================================== */

/* CURSOR SYSTEM                                                          */

/* ====================================================================== */

/* Help cursor */

/* ====================================================================== */

/* MISCELLANEOUS UTILITIES SYSTEM                                         */

/* ====================================================================== */

/* Use default browser styling */

/* Element can be target of pointer events */

/* Optimize for scroll changes */

/* Large bleed offset */

/* ====================================================================== */

/* SIZING SYSTEM                                                          */

/* ====================================================================== */

/**
   * Width and Height Properties
   *
   * These variables define common sizing values for consistent usage.
   * Consistent sizing helps maintain a cohesive layout.
   */

/* Largest size needed for content */

/* Third height of container */

/* Full viewport height */

/* Maximum height of container */

/* ====================================================================== */

/* ALIGNMENT SYSTEM                                                       */

/* ====================================================================== */

/**
   * Vertical Alignment Properties
   *
   * These variables define vertical alignment values for consistent usage.
   * Vertical alignment controls how inline or table-cell elements are aligned.
   */

/* Align to bottom of text */

/* Superscript alignment */

/* ====================================================================== */

/* ANIMATION SYSTEM                                                       */

/* ====================================================================== */

/* Very slow animations */

/* Extra large delay */

/* Default fill mode */

/* Typewriter cursor color */

/* Bouncing effect */

/* ====================================================================== */

/* EASING SYSTEM                                                          */

/* ====================================================================== */

/**
   * Easing Functions
   *
   * These variables define timing functions that control animation pacing.
   * Different easing functions create different feelings of motion.
   */

/* Slow start and end */

/* Slight overshoot */

/* Quick, snappy motion */

/* ====================================================================== */

/* TRANSITION SYSTEM                                                      */

/* ====================================================================== */

/**
   * Transition Properties
   *
   * These variables define common transition presets for consistent usage.
   * Transitions create smooth animations between property changes.
   */

/* Slow transitions */

/* Shadow only */

/* ====================================================================== */

/* COLOR SYSTEM - OKLCH POWERED                                          */

/* ====================================================================== */

/**
   * OKLCH Color System
   *
   * This advanced color system uses OKLCH (Oklab Lightness Chroma Hue) color space
   * for superior color manipulation and perceptual uniformity. OKLCH provides:
   *
   * - Better perceptual uniformity than HSL
   * - More predictable lightness adjustments
   * - Wider color gamut support
   * - Superior color mixing and variations
   *
   * Base Colors: Each color serves as the foundation for 5 tints and 5 shades
   * Tints (lighter): 2xlight, 3xlight, 4xlight, 5xlight (progressively lighter)
   * Shades (darker): 2xdark, 3xdark, 4xdark, 5xdark (progressively darker)
   *
   * Browser Support: Modern browsers support oklch() and oklch(from var()) syntax.
   * Fallbacks are provided for older browsers.
   */

/* Base primary color - customizable in styleguide.css */

/* Pale tint */

/* Deep shade */

/* Base secondary color - customizable in styleguide.css */

/* Pale tint */

/* Deep shade */

/* Base accent color - customizable in styleguide.css */

/* Pale tint */

/* Deep shade */

/**
   * Semantic Colors - OKLCH System
   *
   * Colors that convey meaning and state information.
   * Each semantic color includes 5 tints and 5 shades for comprehensive usage.
   * All colors maintain WCAG AA contrast ratios for accessibility.
   */

/* Base success color - WCAG AA compliant */

/* Pale success tint */

/* Deep success shade */

/* Base warning color - WCAG AA compliant */

/* Pale warning tint */

/* Deep warning shade */

/* Base error color - WCAG AA compliant */

/* Pale error tint */

/* Deep error shade */

/* Base info color - WCAG AA compliant */

/* Pale info tint */

/* Deep info shade */

/* Base visited color - distinct from primary */

/* Pale visited tint */

/* Deep visited shade */

/* Base black color */

/* Pale black tint */

/* Deep black shade */

/* Base gray color - true neutral */

/* Pale gray tint */

/* Deep gray shade */

/* Base white color */

/* Pale white tint */

/* Deep white shade */

/**
   * Transparency Colors - OKLCH Alpha System
   *
   * Creative transparency variations using OKLCH with alpha channels.
   * These provide sophisticated overlay and background effects with superior color mixing.
   * Each transparency level has a creative name for intuitive usage.
   */

/* Intense black overlay */

/* Intense gray overlay */

/* Intense white overlay */

/* Balanced accent overlay */

/**
   * Extended Color Palette - OKLCH System
   *
   * Comprehensive color palette for diverse design needs using OKLCH color space.
   * Each color includes 5 tints and 5 shades for superior color consistency.
   * All colors maintain accessibility and contrast compliance with perceptual uniformity.
   */

/* Base red color - vibrant crimson, WCAG AA compliant */

/* Blush red */

/* Maroon red */

/* Base yellow color - warm amber, not harsh */

/* Ivory yellow */

/* Dark gold */

/* Base green color - fresh emerald, nature-inspired */

/* Honeydew green */

/* Deep forest */

/* Base blue color - trustworthy azure, professional */

/* Alice blue */

/* Deep navy */

/* Base indigo color - sophisticated violet-blue */

/* Ghost white */

/* Deep indigo */

/* Base purple color - rich amethyst, creative */

/* Magnolia */

/* Deep purple */

/* Base pink color - warm rose, approachable */

/* Core Utilities */

/* ========================================================================== */

/* MICO CSS FRAMEWORK - TYPOGRAPHY UTILITIES                                 */

/* ========================================================================== */

/* ========================================================================== */

/* FONT FAMILY UTILITIES                                                     */

/* ========================================================================== */

.f-sans { font-family: var(--mico-font-sans) !important; }

.f-serif { font-family: var(--mico-font-serif) !important; }

.f-mono { font-family: var(--mico-font-mono) !important; }

.f-display { font-family: var(--mico-font-display) !important; }

.f-body { font-family: var(--mico-font-body) !important; }

/* ========================================================================== */

/* FONT SIZE UTILITIES                                                       */

/* ========================================================================== */

.fs-xs { font-size: max(0.995rem, min(1.5vw, 0.975rem)) !important; }

.fs-sm { font-size: max(0.980rem, min(2vw, 1.2rem)) !important; }

.fs-md { font-size: max(1.051rem, min(2.5vw, 1.30rem)) !important; }

.fs-lg { font-size: max(1.25rem, min(3vw, 1.675rem)) !important; }

.fs-xl { font-size: max(1.5rem, min(4vw, 2rem)) !important; }

.fs-2xl { font-size: max(1.875rem, min(5vw, 3rem)) !important; }

.fs-3xl { font-size: max(2.25rem, min(5.5vw, 3.5rem)) !important; }

.fs-4xl { font-size: max(2.5rem, min(6vw, 4rem)) !important; }

.fs-5xl { font-size: max(3rem, min(6.5vw, 4.5rem)) !important; }

.fs-6xl { font-size: max(3.5rem, min(7vw, 5rem)) !important; }

.fs-7xl { font-size: max(3.75rem, min(7.5vw, 5.5rem)) !important; }

.fs-8xl { font-size: max(4rem, min(8vw, 6rem)) !important; }

.fs-9xl { font-size: max(4.5rem, min(9vw, 7rem)) !important; }

/* ========================================================================== */

/* FONT WEIGHT UTILITIES                                                     */

/* ========================================================================== */

.fw-100 { font-weight: 100 !important; }

.fw-200 { font-weight: 200 !important; }

.fw-300 { font-weight: 300 !important; }

.fw-400 { font-weight: 400 !important; }

.fw-500 { font-weight: 500 !important; }

.fw-600 { font-weight: 600 !important; }

.fw-700 { font-weight: 700 !important; }

.fw-800 { font-weight: 800 !important; }

.fw-900 { font-weight: 900 !important; }

/* ========================================================================== */

/* LINE HEIGHT UTILITIES                                                     */

/* ========================================================================== */

.lh-xs { line-height: 1 !important; }

.lh-sm { line-height: 1.25 !important; }

.lh-md { line-height: 1.5 !important; }

.lh-lg { line-height: 1.625 !important; }

.lh-xl { line-height: 2 !important; }

.lh-2xl { line-height: 0.75rem !important; }

.lh-3xl { line-height: 1rem !important; }

.lh-4xl { line-height: 1.25rem !important; }

.lh-5xl { line-height: 1.5rem !important; }

.lh-6xl { line-height: 1.75rem !important; }

.lh-7xl { line-height: 2rem !important; }

.lh-8xl { line-height: 2.25rem !important; }

.lh-9xl { line-height: 2.5rem !important; }

/* ========================================================================== */

/* LETTER SPACING UTILITIES                                                  */

/* ========================================================================== */

.ls-xs { letter-spacing: -0.05em !important; }

.ls-sm { letter-spacing: -0.025em !important; }

.ls-md { letter-spacing: 0em !important; }

.ls-lg { letter-spacing: 0.025em !important; }

.ls-xl { letter-spacing: 0.05em !important; }

.ls-2xl { letter-spacing: 0.1em !important; }

/* ========================================================================== */

/* FONT STYLE UTILITIES                                                      */

/* ========================================================================== */

.f-normal { font-style: normal !important; }

.f-italic { font-style: italic !important; }

/* ========================================================================== */

/* FONT STRETCH UTILITIES                                                    */

/* ========================================================================== */

.f-stretch-ultra-condensed { font-stretch: ultra-condensed !important; }

.f-stretch-extra-condensed { font-stretch: extra-condensed !important; }

.f-stretch-condensed { font-stretch: condensed !important; }

.f-stretch-semi-condensed { font-stretch: semi-condensed !important; }

.f-stretch-normal { font-stretch: normal !important; }

.f-stretch-semi-expanded { font-stretch: semi-expanded !important; }

.f-stretch-expanded { font-stretch: expanded !important; }

.f-stretch-extra-expanded { font-stretch: extra-expanded !important; }

.f-stretch-ultra-expanded { font-stretch: ultra-expanded !important; }

/* ========================================================================== */

/* TEXT ALIGNMENT UTILITIES                                                  */

/* ========================================================================== */

.text-left { text-align: left !important; }

.text-center { text-align: center !important; }

.text-right { text-align: right !important; }

.text-justify { text-align: justify !important; }

.text-start { text-align: left !important; }

.text-end { text-align: right !important; }

/* ========================================================================== */

/* TEXT TRANSFORM UTILITIES                                                  */

/* ========================================================================== */

.text-uppercase { text-transform: uppercase !important; }

.text-lowercase { text-transform: lowercase !important; }

.text-capitalize { text-transform: capitalize !important; }

.text-normal-case { text-transform: none !important; }

/* ========================================================================== */

/* TEXT DECORATION UTILITIES                                                 */

/* ========================================================================== */

.underline { text-decoration-line: underline !important; }

.overline { text-decoration-line: overline !important; }

.line-through { text-decoration-line: line-through !important; }

.no-underline { text-decoration-line: none !important; }

/* Text Decoration Style */

.decoration-solid { text-decoration-style: solid !important; }

.decoration-double { text-decoration-style: double !important; }

.decoration-dotted { text-decoration-style: dotted !important; }

.decoration-dashed { text-decoration-style: dashed !important; }

.decoration-wavy { text-decoration-style: wavy !important; }

/* Text Decoration Thickness */

.decoration-auto { text-decoration-thickness: auto !important; }

.decoration-from-font { text-decoration-thickness: from-font !important; }

.decoration-0 { text-decoration-thickness: 0px !important; }

.decoration-1 { text-decoration-thickness: 1px !important; }

.decoration-2 { text-decoration-thickness: 2px !important; }

.decoration-4 { text-decoration-thickness: 4px !important; }

.decoration-8 { text-decoration-thickness: 8px !important; }

/* Text Underline Offset */

.underline-offset-auto { text-underline-offset: auto !important; }

.underline-offset-0 { text-underline-offset: 0 !important; }

.underline-offset-1 { text-underline-offset: 1px !important; }

.underline-offset-2 { text-underline-offset: 2px !important; }

.underline-offset-4 { text-underline-offset: 4px !important; }

.underline-offset-8 { text-underline-offset: 8px !important; }

/* ========================================================================== */

/* TEXT OVERFLOW UTILITIES                                                   */

/* ========================================================================== */

.text-ellipsis { text-overflow: ellipsis !important; }

.text-clip { text-overflow: clip !important; }

.truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* ========================================================================== */

/* WHITESPACE UTILITIES                                                      */

/* ========================================================================== */

.whitespace-normal { white-space: normal !important; }

.whitespace-nowrap { white-space: nowrap !important; }

.whitespace-pre { white-space: pre !important; }

.whitespace-pre-line { white-space: pre-line !important; }

.whitespace-pre-wrap { white-space: pre-wrap !important; }

.whitespace-break-spaces { white-space: break-spaces !important; }

/* ========================================================================== */

/* WORD BREAK UTILITIES                                                      */

/* ========================================================================== */

.break-normal {
  word-wrap: normal !important;
  word-break: normal !important;
}

.break-words { word-wrap: break-word !important; }

.break-all { word-break: break-all !important; }

.break-keep { word-break: keep-all !important; }

/* ========================================================================== */

/* TEXT DIRECTION UTILITIES                                                  */

/* ========================================================================== */

.text-ltr { direction: ltr !important; }

.text-rtl { direction: rtl !important; }

/* ========================================================================== */

/* VERTICAL ALIGNMENT UTILITIES                                              */

/* ========================================================================== */

.align-baseline { vertical-align: baseline !important; }

.align-top { vertical-align: top !important; }

.align-middle { vertical-align: middle !important; }

.align-bottom { vertical-align: bottom !important; }

.align-text-top { vertical-align: text-top !important; }

.align-text-bottom { vertical-align: text-bottom !important; }

.align-sub { vertical-align: sub !important; }

.align-super { vertical-align: super !important; }

/* ========================================================================== */

/* TEXT INDENT UTILITIES                                                     */

/* ========================================================================== */

.indent-0 { text-indent: 0 !important; }

.indent-xs { text-indent: 1px !important; }

.indent-sm { text-indent: 0.25rem !important; }

.indent-md { text-indent: 0.5rem !important; }

.indent-lg { text-indent: 1rem !important; }

.indent-xl { text-indent: 2rem !important; }

/* ========================================================================== */

/* TEXT SHADOW UTILITIES                                                     */

/* ========================================================================== */

.text-shadow-none { text-shadow: none !important; }

.text-shadow-xs { text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1) !important; }

.text-shadow-sm { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1) !important; }

.text-shadow-md { text-shadow: 4px 4px 6px rgba(0, 0, 0, 0.1) !important; }

.text-shadow-lg { text-shadow: 6px 6px 8px rgba(0, 0, 0, 0.15) !important; }

/* ========================================================================== */

/* TEXT STROKE UTILITIES                                                     */

/* ========================================================================== */

.text-stroke-xs { -webkit-text-stroke-width: 1px !important; }

.text-stroke-sm { -webkit-text-stroke-width: 2px !important; }

.text-stroke-md { -webkit-text-stroke-width: 4px !important; }

/* ========================================================================== */

/* FONT VARIANT UTILITIES                                                    */

/* ========================================================================== */

/* Font Variant Numeric */

.font-variant-normal { font-feature-settings: normal !important; font-variant-numeric: normal !important; }

.font-variant-ordinal { font-feature-settings: ordinal !important; font-variant-numeric: ordinal !important; }

.font-variant-slashed-zero { font-feature-settings: slashed-zero !important; font-variant-numeric: slashed-zero !important; }

.font-variant-lining-nums { font-feature-settings: lining-nums !important; font-variant-numeric: lining-nums !important; }

.font-variant-oldstyle-nums { font-feature-settings: oldstyle-nums !important; font-variant-numeric: oldstyle-nums !important; }

.font-variant-proportional-nums { font-feature-settings: proportional-nums !important; font-variant-numeric: proportional-nums !important; }

.font-variant-tabular-nums { font-feature-settings: tabular-nums !important; font-variant-numeric: tabular-nums !important; }

.font-variant-diagonal-fractions { font-feature-settings: diagonal-fractions !important; font-variant-numeric: diagonal-fractions !important; }

.font-variant-stacked-fractions { font-feature-settings: stacked-fractions !important; font-variant-numeric: stacked-fractions !important; }

/* Font Variant Ligatures */

.ligatures-common { font-feature-settings: common-ligatures !important; font-variant-ligatures: common-ligatures !important; }

.ligatures-no-common { font-feature-settings: no-common-ligatures !important; font-variant-ligatures: no-common-ligatures !important; }

.ligatures-discretionary { font-feature-settings: discretionary-ligatures !important; font-variant-ligatures: discretionary-ligatures !important; }

.ligatures-no-discretionary { font-feature-settings: no-discretionary-ligatures !important; font-variant-ligatures: no-discretionary-ligatures !important; }

.ligatures-historical { font-feature-settings: historical-ligatures !important; font-variant-ligatures: historical-ligatures !important; }

.ligatures-no-historical { font-feature-settings: no-historical-ligatures !important; font-variant-ligatures: no-historical-ligatures !important; }

.ligatures-contextual { font-feature-settings: contextual !important; font-variant-ligatures: contextual !important; }

.ligatures-no-contextual { font-feature-settings: no-contextual !important; font-variant-ligatures: no-contextual !important; }

/* Font Variant Caps */

.caps-normal { font-feature-settings: normal !important; font-variant-caps: normal !important; }

.caps-small { font-feature-settings: small-caps !important; font-variant-caps: small-caps !important; }

.caps-all-small { font-feature-settings: all-small-caps !important; font-variant-caps: all-small-caps !important; }

.caps-petite { font-feature-settings: petite-caps !important; font-variant-caps: petite-caps !important; }

.caps-all-petite { font-feature-settings: all-petite-caps !important; font-variant-caps: all-petite-caps !important; }

.caps-unicase { font-feature-settings: unicase !important; font-variant-caps: unicase !important; }

.caps-titling { font-feature-settings: titling-caps !important; font-variant-caps: titling-caps !important; }

/* ========================================================================== */

/* LIST STYLE UTILITIES                                                      */

/* ========================================================================== */

.list-none { list-style-type: none !important; }

.list-disc { list-style-type: disc !important; }

.list-decimal { list-style-type: decimal !important; }

.list-square { list-style-type: square !important; }

.list-upper-roman { list-style-type: upper-roman !important; }

.list-lower-roman { list-style-type: lower-roman !important; }

.list-upper-alpha { list-style-type: upper-alpha !important; }

.list-lower-alpha { list-style-type: lower-alpha !important; }

.list-inside { list-style-position: inside !important; }

.list-outside { list-style-position: outside !important; }

/* ========================================================================== */

/* USER SELECT UTILITIES                                                     */

/* ========================================================================== */

.select-none { -webkit-user-select: none !important; -moz-user-select: none !important; user-select: none !important; }

.select-text { -webkit-user-select: text !important; -moz-user-select: text !important; user-select: text !important; }

.select-all { -webkit-user-select: all !important; -moz-user-select: all !important; user-select: all !important; }

.select-auto { -webkit-user-select: auto !important; -moz-user-select: auto !important; user-select: auto !important; }

/* ========================================================================== */

/* WRITING MODE UTILITIES                                                    */

/* ========================================================================== */

.writing-horizontal { writing-mode: horizontal-tb !important; }

.writing-vertical-rl { writing-mode: vertical-rl !important; }

.writing-vertical-lr { writing-mode: vertical-lr !important; }

/* ========================================================================== */

/* TEXT ORIENTATION UTILITIES                                                */

/* ========================================================================== */

.orientation-mixed { text-orientation: mixed !important; }

.orientation-upright { text-orientation: upright !important; }

.orientation-sideways { text-orientation: sideways !important; }

/* ========================================================================== */

/* HYPHENS UTILITIES                                                         */

/* ========================================================================== */

.hyphens-none { hyphens: none !important; }

.hyphens-manual { hyphens: manual !important; }

.hyphens-auto { hyphens: auto !important; }

/* ========================================================================== */

/* ADVANCED TYPOGRAPHY FEATURES                                              */

/* ========================================================================== */

.drop-cap::first-letter {
  float: left;
  font-size: 3em;
  line-height: 0.8;
  padding-right: 0.1em;
  padding-top: 0.1em;
}

.text-wrap-balance { text-wrap: balance; }

.text-wrap-pretty { text-wrap: pretty; }

.text-wrap-stable { text-wrap: stable; }

.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* ========================================================================== */

/* FONT SMOOTHING UTILITIES                                                  */

/* ========================================================================== */

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.subpixel-antialiased {
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
}

/**
 * Color Utility Classes
 *
 * Comprehensive color utilities for the Mico CSS Framework.
 * All color variables are defined in css/base/variables.css
 *
 * This file contains only utility classes that reference those variables.
 * Classes are organized by: Brand Colors, Neutral Colors, Extended Palette,
 * Transparency Colors, and Semantic Colors.
 */

/* ====================================================================== */

/* BRAND COLOR UTILITIES                                                  */

/* ====================================================================== */

/**
 * Base Brand Colors
 * Primary, secondary, and accent colors for brand consistency
 */

/* Base Brand Background Colors */

.bg-primary { background-color: rgb(39, 132, 213) !important; }

.bg-secondary { background-color: rgb(81, 97, 145) !important; }

.bg-accent { background-color: rgb(236, 125, 0) !important; background-color: color(display-p3 0.86926 0.51255 0.09455) !important; }

/* Base Brand Text Colors */

.text-primary { color: rgb(39, 132, 213) !important; }

.text-secondary { color: rgb(81, 97, 145) !important; }

.text-accent { color: rgb(236, 125, 0) !important; color: color(display-p3 0.86926 0.51255 0.09455) !important; }

/**
 * Primary Color Variations
 * Shades (darker) and tones (lighter) of the primary brand color
 */

/* Primary Tint Text Colors (lighter) */

.text-primary-2xlight { color: rgb(103, 170, 237) !important; }

.text-primary-3xlight { color: rgb(145, 195, 246) !important; }

.text-primary-4xlight { color: rgb(186, 219, 254) !important; }

.text-primary-5xlight { color: rgb(221, 237, 255) !important; color: color(display-p3 0.87736 0.92848 0.9935) !important; }

/* Primary Shade Text Colors (darker) */

.text-primary-2xdark { color: rgb(0, 94, 181) !important; color: color(display-p3 0.06248 0.36112 0.68706) !important; }

.text-primary-3xdark { color: rgb(0, 60, 129) !important; color: color(display-p3 0 0.21727 0.54103) !important; }

.text-primary-4xdark { color: rgb(0, 29, 78) !important; color: color(display-p3 0 0.10188 0.3246) !important; }

.text-primary-5xdark { color: rgb(0, 8, 44) !important; color: color(display-p3 0 0.02366 0.17799) !important; }

/* Primary Tint Background Colors (lighter) */

.bg-primary-2xlight { background-color: rgb(103, 170, 237) !important; }

.bg-primary-3xlight { background-color: rgb(145, 195, 246) !important; }

.bg-primary-4xlight { background-color: rgb(186, 219, 254) !important; }

.bg-primary-5xlight { background-color: rgb(221, 237, 255) !important; background-color: color(display-p3 0.87736 0.92848 0.9935) !important; }

/* Primary Shade Background Colors (darker) */

.bg-primary-2xdark { background-color: rgb(0, 94, 181) !important; background-color: color(display-p3 0.06248 0.36112 0.68706) !important; }

.bg-primary-3xdark { background-color: rgb(0, 60, 129) !important; background-color: color(display-p3 0 0.21727 0.54103) !important; }

.bg-primary-4xdark { background-color: rgb(0, 29, 78) !important; background-color: color(display-p3 0 0.10188 0.3246) !important; }

.bg-primary-5xdark { background-color: rgb(0, 8, 44) !important; background-color: color(display-p3 0 0.02366 0.17799) !important; }

/**
 * Secondary Color Variations
 * Shades (darker) and tones (lighter) of the secondary brand color
 */

/* Secondary Tint Text Colors (lighter) */

.text-secondary-2xlight { color: rgb(128, 142, 183) !important; }

.text-secondary-3xlight { color: rgb(162, 173, 205) !important; }

.text-secondary-4xlight { color: rgb(197, 205, 227) !important; }

.text-secondary-5xlight { color: rgb(226, 230, 241) !important; }

/* Secondary Shade Text Colors (darker) */

.text-secondary-2xdark { color: rgb(53, 68, 119) !important; }

.text-secondary-3xdark { color: rgb(28, 40, 93) !important; }

.text-secondary-4xdark { color: rgb(7, 10, 68) !important; }

.text-secondary-5xdark { color: rgb(3, 0, 47) !important; color: color(display-p3 0.00631 0 0.17626) !important; }

/* Secondary Tint Background Colors (lighter) */

.bg-secondary-2xlight { background-color: rgb(128, 142, 183) !important; }

.bg-secondary-3xlight { background-color: rgb(162, 173, 205) !important; }

.bg-secondary-4xlight { background-color: rgb(197, 205, 227) !important; }

.bg-secondary-5xlight { background-color: rgb(226, 230, 241) !important; }

/* Secondary Shade Background Colors (darker) */

.bg-secondary-2xdark { background-color: rgb(53, 68, 119) !important; }

.bg-secondary-3xdark { background-color: rgb(28, 40, 93) !important; }

.bg-secondary-4xdark { background-color: rgb(7, 10, 68) !important; }

.bg-secondary-5xdark { background-color: rgb(3, 0, 47) !important; background-color: color(display-p3 0.00631 0 0.17626) !important; }

/**
 * Accent Color Variations
 * Shades (darker) and tones (lighter) of the accent brand color
 */

/* Accent Tint Text Colors (lighter) */

.text-accent-2xlight { color: rgb(253, 162, 81) !important; }

.text-accent-3xlight { color: rgb(255, 189, 132) !important; color: color(display-p3 0.97138 0.75229 0.55196) !important; }

.text-accent-4xlight { color: rgb(255, 215, 178) !important; color: color(display-p3 0.99752 0.84938 0.71576) !important; }

.text-accent-5xlight { color: rgb(255, 235, 217) !important; color: color(display-p3 1 0.92547 0.85819) !important; }

/* Accent Shade Text Colors (darker) */

.text-accent-2xdark { color: rgb(178, 89, 0) !important; color: color(display-p3 0.67601 0.3501 0) !important; }

.text-accent-3xdark { color: rgb(122, 56, 0) !important; color: color(display-p3 0.46076 0.22297 0) !important; }

.text-accent-4xdark { color: rgb(70, 27, 0) !important; color: color(display-p3 0.26237 0.10629 0) !important; }

.text-accent-5xdark { color: rgb(35, 7, 0) !important; color: color(display-p3 0.12787 0.02868 0) !important; }

/* Accent Tint Background Colors (lighter) */

.bg-accent-2xlight { background-color: rgb(253, 162, 81) !important; }

.bg-accent-3xlight { background-color: rgb(255, 189, 132) !important; background-color: color(display-p3 0.97138 0.75229 0.55196) !important; }

.bg-accent-4xlight { background-color: rgb(255, 215, 178) !important; background-color: color(display-p3 0.99752 0.84938 0.71576) !important; }

.bg-accent-5xlight { background-color: rgb(255, 235, 217) !important; background-color: color(display-p3 1 0.92547 0.85819) !important; }

/* Accent Shade Background Colors (darker) */

.bg-accent-2xdark { background-color: rgb(178, 89, 0) !important; background-color: color(display-p3 0.67601 0.3501 0) !important; }

.bg-accent-3xdark { background-color: rgb(122, 56, 0) !important; background-color: color(display-p3 0.46076 0.22297 0) !important; }

.bg-accent-4xdark { background-color: rgb(70, 27, 0) !important; background-color: color(display-p3 0.26237 0.10629 0) !important; }

.bg-accent-5xdark { background-color: rgb(35, 7, 0) !important; background-color: color(display-p3 0.12787 0.02868 0) !important; }

/* ====================================================================== */

/* NEUTRAL COLOR UTILITIES                                                */

/* ====================================================================== */

/**
 * Black Color System - OKLCH
 * Base black with 5 tints and 5 shades for dark themes and high contrast
 */

/* Black Base Color */

.bg-black { background-color: rgb(3, 3, 4) !important; }

.text-black { color: rgb(3, 3, 4) !important; }

/* Black Tint Background Colors (lighter) */

.bg-black-2xlight { background-color: rgb(63, 64, 66) !important; }

.bg-black-3xlight { background-color: rgb(113, 113, 115) !important; }

.bg-black-4xlight { background-color: rgb(167, 168, 169) !important; }

.bg-black-5xlight { background-color: rgb(210, 210, 211) !important; }

/* Black Tint Text Colors (lighter) */

.text-black-2xlight { color: rgb(63, 64, 66) !important; }

.text-black-3xlight { color: rgb(113, 113, 115) !important; }

.text-black-4xlight { color: rgb(167, 168, 169) !important; }

.text-black-5xlight { color: rgb(210, 210, 211) !important; }

/* Black Shade Background Colors (darker) */

.bg-black-2xdark { background-color: rgb(1, 2, 3) !important; }

.bg-black-3xdark { background-color: rgb(1, 1, 1) !important; }

.bg-black-4xdark { background-color: rgb(0, 0, 0) !important; }

.bg-black-5xdark { background-color: rgb(0, 0, 0) !important; }

/* Black Shade Text Colors (darker) */

.text-black-2xdark { color: rgb(1, 2, 3) !important; }

.text-black-3xdark { color: rgb(1, 1, 1) !important; }

.text-black-4xdark { color: rgb(0, 0, 0) !important; }

.text-black-5xdark { color: rgb(0, 0, 0) !important; }

/**
 * Gray Color System - OKLCH
 * Base gray with 5 tints and 5 shades for UI elements, text, and backgrounds
 * Optimized for contrast and accessibility with perceptual uniformity
 */

/* Gray Base Color */

.bg-gray { background-color: rgb(97, 99, 105) !important; }

.text-gray { color: rgb(97, 99, 105) !important; }

/* Gray Tint Background Colors (lighter) */

.bg-gray-2xlight { background-color: rgb(141, 143, 148) !important; }

.bg-gray-3xlight { background-color: rgb(172, 174, 178) !important; }

.bg-gray-4xlight { background-color: rgb(205, 206, 208) !important; }

.bg-gray-5xlight { background-color: rgb(229, 230, 231) !important; }

/* Gray Tint Text Colors (lighter) */

.text-gray-2xlight { color: rgb(141, 143, 148) !important; }

.text-gray-3xlight { color: rgb(172, 174, 178) !important; }

.text-gray-4xlight { color: rgb(205, 206, 208) !important; }

.text-gray-5xlight { color: rgb(229, 230, 231) !important; }

/* Gray Shade Background Colors (darker) */

.bg-gray-2xdark { background-color: rgb(69, 72, 78) !important; }

.bg-gray-3xdark { background-color: rgb(43, 46, 52) !important; }

.bg-gray-4xdark { background-color: rgb(20, 22, 28) !important; }

.bg-gray-5xdark { background-color: rgb(5, 6, 12) !important; }

/* Gray Shade Text Colors (darker) */

.text-gray-2xdark { color: rgb(69, 72, 78) !important; }

.text-gray-3xdark { color: rgb(43, 46, 52) !important; }

.text-gray-4xdark { color: rgb(20, 22, 28) !important; }

.text-gray-5xdark { color: rgb(5, 6, 12) !important; }

/**
 * White Color System - OKLCH
 * Base white with 5 tints and 5 shades for clean, minimal aesthetics
 */

/* White Base Color */

.bg-white { background-color: rgb(237, 238, 242) !important; }

.text-white { color: rgb(237, 238, 242) !important; }

/* White Tint Background Colors (lighter) */

.bg-white-2xlight { background-color: rgb(242, 243, 246) !important; }

.bg-white-3xlight { background-color: rgb(246, 247, 249) !important; }

.bg-white-4xlight { background-color: rgb(249, 250, 251) !important; }

.bg-white-5xlight { background-color: rgb(252, 252, 253) !important; }

/* White Tint Text Colors (lighter) */

.text-white-2xlight { color: rgb(242, 243, 246) !important; }

.text-white-3xlight { color: rgb(246, 247, 249) !important; }

.text-white-4xlight { color: rgb(249, 250, 251) !important; }

.text-white-5xlight { color: rgb(252, 252, 253) !important; }

/* White Shade Background Colors (darker) */

.bg-white-2xdark { background-color: rgb(176, 177, 181) !important; }

.bg-white-3xdark { background-color: rgb(118, 119, 123) !important; }

.bg-white-4xdark { background-color: rgb(65, 66, 70) !important; }

.bg-white-5xdark { background-color: rgb(29, 31, 34) !important; }

/* White Shade Text Colors (darker) */

.text-white-2xdark { color: rgb(176, 177, 181) !important; }

.text-white-3xdark { color: rgb(118, 119, 123) !important; }

.text-white-4xdark { color: rgb(65, 66, 70) !important; }

.text-white-5xdark { color: rgb(29, 31, 34) !important; }

/* ====================================================================== */

/* EXTENDED COLOR PALETTE UTILITIES                                       */

/* ====================================================================== */

/**
 * Red Color System - OKLCH
 * Base red with 5 tints and 5 shades for error states, alerts, danger
 */

/* Red Base Color */

.bg-red { background-color: rgb(212, 12, 26) !important; }

.text-red { color: rgb(212, 12, 26) !important; }

/* Red Tint Background Colors (lighter) */

.bg-red-2xlight { background-color: rgb(249, 125, 114) !important; }

.bg-red-3xlight { background-color: rgb(255, 168, 157) !important; background-color: color(display-p3 0.96717 0.67567 0.63039) !important; }

.bg-red-4xlight { background-color: rgb(255, 202, 195) !important; background-color: color(display-p3 0.98072 0.80107 0.77062) !important; }

.bg-red-5xlight { background-color: rgb(255, 229, 225) !important; background-color: color(display-p3 0.99586 0.90366 0.88735) !important; }

/* Red Tint Text Colors (lighter) */

.text-red-2xlight { color: rgb(249, 125, 114) !important; }

.text-red-3xlight { color: rgb(255, 168, 157) !important; color: color(display-p3 0.96717 0.67567 0.63039) !important; }

.text-red-4xlight { color: rgb(255, 202, 195) !important; color: color(display-p3 0.98072 0.80107 0.77062) !important; }

.text-red-5xlight { color: rgb(255, 229, 225) !important; color: color(display-p3 0.99586 0.90366 0.88735) !important; }

/* Red Shade Background Colors (darker) */

.bg-red-2xdark { background-color: rgb(178, 0, 0) !important; background-color: color(display-p3 0.66322 0 0) !important; }

.bg-red-3xdark { background-color: rgb(138, 0, 0) !important; background-color: color(display-p3 0.51667 0 0) !important; }

.bg-red-4xdark { background-color: rgb(88, 0, 0) !important; background-color: color(display-p3 0.32655 0 0) !important; }

.bg-red-5xdark { background-color: rgb(53, 0, 0) !important; background-color: color(display-p3 0.19521 0 0) !important; }

/* Red Shade Text Colors (darker) */

.text-red-2xdark { color: rgb(178, 0, 0) !important; color: color(display-p3 0.66322 0 0) !important; }

.text-red-3xdark { color: rgb(138, 0, 0) !important; color: color(display-p3 0.51667 0 0) !important; }

.text-red-4xdark { color: rgb(88, 0, 0) !important; color: color(display-p3 0.32655 0 0) !important; }

.text-red-5xdark { color: rgb(53, 0, 0) !important; color: color(display-p3 0.19521 0 0) !important; }

/**
 * Yellow Color System - OKLCH
 * Base yellow with 5 tints and 5 shades for warning states, highlights
 */

/* Yellow Base Color */

.bg-yellow { background-color: rgb(212, 167, 62) !important; }

.text-yellow { color: rgb(212, 167, 62) !important; }

/* Yellow Tint Background Colors (lighter) */

.bg-yellow-2xlight { background-color: rgb(233, 202, 136) !important; }

.bg-yellow-3xlight { background-color: rgb(242, 219, 173) !important; }

.bg-yellow-4xlight { background-color: rgb(246, 233, 206) !important; }

.bg-yellow-5xlight { background-color: rgb(251, 244, 231) !important; }

/* Yellow Tint Text Colors (lighter) */

.text-yellow-2xlight { color: rgb(233, 202, 136) !important; }

.text-yellow-3xlight { color: rgb(242, 219, 173) !important; }

.text-yellow-4xlight { color: rgb(246, 233, 206) !important; }

.text-yellow-5xlight { color: rgb(251, 244, 231) !important; }

/* Yellow Shade Background Colors (darker) */

.bg-yellow-2xdark { background-color: rgb(182, 128, 0) !important; background-color: color(display-p3 0.68269 0.51122 0.14346) !important; }

.bg-yellow-3xdark { background-color: rgb(147, 94, 0) !important; background-color: color(display-p3 0.55977 0.36969 0) !important; }

.bg-yellow-4xdark { background-color: rgb(108, 64, 0) !important; background-color: color(display-p3 0.40982 0.25238 0) !important; }

.bg-yellow-5xdark { background-color: rgb(70, 37, 0) !important; background-color: color(display-p3 0.265 0.14578 0) !important; }

/* Yellow Shade Text Colors (darker) */

.text-yellow-2xdark { color: rgb(182, 128, 0) !important; color: color(display-p3 0.68269 0.51122 0.14346) !important; }

.text-yellow-3xdark { color: rgb(147, 94, 0) !important; color: color(display-p3 0.55977 0.36969 0) !important; }

.text-yellow-4xdark { color: rgb(108, 64, 0) !important; color: color(display-p3 0.40982 0.25238 0) !important; }

.text-yellow-5xdark { color: rgb(70, 37, 0) !important; color: color(display-p3 0.265 0.14578 0) !important; }

/**
 * Green Color System - OKLCH
 * Base green with 5 tints and 5 shades for success states, positive actions
 */

/* Green Base Color */

.bg-green { background-color: rgb(61, 151, 53) !important; }

.text-green { color: rgb(61, 151, 53) !important; }

/* Green Tint Background Colors (lighter) */

.bg-green-2xlight { background-color: rgb(135, 195, 128) !important; }

.bg-green-3xlight { background-color: rgb(173, 216, 168) !important; }

.bg-green-4xlight { background-color: rgb(205, 231, 201) !important; }

.bg-green-5xlight { background-color: rgb(230, 244, 229) !important; }

/* Green Tint Text Colors (lighter) */

.text-green-2xlight { color: rgb(135, 195, 128) !important; }

.text-green-3xlight { color: rgb(173, 216, 168) !important; }

.text-green-4xlight { color: rgb(205, 231, 201) !important; }

.text-green-5xlight { color: rgb(230, 244, 229) !important; }

/* Green Shade Background Colors (darker) */

.bg-green-2xdark { background-color: rgb(18, 124, 7) !important; }

.bg-green-3xdark { background-color: rgb(0, 97, 0) !important; background-color: color(display-p3 0.09921 0.37628 0) !important; }

.bg-green-4xdark { background-color: rgb(0, 59, 0) !important; background-color: color(display-p3 0 0.23465 0) !important; }

.bg-green-5xdark { background-color: rgb(0, 33, 0) !important; background-color: color(display-p3 0 0.13211 0) !important; }

/* Green Shade Text Colors (darker) */

.text-green-2xdark { color: rgb(18, 124, 7) !important; }

.text-green-3xdark { color: rgb(0, 97, 0) !important; color: color(display-p3 0.09921 0.37628 0) !important; }

.text-green-4xdark { color: rgb(0, 59, 0) !important; color: color(display-p3 0 0.23465 0) !important; }

.text-green-5xdark { color: rgb(0, 33, 0) !important; color: color(display-p3 0 0.13211 0) !important; }

/**
 * Blue Color System - OKLCH
 * Base blue with 5 tints and 5 shades for information, links, primary actions
 */

/* Blue Base Color */

.bg-blue { background-color: rgb(0, 127, 218) !important; background-color: color(display-p3 0.0875 0.49016 0.83544) !important; }

.text-blue { color: rgb(0, 127, 218) !important; color: color(display-p3 0.0875 0.49016 0.83544) !important; }

/* Blue Tint Background Colors (lighter) */

.bg-blue-2xlight { background-color: rgb(98, 181, 248) !important; }

.bg-blue-3xlight { background-color: rgb(150, 207, 255) !important; background-color: color(display-p3 0.63536 0.80429 0.98287) !important; }

.bg-blue-4xlight { background-color: rgb(191, 225, 254) !important; }

.bg-blue-5xlight { background-color: rgb(224, 241, 255) !important; background-color: color(display-p3 0.89067 0.94224 0.99703) !important; }

/* Blue Tint Text Colors (lighter) */

.text-blue-2xlight { color: rgb(98, 181, 248) !important; }

.text-blue-3xlight { color: rgb(150, 207, 255) !important; color: color(display-p3 0.63536 0.80429 0.98287) !important; }

.text-blue-4xlight { color: rgb(191, 225, 254) !important; }

.text-blue-5xlight { color: rgb(224, 241, 255) !important; color: color(display-p3 0.89067 0.94224 0.99703) !important; }

/* Blue Shade Background Colors (darker) */

.bg-blue-2xdark { background-color: rgb(0, 101, 178) !important; background-color: color(display-p3 0 0.38145 0.73899) !important; }

.bg-blue-3xdark { background-color: rgb(0, 76, 139) !important; background-color: color(display-p3 0 0.28498 0.58203) !important; }

.bg-blue-4xdark { background-color: rgb(0, 45, 91) !important; background-color: color(display-p3 0 0.1653 0.37824) !important; }

.bg-blue-5xdark { background-color: rgb(0, 23, 57) !important; background-color: color(display-p3 0 0.08252 0.23701) !important; }

/* Blue Shade Text Colors (darker) */

.text-blue-2xdark { color: rgb(0, 101, 178) !important; color: color(display-p3 0 0.38145 0.73899) !important; }

.text-blue-3xdark { color: rgb(0, 76, 139) !important; color: color(display-p3 0 0.28498 0.58203) !important; }

.text-blue-4xdark { color: rgb(0, 45, 91) !important; color: color(display-p3 0 0.1653 0.37824) !important; }

.text-blue-5xdark { color: rgb(0, 23, 57) !important; color: color(display-p3 0 0.08252 0.23701) !important; }

/**
 * Indigo Color System - OKLCH
 * Base indigo with 5 tints and 5 shades for deep blues, professional themes
 */

/* Indigo Base Color */

.bg-indigo { background-color: rgb(80, 90, 200) !important; }

.text-indigo { color: rgb(80, 90, 200) !important; }

/* Indigo Tint Background Colors (lighter) */

.bg-indigo-2xlight { background-color: rgb(141, 155, 236) !important; }

.bg-indigo-3xlight { background-color: rgb(176, 188, 248) !important; }

.bg-indigo-4xlight { background-color: rgb(205, 214, 250) !important; }

.bg-indigo-5xlight { background-color: rgb(231, 235, 254) !important; }

/* Indigo Tint Text Colors (lighter) */

.text-indigo-2xlight { color: rgb(141, 155, 236) !important; }

.text-indigo-3xlight { color: rgb(176, 188, 248) !important; }

.text-indigo-4xlight { color: rgb(205, 214, 250) !important; }

.text-indigo-5xlight { color: rgb(231, 235, 254) !important; }

/* Indigo Shade Background Colors (darker) */

.bg-indigo-2xdark { background-color: rgb(60, 63, 179) !important; }

.bg-indigo-3xdark { background-color: rgb(43, 34, 157) !important; }

.bg-indigo-4xdark { background-color: rgb(26, 0, 123) !important; background-color: color(display-p3 0.08473 0 0.46405) !important; }

.bg-indigo-5xdark { background-color: rgb(13, 0, 76) !important; background-color: color(display-p3 0.03529 0 0.28851) !important; }

/* Indigo Shade Text Colors (darker) */

.text-indigo-2xdark { color: rgb(60, 63, 179) !important; }

.text-indigo-3xdark { color: rgb(43, 34, 157) !important; }

.text-indigo-4xdark { color: rgb(26, 0, 123) !important; color: color(display-p3 0.08473 0 0.46405) !important; }

.text-indigo-5xdark { color: rgb(13, 0, 76) !important; color: color(display-p3 0.03529 0 0.28851) !important; }

/**
 * Purple Color System - OKLCH
 * Base purple with 5 tints and 5 shades for creative themes, luxury
 */

/* Purple Base Color */

.bg-purple { background-color: rgb(150, 74, 198) !important; }

.text-purple { color: rgb(150, 74, 198) !important; }

/* Purple Tint Background Colors (lighter) */

.bg-purple-2xlight { background-color: rgb(195, 145, 232) !important; }

.bg-purple-3xlight { background-color: rgb(216, 181, 244) !important; }

.bg-purple-4xlight { background-color: rgb(230, 209, 248) !important; }

.bg-purple-5xlight { background-color: rgb(243, 233, 252) !important; }

/* Purple Tint Text Colors (lighter) */

.text-purple-2xlight { color: rgb(195, 145, 232) !important; }

.text-purple-3xlight { color: rgb(216, 181, 244) !important; }

.text-purple-4xlight { color: rgb(230, 209, 248) !important; }

.text-purple-5xlight { color: rgb(243, 233, 252) !important; }

/* Purple Shade Background Colors (darker) */

.bg-purple-2xdark { background-color: rgb(126, 42, 174) !important; }

.bg-purple-3xdark { background-color: rgb(103, 0, 150) !important; background-color: color(display-p3 0.36702 0.0042 0.56425) !important; }

.bg-purple-4xdark { background-color: rgb(65, 0, 99) !important; background-color: color(display-p3 0.2318 0 0.38096) !important; }

.bg-purple-5xdark { background-color: rgb(37, 0, 60) !important; background-color: color(display-p3 0.1312 0 0.2319) !important; }

/* Purple Shade Text Colors (darker) */

.text-purple-2xdark { color: rgb(126, 42, 174) !important; }

.text-purple-3xdark { color: rgb(103, 0, 150) !important; color: color(display-p3 0.36702 0.0042 0.56425) !important; }

.text-purple-4xdark { color: rgb(65, 0, 99) !important; color: color(display-p3 0.2318 0 0.38096) !important; }

.text-purple-5xdark { color: rgb(37, 0, 60) !important; color: color(display-p3 0.1312 0 0.2319) !important; }

/**
 * Pink Color System - OKLCH
 * Base pink with 5 tints and 5 shades for feminine themes, highlights
 */

/* Pink Base Color */

.bg-pink { background-color: rgb(231, 104, 139) !important; }

.text-pink { color: rgb(231, 104, 139) !important; }

/* Pink Tint Background Colors (lighter) */

.bg-pink-2xlight { background-color: rgb(254, 161, 182) !important; }

.bg-pink-3xlight { background-color: rgb(255, 191, 206) !important; background-color: color(display-p3 0.9771 0.76168 0.80699) !important; }

.bg-pink-4xlight { background-color: rgb(255, 216, 224) !important; background-color: color(display-p3 0.98532 0.85319 0.87918) !important; }

.bg-pink-5xlight { background-color: rgb(255, 236, 240) !important; background-color: color(display-p3 0.99606 0.92869 0.94133) !important; }

/* Pink Tint Text Colors (lighter) */

.text-pink-2xlight { color: rgb(254, 161, 182) !important; }

.text-pink-3xlight { color: rgb(255, 191, 206) !important; color: color(display-p3 0.9771 0.76168 0.80699) !important; }

.text-pink-4xlight { color: rgb(255, 216, 224) !important; color: color(display-p3 0.98532 0.85319 0.87918) !important; }

.text-pink-5xlight { color: rgb(255, 236, 240) !important; color: color(display-p3 0.99606 0.92869 0.94133) !important; }

/* Pink Shade Background Colors (darker) */

.bg-pink-2xdark { background-color: rgb(198, 68, 108) !important; }

.bg-pink-3xdark { background-color: rgb(166, 25, 79) !important; }

.bg-pink-4xdark { background-color: rgb(113, 0, 45) !important; background-color: color(display-p3 0.42044 0 0.17157) !important; }

.bg-pink-5xdark { background-color: rgb(69, 0, 23) !important; background-color: color(display-p3 0.2547 0 0.08854) !important; }

/* Pink Shade Text Colors (darker) */

.text-pink-2xdark { color: rgb(198, 68, 108) !important; }

.text-pink-3xdark { color: rgb(166, 25, 79) !important; }

.text-pink-4xdark { color: rgb(113, 0, 45) !important; color: color(display-p3 0.42044 0 0.17157) !important; }

.text-pink-5xdark { color: rgb(69, 0, 23) !important; color: color(display-p3 0.2547 0 0.08854) !important; }

/* ====================================================================== */

/* TRANSPARENCY COLOR UTILITIES - OKLCH ALPHA SYSTEM                     */

/* ====================================================================== */

/**
 * Creative transparency variations using OKLCH with alpha channels
 * These provide sophisticated overlay and background effects with superior color mixing
 */

/* Black Transparency Background Colors */

.bg-black-whisper { background-color: rgba(3, 3, 4, 0.1) !important; }

.bg-black-breath { background-color: rgba(3, 3, 4, 0.2) !important; }

.bg-black-mist { background-color: rgba(3, 3, 4, 0.3) !important; }

.bg-black-veil { background-color: rgba(3, 3, 4, 0.4) !important; }

.bg-black-shadow { background-color: rgba(3, 3, 4, 0.5) !important; }

.bg-black-shroud { background-color: rgba(3, 3, 4, 0.6) !important; }

.bg-black-cloak { background-color: rgba(3, 3, 4, 0.7) !important; }

.bg-black-eclipse { background-color: rgba(3, 3, 4, 0.8) !important; }

.bg-black-void { background-color: rgba(3, 3, 4, 0.9) !important; }

/* Gray Transparency Background Colors */

.bg-gray-whisper { background-color: rgba(97, 99, 105, 0.1) !important; }

.bg-gray-breath { background-color: rgba(97, 99, 105, 0.2) !important; }

.bg-gray-mist { background-color: rgba(97, 99, 105, 0.3) !important; }

.bg-gray-veil { background-color: rgba(97, 99, 105, 0.4) !important; }

.bg-gray-shadow { background-color: rgba(97, 99, 105, 0.5) !important; }

.bg-gray-shroud { background-color: rgba(97, 99, 105, 0.6) !important; }

.bg-gray-cloak { background-color: rgba(97, 99, 105, 0.7) !important; }

.bg-gray-eclipse { background-color: rgba(97, 99, 105, 0.8) !important; }

.bg-gray-void { background-color: rgba(97, 99, 105, 0.9) !important; }

/* White Transparency Background Colors */

.bg-white-whisper { background-color: rgba(237, 238, 242, 0.1) !important; }

.bg-white-breath { background-color: rgba(237, 238, 242, 0.2) !important; }

.bg-white-mist { background-color: rgba(237, 238, 242, 0.3) !important; }

.bg-white-veil { background-color: rgba(237, 238, 242, 0.4) !important; }

.bg-white-shadow { background-color: rgba(237, 238, 242, 0.5) !important; }

.bg-white-shroud { background-color: rgba(237, 238, 242, 0.6) !important; }

.bg-white-cloak { background-color: rgba(237, 238, 242, 0.7) !important; }

.bg-white-eclipse { background-color: rgba(237, 238, 242, 0.8) !important; }

.bg-white-void { background-color: rgba(237, 238, 242, 0.9) !important; }

/* Brand Color Transparency Background Colors */

.bg-primary-whisper { background-color: rgba(39, 132, 213, 0.1) !important; }

.bg-primary-breath { background-color: rgba(39, 132, 213, 0.2) !important; }

.bg-primary-mist { background-color: rgba(39, 132, 213, 0.3) !important; }

.bg-primary-veil { background-color: rgba(39, 132, 213, 0.4) !important; }

.bg-primary-shadow { background-color: rgba(39, 132, 213, 0.5) !important; }

.bg-secondary-whisper { background-color: rgba(81, 97, 145, 0.1) !important; }

.bg-secondary-breath { background-color: rgba(81, 97, 145, 0.2) !important; }

.bg-secondary-mist { background-color: rgba(81, 97, 145, 0.3) !important; }

.bg-secondary-veil { background-color: rgba(81, 97, 145, 0.4) !important; }

.bg-secondary-shadow { background-color: rgba(81, 97, 145, 0.5) !important; }

.bg-accent-whisper { background-color: rgba(236, 125, 0, 0.1) !important; background-color: color(display-p3 0.86926 0.51255 0.09455 / 0.1) !important; }

.bg-accent-breath { background-color: rgba(236, 125, 0, 0.2) !important; background-color: color(display-p3 0.86926 0.51255 0.09455 / 0.2) !important; }

.bg-accent-mist { background-color: rgba(236, 125, 0, 0.3) !important; background-color: color(display-p3 0.86926 0.51255 0.09455 / 0.3) !important; }

.bg-accent-veil { background-color: rgba(236, 125, 0, 0.4) !important; background-color: color(display-p3 0.86926 0.51255 0.09455 / 0.4) !important; }

.bg-accent-shadow { background-color: rgba(236, 125, 0, 0.5) !important; background-color: color(display-p3 0.86926 0.51255 0.09455 / 0.5) !important; }

/* ====================================================================== */

/* SEMANTIC COLOR UTILITIES - OKLCH SYSTEM                               */

/* ====================================================================== */

/**
 * Colors that convey meaning and state information with OKLCH variations
 * Each semantic color includes 5 tints and 5 shades for comprehensive usage
 * All colors maintain WCAG AA contrast ratios for accessibility
 */

/* Semantic Base Colors */

.bg-success { background-color: rgb(58, 168, 91) !important; }

.bg-warning { background-color: rgb(223, 161, 26) !important; }

.bg-error { background-color: rgb(215, 71, 69) !important; }

.bg-info { background-color: rgb(39, 132, 213) !important; }

.bg-visited { background-color: rgb(128, 89, 187) !important; }

.text-success { color: rgb(58, 168, 91) !important; }

.text-warning { color: rgb(223, 161, 26) !important; }

.text-error { color: rgb(215, 71, 69) !important; }

.text-info { color: rgb(39, 132, 213) !important; }

.text-visited { color: rgb(128, 89, 187) !important; }

/* Success Color Variations */

.bg-success-2xlight { background-color: rgb(116, 197, 134) !important; }

.bg-success-3xlight { background-color: rgb(156, 215, 167) !important; }

.bg-success-4xlight { background-color: rgb(193, 232, 200) !important; }

.bg-success-5xlight { background-color: rgb(224, 244, 227) !important; }

.bg-success-2xdark { background-color: rgb(0, 129, 46) !important; background-color: color(display-p3 0.16319 0.49824 0.22189) !important; }

.bg-success-3xdark { background-color: rgb(0, 88, 20) !important; background-color: color(display-p3 0 0.35214 0.03561) !important; }

.bg-success-4xdark { background-color: rgb(0, 48, 2) !important; background-color: color(display-p3 0 0.19395 0) !important; }

.bg-success-5xdark { background-color: rgb(0, 21, 0) !important; background-color: color(display-p3 0 0.08558 0) !important; }

.text-success-2xlight { color: rgb(116, 197, 134) !important; }

.text-success-3xlight { color: rgb(156, 215, 167) !important; }

.text-success-4xlight { color: rgb(193, 232, 200) !important; }

.text-success-5xlight { color: rgb(224, 244, 227) !important; }

.text-success-2xdark { color: rgb(0, 129, 46) !important; color: color(display-p3 0.16319 0.49824 0.22189) !important; }

.text-success-3xdark { color: rgb(0, 88, 20) !important; color: color(display-p3 0 0.35214 0.03561) !important; }

.text-success-4xdark { color: rgb(0, 48, 2) !important; color: color(display-p3 0 0.19395 0) !important; }

.text-success-5xdark { color: rgb(0, 21, 0) !important; color: color(display-p3 0 0.08558 0) !important; }

/* Warning Color Variations */

.bg-warning-2xlight { background-color: rgb(239, 189, 101) !important; }

.bg-warning-3xlight { background-color: rgb(245, 208, 145) !important; }

.bg-warning-4xlight { background-color: rgb(252, 227, 186) !important; }

.bg-warning-5xlight { background-color: rgb(253, 241, 221) !important; }

.bg-warning-2xdark { background-color: rgb(172, 116, 0) !important; background-color: color(display-p3 0.65974 0.4553 0) !important; }

.bg-warning-3xdark { background-color: rgb(117, 76, 0) !important; background-color: color(display-p3 0.44777 0.29908 0) !important; }

.bg-warning-4xdark { background-color: rgb(66, 39, 0) !important; background-color: color(display-p3 0.2526 0.15565 0) !important; }

.bg-warning-5xdark { background-color: rgb(32, 15, 0) !important; background-color: color(display-p3 0.12057 0.05921 0) !important; }

.text-warning-2xlight { color: rgb(239, 189, 101) !important; }

.text-warning-3xlight { color: rgb(245, 208, 145) !important; }

.text-warning-4xlight { color: rgb(252, 227, 186) !important; }

.text-warning-5xlight { color: rgb(253, 241, 221) !important; }

.text-warning-2xdark { color: rgb(172, 116, 0) !important; color: color(display-p3 0.65974 0.4553 0) !important; }

.text-warning-3xdark { color: rgb(117, 76, 0) !important; color: color(display-p3 0.44777 0.29908 0) !important; }

.text-warning-4xdark { color: rgb(66, 39, 0) !important; color: color(display-p3 0.2526 0.15565 0) !important; }

.text-warning-5xdark { color: rgb(32, 15, 0) !important; color: color(display-p3 0.12057 0.05921 0) !important; }

/* Error Color Variations */

.bg-error-2xlight { background-color: rgb(241, 125, 118) !important; }

.bg-error-3xlight { background-color: rgb(252, 162, 154) !important; }

.bg-error-4xlight { background-color: rgb(255, 198, 192) !important; background-color: color(display-p3 0.9817 0.78535 0.75922) !important; }

.bg-error-5xlight { background-color: rgb(255, 227, 223) !important; background-color: color(display-p3 0.99386 0.89298 0.87863) !important; }

.bg-error-2xdark { background-color: rgb(179, 0, 25) !important; background-color: color(display-p3 0.64135 0.11604 0.13463) !important; }

.bg-error-3xdark { background-color: rgb(126, 0, 3) !important; background-color: color(display-p3 0.46952 0 0.00876) !important; }

.bg-error-4xdark { background-color: rgb(72, 0, 0) !important; background-color: color(display-p3 0.26798 0 0) !important; }

.bg-error-5xdark { background-color: rgb(36, 0, 0) !important; background-color: color(display-p3 0.13155 0 0) !important; }

.text-error-2xlight { color: rgb(241, 125, 118) !important; }

.text-error-3xlight { color: rgb(252, 162, 154) !important; }

.text-error-4xlight { color: rgb(255, 198, 192) !important; color: color(display-p3 0.9817 0.78535 0.75922) !important; }

.text-error-5xlight { color: rgb(255, 227, 223) !important; color: color(display-p3 0.99386 0.89298 0.87863) !important; }

.text-error-2xdark { color: rgb(179, 0, 25) !important; color: color(display-p3 0.64135 0.11604 0.13463) !important; }

.text-error-3xdark { color: rgb(126, 0, 3) !important; color: color(display-p3 0.46952 0 0.00876) !important; }

.text-error-4xdark { color: rgb(72, 0, 0) !important; color: color(display-p3 0.26798 0 0) !important; }

.text-error-5xdark { color: rgb(36, 0, 0) !important; color: color(display-p3 0.13155 0 0) !important; }

/* Info Color Variations */

.bg-info-2xlight { background-color: rgb(103, 170, 237) !important; }

.bg-info-3xlight { background-color: rgb(145, 195, 246) !important; }

.bg-info-4xlight { background-color: rgb(186, 219, 254) !important; }

.bg-info-5xlight { background-color: rgb(221, 237, 255) !important; background-color: color(display-p3 0.87736 0.92848 0.9935) !important; }

.bg-info-2xdark { background-color: rgb(0, 94, 181) !important; background-color: color(display-p3 0.06248 0.36112 0.68706) !important; }

.bg-info-3xdark { background-color: rgb(0, 60, 129) !important; background-color: color(display-p3 0 0.21727 0.54103) !important; }

.bg-info-4xdark { background-color: rgb(0, 29, 78) !important; background-color: color(display-p3 0 0.10188 0.3246) !important; }

.bg-info-5xdark { background-color: rgb(0, 8, 44) !important; background-color: color(display-p3 0 0.02366 0.17799) !important; }

.text-info-2xlight { color: rgb(103, 170, 237) !important; }

.text-info-3xlight { color: rgb(145, 195, 246) !important; }

.text-info-4xlight { color: rgb(186, 219, 254) !important; }

.text-info-5xlight { color: rgb(221, 237, 255) !important; color: color(display-p3 0.87736 0.92848 0.9935) !important; }

.text-info-2xdark { color: rgb(0, 94, 181) !important; color: color(display-p3 0.06248 0.36112 0.68706) !important; }

.text-info-3xdark { color: rgb(0, 60, 129) !important; color: color(display-p3 0 0.21727 0.54103) !important; }

.text-info-4xdark { color: rgb(0, 29, 78) !important; color: color(display-p3 0 0.10188 0.3246) !important; }

.text-info-5xdark { color: rgb(0, 8, 44) !important; color: color(display-p3 0 0.02366 0.17799) !important; }

/* Visited Color Variations */

.bg-visited-2xlight { background-color: rgb(165, 136, 217) !important; }

.bg-visited-3xlight { background-color: rgb(190, 169, 231) !important; }

.bg-visited-4xlight { background-color: rgb(216, 202, 245) !important; }

.bg-visited-5xlight { background-color: rgb(235, 229, 250) !important; }

.bg-visited-2xdark { background-color: rgb(98, 51, 158) !important; }

.bg-visited-3xdark { background-color: rgb(71, 0, 129) !important; background-color: color(display-p3 0.25174 0.01252 0.4851) !important; }

.bg-visited-4xdark { background-color: rgb(39, 0, 79) !important; background-color: color(display-p3 0.13694 0 0.30439) !important; }

.bg-visited-5xdark { background-color: rgb(17, 0, 41) !important; background-color: color(display-p3 0.05396 0 0.15506) !important; }

.text-visited-2xlight { color: rgb(165, 136, 217) !important; }

.text-visited-3xlight { color: rgb(190, 169, 231) !important; }

.text-visited-4xlight { color: rgb(216, 202, 245) !important; }

.text-visited-5xlight { color: rgb(235, 229, 250) !important; }

.text-visited-2xdark { color: rgb(98, 51, 158) !important; }

.text-visited-3xdark { color: rgb(71, 0, 129) !important; color: color(display-p3 0.25174 0.01252 0.4851) !important; }

.text-visited-4xdark { color: rgb(39, 0, 79) !important; color: color(display-p3 0.13694 0 0.30439) !important; }

.text-visited-5xdark { color: rgb(17, 0, 41) !important; color: color(display-p3 0.05396 0 0.15506) !important; }

/**
 * Mico CSS Framework - Layout Utilities
 *
 * This file provides utilities for controlling the layout of elements:
 *
 * - Box model properties (sizing, decoration, etc.)
 * - Float and clear properties
 * - Isolation and stacking context
 * - Object fit and position
 * - Overflow behavior
 * - Visibility control
 * - Positioning (static, fixed, absolute, etc.)
 * - Display properties (block, inline, flex, grid, etc.)
 * - Flexbox properties (flex container and item control)
 * - Grid properties (grid container and item control)
 * - Width and height control
 * - Min and max width/height control
 * - Aspect ratio control
 * - Overscroll behavior
 * - Z-index control
 * - Gap control (for flex and grid)
 *
 * USAGE:
 * Box Model: .box-border, .box-content
 * Float and Clear: .float-right, .clear-both
 * Isolation: .isolate
 * Object Fit and Position: .object-contain, .object-top
 * Overflow: .overflow-hidden, .overflow-x-auto
 * Visibility: .visibility-hidden
 * Positioning: .fixed, .absolute, .top-0
 * Display: .d-block, .d-flex
 * Flexbox: .flex-row, .flex-wrap, .justify-content-center
 * Grid: .grid, .grid-cols-2, .grid-rows-3
 * Width and Height: .w-100, .h-50
 * Min and Max Width/Height: .min-w-20, .max-h-80
 * Aspect Ratio: .aspect-16-9
 * Z-index: .z-0, .z-10
 * Gap: .gap-4, .gap-x-8
 *
 *

/* Box Model */

.box-border { box-sizing: border-box; }

.box-content { box-sizing: content-box; }

.decoration-slice { -webkit-box-decoration-break: slice; box-decoration-break: slice; }

.decoration-clone { -webkit-box-decoration-break: clone; box-decoration-break: clone; }

/* Float and Clear */

.float-right { float: right !important; }

.float-left { float: left !important; }

.float-none { float: none !important; }

.clear-left { clear: left !important; }

.clear-right { clear: right !important; }

.clear-both { clear: both !important; }

.clear-none { clear: none !important; }

/* Isolation */

.isolate { isolation: isolate; }

.isolate-auto { isolation: auto; }

/* Object Fit and Position */

.object-contain { -o-object-fit: contain; object-fit: contain; }

.object-cover { -o-object-fit: cover; object-fit: cover; }

.object-fill { -o-object-fit: fill; object-fit: fill; }

.object-none { -o-object-fit: none; object-fit: none; }

.object-scale-down { -o-object-fit: scale-down; object-fit: scale-down; }

.object-top { -o-object-position: top; object-position: top; }

.object-bottom { -o-object-position: bottom; object-position: bottom; }

.object-center { -o-object-position: center; object-position: center; }

.object-left { -o-object-position: left; object-position: left; }

.object-right { -o-object-position: right; object-position: right; }

.object-top-left { -o-object-position: top left; object-position: top left; }

.object-top-right { -o-object-position: top right; object-position: top right; }

.object-bottom-left { -o-object-position: bottom left; object-position: bottom left; }

.object-bottom-right { -o-object-position: bottom right; object-position: bottom right; }

/* Overflow */

.overflow-visible { overflow: visible; }

.overflow-hidden { overflow: hidden; }

.overflow-clip { overflow: clip; }

.overflow-scroll { overflow: scroll; }

.overflow-auto { overflow: auto; }

.overflow-x-visible { overflow-x: visible; }

.overflow-x-hidden { overflow-x: hidden; }

.overflow-x-clip { overflow-x: clip; }

.overflow-x-scroll { overflow-x: scroll; }

.overflow-x-auto { overflow-x: auto; }

.overflow-y-visible { overflow-y: visible; }

.overflow-y-hidden { overflow-y: hidden; }

.overflow-y-clip { overflow-y: clip; }

.overflow-y-scroll { overflow-y: scroll; }

.overflow-y-auto { overflow-y: auto; }

/* Visibility */

.visibility-visible { visibility: visible; }

.visibility-hidden { visibility: hidden; }

.visibility-collapse { visibility: collapse; }

/* Overscroll Behavior */

.overscroll-auto { overscroll-behavior: auto; }

.overscroll-contain { overscroll-behavior: contain; }

.overscroll-none { overscroll-behavior: none; }

.overscroll-x-auto { overscroll-behavior-x: auto; }

.overscroll-x-contain { overscroll-behavior-x: contain; }

.overscroll-x-none { overscroll-behavior-x: none; }

.overscroll-y-auto { overscroll-behavior-y: auto; }

.overscroll-y-contain { overscroll-behavior-y: contain; }

.overscroll-y-none { overscroll-behavior-y: none; }

/* Position */

.static { position: static; }

.fixed { position: fixed; }

.absolute { position: absolute; }

.relative { position: relative; }

.sticky { position: sticky; }

.top-0 { top: 0; }

.right-0 { right: 0; }

.bottom-0 { bottom: 0; }

.left-0 { left: 0; }

.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

.inset-auto { top: auto; right: auto; bottom: auto; left: auto; }

.inset-x-0 { left: 0; right: 0; }

.inset-y-0 { top: 0; bottom: 0; }

.inset-x-auto { left: auto; right: auto; }

.inset-y-auto { top: auto; bottom: auto; }

/* Display */

.d-none { display: none !important; }

.d-inline { display: inline !important; }

.d-inline-block { display: inline-block !important; }

.d-block { display: block !important; }

.d-flex { display: flex !important; }

.d-inline-flex { display: inline-flex !important; }

.d-grid { display: grid !important; }

.d-table { display: table !important; }

.d-table-row { display: table-row !important; }

.d-table-cell { display: table-cell !important; }

.d-table-column { display: table-column !important; }

.d-table-column-group { display: table-column-group !important; }

.d-table-header-group { display: table-header-group !important; }

.d-table-footer-group { display: table-footer-group !important; }

.d-table-row-group { display: table-row-group !important; }

.d-flow-root { display: flow-root !important; }

.d-contents { display: contents !important; }

.d-list-item { display: list-item !important; }

.d-ruby { display: ruby !important; }

.d-ruby-base { display: ruby-base !important; }

.d-ruby-text { display: ruby-text !important; }

.d-ruby-base-container { display: ruby-base-container !important; }

/* Flexbox */

.flex-row { flex-direction: row !important; }

.flex-column { flex-direction: column !important; }

.flex-row-reverse { flex-direction: row-reverse !important; }

.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }

.flex-nowrap { flex-wrap: nowrap !important; }

.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }

.justify-content-end { justify-content: flex-end !important; }

.justify-content-center { justify-content: center !important; }

.justify-content-between { justify-content: space-between !important; }

.justify-content-around { justify-content: space-around !important; }

.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }

.align-items-end { align-items: flex-end !important; }

.align-items-center { align-items: center !important; }

.align-items-baseline { align-items: baseline !important; }

.align-items-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }

.align-content-end { align-content: flex-end !important; }

.align-content-center { align-content: center !important; }

.align-content-between { align-content: space-between !important; }

.align-content-around { align-content: space-around !important; }

.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }

.align-self-start { align-self: flex-start !important; }

.align-self-end { align-self: flex-end !important; }

.align-self-center { align-self: center !important; }

.align-self-baseline { align-self: baseline !important; }

.align-self-stretch { align-self: stretch !important; }

.flex-grow-0 { flex-grow: 0 !important; }

.flex-grow-1 { flex-grow: 1 !important; }

.flex-shrink-0 { flex-shrink: 0 !important; }

.flex-shrink-1 { flex-shrink: 1 !important; }

.flex-1 { flex: 1 1 0% !important; }

.flex-auto { flex: 1 1 auto !important; }

.flex-initial { flex: 0 1 auto !important; }

.flex-none { flex: none !important; }

/* Z-index */

.z-auto { z-index: auto; }

.z-0 { z-index: 0; }

.z-4 { z-index: 4; }

.z-8 { z-index: 8; }

.z-12 { z-index: 12; }

.z-16 { z-index: 16; }

.z-20 { z-index: 20; }

.z-n4 { z-index: -4; }

.z-n8 { z-index: -8; }

.z-n12 { z-index: -12; }

.z-n16 { z-index: -16; }

.z-n20 { z-index: -20; }

.z-100 { z-index: 100; }

.z-200 { z-index: 200; }

.z-max { z-index: 9999; }

/* Gap */

.gap-0 { gap: 0 !important; }

.gap-4 { gap: calc(4px * 1) !important; }

.gap-8 { gap: calc(4px * 2) !important; }

.gap-12 { gap: calc(4px * 3) !important; }

.gap-16 { gap: calc(4px * 4) !important; }

.gap-20 { gap: calc(4px * 5) !important; }

.gap-24 { gap: calc(4px * 6) !important; }

.gap-28 { gap: calc(4px * 7) !important; }

.gap-32 { gap: calc(4px * 8) !important; }

.gap-36 { gap: calc(4px * 9) !important; }

.gap-40 { gap: calc(4px * 10) !important; }

/* Grid */

.grid-cols-1 { grid-template-columns: repeat(1, 1fr) !important; }

.grid-cols-2 { grid-template-columns: repeat(2, 1fr) !important; }

.grid-cols-3 { grid-template-columns: repeat(3, 1fr) !important; }

.grid-cols-4 { grid-template-columns: repeat(4, 1fr) !important; }

.grid-cols-5 { grid-template-columns: repeat(5, 1fr) !important; }

.grid-cols-6 { grid-template-columns: repeat(6, 1fr) !important; }

.grid-cols-12 { grid-template-columns: repeat(12, 1fr) !important; }

.grid-rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)) !important; }

.grid-rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)) !important; }

.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)) !important; }

.grid-rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)) !important; }

.grid-rows-5 { grid-template-rows: repeat(5, minmax(0, 1fr)) !important; }

.grid-rows-6 { grid-template-rows: repeat(6, minmax(0, 1fr)) !important; }

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.grid-area {
  grid-area: var(--mico-grid-area);
}

.grid-template-areas {
  grid-template-areas: var(--mico-grid-template-areas);
}

/* Width and Height */

.w-none { width: none !important; }

.w-auto { width: auto !important; }

.w-screen { width: 100vw !important; }

.w-10p { width: 10% !important; }

.w-20p { width: 20% !important; }

.w-30p { width: 30% !important; }

.w-40p { width: 40% !important; }

.w-50p { width: 50% !important; }

.w-60p { width: 60% !important; }

.w-70p { width: 70% !important; }

.w-80p { width: 80% !important; }

.w-90p { width: 90% !important; }

.w-100p { width: 100% !important; }

.h-none { height: none !important; }

.h-auto { height: auto !important; }

.h-screen { height: 100vh !important; }

.h-10p { height: 10% !important; }

.h-20p { height: 20% !important; }

.h-30p { height: 30% !important; }

.h-40p { height: 40% !important; }

.h-50p { height: 50% !important; }

.h-60p { height: 60% !important; }

.h-70p { height: 70% !important; }

.h-80p { height: 80% !important; }

.h-90p { height: 90% !important; }

.h-100p { height: 100% !important; }

/* Column spans */

.col-1 { grid-column: span 1 !important; }

.col-2 { grid-column: span 2 !important; }

.col-3 { grid-column: span 3 !important; }

.col-4 { grid-column: span 4 !important; }

.col-5 { grid-column: span 5 !important; }

.col-6 { grid-column: span 6 !important; }

.col-7 { grid-column: span 7 !important; }

.col-8 { grid-column: span 8 !important; }

.col-9 { grid-column: span 9 !important; }

.col-10 { grid-column: span 10 !important; }

.col-11 { grid-column: span 11 !important; }

.col-12 { grid-column: span 12 !important; }

/* Advanced grid features */

.grid-masonry {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  grid-auto-rows: 1px;
}

.grid-masonry > * {
  page-break-inside: avoid;
  -moz-column-break-inside: avoid;
       break-inside: avoid;
}

/* Fallbacks for older browsers */

@supports not (display: grid) {
  .grid-masonry {
    -moz-column-count: auto;
         column-count: auto;
    -moz-column-width: 200px;
         column-width: 200px;
  }
  .grid-masonry > * {
    display: inline-block;
    width: 100%;
  }
  
  .grid {
    display: flex;
    flex-wrap: wrap;
    margin: calc(-1 * (4px * 4));
  }


  /* Grid item styles */
  .grid > * {
    flex-basis: calc((100% / 12) - (2 * calc(4px * 4)));
    margin: calc(4px * 4);
  }

  /* Responsive grid items */
  @media (max-width: 768px) {
    .grid > * {
      flex-basis: calc(50% - (2 * calc(4px * 4)));
    }
  }

  @media (max-width: 320px) {
    .grid > * {
      flex-basis: 100%;
    }
  }

}

/* ************************************************************************************************ */

/* Fluid Padding Spacing - Useful for section paddings e.g, <section class="container-fluid-md"> */

.container-fluid-xs {
    padding-top: max(calc(4px * 4), min(3vw, calc(4px * 8))) !important;
    padding-bottom: max(calc(4px * 4), min(3vw, calc(4px * 8))) !important;
    padding-left: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important;
    padding-right: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }

.container-fluid-sm {
    padding-top: max(calc(4px * 4), min(4vw, calc(4px * 14))) !important;
    padding-bottom: max(calc(4px * 4), min(4vw, calc(4px * 14))) !important;
    padding-left: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important;
    padding-right: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }

.container-fluid-md {
    padding-top: max(calc(4px * 4), min(6vw, calc(4px * 20))) !important;
    padding-bottom: max(calc(4px * 4), min(6vw, calc(4px * 20))) !important;
    padding-left: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important;
    padding-right: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }

.container-fluid-lg {
    padding-top: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important;
    padding-bottom: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important;
    padding-left: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important;
    padding-right: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }

.container-fluid-xl {
    padding-top: max(calc(4px * 4), min(10vw, calc(4px * 32))) !important;
    padding-bottom: max(calc(4px * 4), min(10vw, calc(4px * 32))) !important;
    padding-left: max(calc(4px * 4), min(10vw, calc(4px * 32))) !important;
    padding-right: max(calc(4px * 4), min(10vw, calc(4px * 32))) !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }

.container-fluid-2xl {
    padding-top: max(calc(4px * 4), min(12vw, calc(4px * 48))) !important;
    padding-bottom: max(calc(4px * 4), min(12vw, calc(4px * 48))) !important;
    padding-left: max(calc(4px * 4), min(12vw, calc(4px * 48))) !important;
    padding-right: max(calc(4px * 4), min(12vw, calc(4px * 48))) !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }

/* Fluid Padding Spacing - Useful for individual sides, x-axis or y-axis paddings e.g, <div class="container-fluid-md"> */

.px-fluid-xs { padding-left: max(calc(4px * 4), min(3vw, calc(4px * 8))) !important; padding-right: max(calc(4px * 4), min(3vw, calc(4px * 8))) !important; }

.py-fluid-xs { padding-top: max(calc(4px * 4), min(3vw, calc(4px * 8))) !important; padding-bottom: max(calc(4px * 4), min(3vw, calc(4px * 8))) !important; }

.px-fluid-sm { padding-left: max(calc(4px * 4), min(4vw, calc(4px * 14))) !important; padding-right: max(calc(4px * 4), min(4vw, calc(4px * 14))) !important; }

.py-fluid-sm { padding-top: max(calc(4px * 4), min(4vw, calc(4px * 14))) !important; padding-bottom: max(calc(4px * 4), min(4vw, calc(4px * 14))) !important; }

.px-fluid-md { padding-left: max(calc(4px * 4), min(6vw, calc(4px * 20))) !important; padding-right: max(calc(4px * 4), min(6vw, calc(4px * 20))) !important; }

.py-fluid-md { padding-top: max(calc(4px * 4), min(6vw, calc(4px * 20))) !important; padding-bottom: max(calc(4px * 4), min(6vw, calc(4px * 20))) !important; }

.px-fluid-lg { padding-left: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important; padding-right: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important; }

.py-fluid-lg { padding-top: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important; padding-bottom: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important; }

.px-fluid-xl { padding-left: max(calc(4px * 4), min(10vw, calc(4px * 32))) !important; padding-right: max(calc(4px * 4), min(10vw, calc(4px * 32))) !important; }

.py-fluid-xl { padding-top: max(calc(4px * 4), min(10vw, calc(4px * 32))) !important; padding-bottom: max(calc(4px * 4), min(10vw, calc(4px * 32))) !important; }

.px-fluid-2xl { padding-left: max(calc(4px * 4), min(12vw, calc(4px * 48))) !important; padding-right: max(calc(4px * 4), min(12vw, calc(4px * 48))) !important; }

.py-fluid-2xl { padding-top: max(calc(4px * 4), min(12vw, calc(4px * 48))) !important; padding-bottom: max(calc(4px * 4), min(12vw, calc(4px * 48))) !important; }

.p-fluid-sm { padding: max(calc(4px * 4), min(4vw, calc(4px * 14))) !important; }

.p-fluid-md { padding: max(calc(4px * 4), min(6vw, calc(4px * 20))) !important; }

.p-fluid-lg { padding: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important; }

.p-fluid-xl { padding: max(calc(4px * 4), min(10vw, calc(4px * 32))) !important; }

.p-fluid-2xl { padding: max(calc(4px * 4), min(12vw, calc(4px * 48))) !important; }

.pt-fluid-sm{ padding-top: max(calc(4px * 4), min(4vw, calc(4px * 14))) !important; }

.pt-fluid-md{ padding-top: max(calc(4px * 4), min(6vw, calc(4px * 20))) !important; }

.pt-fluid-lg{ padding-top: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important; }

.pt-fluid-xl{ padding-top: max(calc(4px * 4), min(10vw, calc(4px * 32))) !important; }

.pt-fluid-2xl{ padding-top: max(calc(4px * 4), min(12vw, calc(4px * 48))) !important; }

.pl-fluid-sm{ padding-left: max(calc(4px * 4), min(4vw, calc(4px * 14))) !important; }

.pl-fluid-md{ padding-left: max(calc(4px * 4), min(6vw, calc(4px * 20))) !important; }

.pl-fluid-lg{ padding-left: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important; }

.pl-fluid-xl{ padding-left: max(calc(4px * 4), min(10vw, calc(4px * 32))) !important; }

.pl-fluid-2xl{ padding-left: max(calc(4px * 4), min(12vw, calc(4px * 48))) !important; }

.pb-fluid-sm{ padding-bottom: max(calc(4px * 4), min(4vw, calc(4px * 14))) !important; }

.pb-fluid-md{ padding-bottom: max(calc(4px * 4), min(6vw, calc(4px * 20))) !important; }

.pb-fluid-lg{ padding-bottom: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important; }

.pb-fluid-xl{ padding-bottom: max(calc(4px * 4), min(10vw, calc(4px * 32))) !important; }

.pb-fluid-2xl{ padding-bottom: max(calc(4px * 4), min(12vw, calc(4px * 48))) !important; }

.pr-fluid-sm{ padding-right: max(calc(4px * 4), min(4vw, calc(4px * 14))) !important; }

.pr-fluid-md{ padding-right: max(calc(4px * 4), min(6vw, calc(4px * 20))) !important; }

.pr-fluid-lg{ padding-right: max(calc(4px * 4), min(8vw, calc(4px * 25))) !important; }

.pr-fluid-xl{ padding-right: max(calc(4px * 4), min(10vw, calc(4px * 32))) !important; }

.pr-fluid-2xl{ padding-right: max(calc(4px * 4), min(12vw, calc(4px * 48))) !important; }

/* Margin */

.m-0 { margin: 0 !important; }

.mt-0 { margin-top: 0 !important; }

.mb-0 { margin-bottom: 0 !important; }

.ml-0 { margin-left: 0 !important; }

.mr-0 { margin-right: 0 !important; }

.m-auto { margin: auto !important; }

.mt-auto { margin-top: auto !important; }

.mb-auto { margin-bottom: auto !important; }

.ml-auto { margin-left: auto !important; }

.mr-auto { margin-right: auto !important; }

.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.my-auto { margin-top: auto !important; margin-bottom: auto !important; }

/* Padding */

.p-0 { padding: 0 !important; }

.pt-0 { padding-top: 0 !important; }

.pb-0 { padding-bottom: 0 !important; }

.pl-0 { padding-left: 0 !important; }

.pr-0 { padding-right: 0 !important; }

.px-0 { padding-left: 0 !important; padding-right: 0 !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }

.p-auto { padding: auto !important; }

.pt-auto { padding-top: auto !important; }

.pb-auto { padding-bottom: auto !important; }

.pl-auto { padding-left: auto !important; }

.pr-auto { padding-right: auto !important; }

.px-auto { padding-left: auto !important; padding-right: auto !important; }

.py-auto { padding-top: auto !important; padding-bottom: auto !important; }

/* Margin classes from 4 to 100 */

.m-4 { margin: calc(4px * 1) !important; }

.m-8 { margin: calc(4px * 2) !important; }

.m-12 { margin: calc(4px * 3) !important; }

.m-16 { margin: calc(4px * 4) !important; }

.m-20 { margin: calc(4px * 5) !important; }

.m-24 { margin: calc(4px * 6) !important; }

.m-28 { margin: calc(4px * 7) !important; }

.m-32 { margin: calc(4px * 8) !important; }

.m-36 { margin: calc(4px * 9) !important; }

.m-40 { margin: calc(4px * 10) !important; }

.m-44 { margin: calc(4px * 11) !important; }

.m-48 { margin: calc(4px * 12) !important; }

.m-52 { margin: calc(4px * 13) !important; }

.m-56 { margin: calc(4px * 14) !important; }

.m-60 { margin: calc(4px * 15) !important; }

.m-64 { margin: calc(4px * 16) !important; }

.m-68 { margin: var(--mico-size-68) !important; }

.m-72 { margin: calc(4px * 18) !important; }

.m-76 { margin: var(--mico-size-76) !important; }

.m-80 { margin: calc(4px * 20) !important; }

.m-84 { margin: var(--mico-size-84) !important; }

.m-88 { margin: var(--mico-size-88) !important; }

.m-92 { margin: var(--mico-size-92) !important; }

.m-96 { margin: calc(4px * 24) !important; }

.m-100 { margin: calc(4px * 25) !important; }

.m-104 { margin: var(--mico-size-104) !important; }

.m-108 { margin: var(--mico-size-108) !important; }

.m-112 { margin: calc(4px * 28) !important; }

.m-116 { margin: var(--mico-size-116) !important; }

.m-120 { margin: var(--mico-size-120) !important; }

.m-124 { margin: var(--mico-size-124) !important; }

.m-128 { margin: calc(4px * 32) !important; }

.m-132 { margin: var(--mico-size-132) !important; }

.m-136 { margin: var(--mico-size-136) !important; }

.m-140 { margin: var(--mico-size-140) !important; }

.m-144 { margin: calc(4px * 36) !important; }

.m-148 { margin: var(--mico-size-148) !important; }

.m-152 { margin: var(--mico-size-152) !important; }

.m-156 { margin: var(--mico-size-156) !important; }

.m-160 { margin: calc(4px * 40) !important; }

.m-164 { margin: var(--mico-size-164) !important; }

.m-168 { margin: var(--mico-size-168) !important; }

.m-172 { margin: var(--mico-size-172) !important; }

.m-176 { margin: calc(4px * 44) !important; }

.m-180 { margin: var(--mico-size-180) !important; }

.m-184 { margin: var(--mico-size-184) !important; }

.m-188 { margin: var(--mico-size-188) !important; }

.m-192 { margin: calc(4px * 48) !important; }

.m-196 { margin: var(--mico-size-196) !important; }

.m-200 { margin: var(--mico-size-200) !important; }

.m-204 { margin: var(--mico-size-204) !important; }

.m-208 { margin: calc(4px * 52) !important; }

.m-212 { margin: var(--mico-size-212) !important; }

.m-216 { margin: var(--mico-size-216) !important; }

.m-220 { margin: var(--mico-size-220) !important; }

.m-224 { margin: calc(4px * 56) !important; }

.m-228 { margin: var(--mico-size-228) !important; }

.m-232 { margin: var(--mico-size-232) !important; }

.m-236 { margin: var(--mico-size-236) !important; }

.m-240 { margin: calc(4px * 60) !important; }

.m-244 { margin: var(--mico-size-244) !important; }

.m-248 { margin: var(--mico-size-248) !important; }

.m-252 { margin: var(--mico-size-252) !important; }

.m-256 { margin: calc(4px * 64) !important; }

.m-260 { margin: calc(4px * 68) !important; }

.m-264 { margin: calc(4px * 72) !important; }

.m-268 { margin: calc(4px * 76) !important; }

.m-272 { margin: calc(4px * 80) !important; }

.m-276 { margin: calc(4px * 84) !important; }

.m-280 { margin: calc(4px * 88) !important; }

.m-284 { margin: calc(4px * 92) !important; }

.m-288 { margin: calc(4px * 96) !important; }

.m-292 { margin: calc(4px * 100) !important; }

.m-296 { margin: calc(4px * 104) !important; }

.m-300 { margin: calc(4px * 108) !important; }

.m-304 { margin: calc(4px * 112) !important; }

.m-308 { margin: calc(4px * 116) !important; }

.m-312 { margin: calc(4px * 120) !important; }

.m-316 { margin: calc(4px * 124) !important; }

.m-320 { margin: calc(4px * 128) !important; }

.m-324 { margin: calc(4px * 132) !important; }

.m-328 { margin: calc(4px * 136) !important; }

.m-332 { margin: calc(4px * 140) !important; }

.m-336 { margin: calc(4px * 144) !important; }

.m-340 { margin: calc(4px * 148) !important; }

.m-344 { margin: calc(4px * 152) !important; }

.m-348 { margin: calc(4px * 156) !important; }

.m-352 { margin: calc(4px * 160) !important; }

.m-356 { margin: calc(4px * 164) !important; }

.m-360 { margin: calc(4px * 168) !important; }

.m-364 { margin: calc(4px * 172) !important; }

.m-368 { margin: calc(4px * 176) !important; }

.m-372 { margin: calc(4px * 180) !important; }

.m-376 { margin: calc(4px * 184) !important; }

.m-380 { margin: calc(4px * 188) !important; }

.m-384 { margin: calc(4px * 192) !important; }

.m-388 { margin: calc(4px * 196) !important; }

.m-392 { margin: calc(4px * 200) !important; }

.m-396 { margin: calc(4px * 204) !important; }

.m-400 { margin: calc(4px * 208) !important; }

/* Margin-top classes from 4 to 100 */

.mt-4 { margin-top: calc(4px * 1) !important; }

.mt-8 { margin-top: calc(4px * 2) !important; }

.mt-12 { margin-top: calc(4px * 3) !important; }

.mt-16 { margin-top: calc(4px * 4) !important; }

.mt-20 { margin-top: calc(4px * 5) !important; }

.mt-24 { margin-top: calc(4px * 6) !important; }

.mt-28 { margin-top: calc(4px * 7) !important; }

.mt-32 { margin-top: calc(4px * 8) !important; }

.mt-36 { margin-top: calc(4px * 9) !important; }

.mt-40 { margin-top: calc(4px * 10) !important; }

.mt-44 { margin-top: calc(4px * 11) !important; }

.mt-48 { margin-top: calc(4px * 12) !important; }

.mt-52 { margin-top: calc(4px * 13) !important; }

.mt-56 { margin-top: calc(4px * 14) !important; }

.mt-60 { margin-top: calc(4px * 15) !important; }

.mt-64 { margin-top: calc(4px * 16) !important; }

.mt-68 { margin-top: var(--mico-size-68) !important; }

.mt-72 { margin-top: calc(4px * 18) !important; }

.mt-76 { margin-top: var(--mico-size-76) !important; }

.mt-80 { margin-top: calc(4px * 20) !important; }

.mt-84 { margin-top: var(--mico-size-84) !important; }

.mt-88 { margin-top: var(--mico-size-88) !important; }

.mt-92 { margin-top: var(--mico-size-92) !important; }

.mt-96 { margin-top: calc(4px * 24) !important; }

.mt-100 { margin-top: calc(4px * 25) !important; }

.mt-104 { margin-top: var(--mico-size-104) !important; }

.mt-108 { margin-top: var(--mico-size-108) !important; }

.mt-112 { margin-top: calc(4px * 28) !important; }

.mt-116 { margin-top: var(--mico-size-116) !important; }

.mt-120 { margin-top: var(--mico-size-120) !important; }

.mt-124 { margin-top: var(--mico-size-124) !important; }

.mt-128 { margin-top: calc(4px * 32) !important; }

.mt-132 { margin-top: var(--mico-size-132) !important; }

.mt-136 { margin-top: var(--mico-size-136) !important; }

.mt-140 { margin-top: var(--mico-size-140) !important; }

.mt-144 { margin-top: calc(4px * 36) !important; }

.mt-148 { margin-top: var(--mico-size-148) !important; }

.mt-152 { margin-top: var(--mico-size-152) !important; }

.mt-156 { margin-top: var(--mico-size-156) !important; }

.mt-160 { margin-top: calc(4px * 40) !important; }

.mt-164 { margin-top: var(--mico-size-164) !important; }

.mt-168 { margin-top: var(--mico-size-168) !important; }

.mt-172 { margin-top: var(--mico-size-172) !important; }

.mt-176 { margin-top: calc(4px * 44) !important; }

.mt-180 { margin-top: var(--mico-size-180) !important; }

.mt-184 { margin-top: var(--mico-size-184) !important; }

.mt-188 { margin-top: var(--mico-size-188) !important; }

.mt-192 { margin-top: calc(4px * 48) !important; }

.mt-196 { margin-top: var(--mico-size-196) !important; }

.mt-200 { margin-top: var(--mico-size-200) !important; }

.mt-204 { margin-top: var(--mico-size-204) !important; }

.mt-208 { margin-top: calc(4px * 52) !important; }

.mt-212 { margin-top: var(--mico-size-212) !important; }

.mt-216 { margin-top: var(--mico-size-216) !important; }

.mt-220 { margin-top: var(--mico-size-220) !important; }

.mt-224 { margin-top: calc(4px * 56) !important; }

.mt-228 { margin-top: var(--mico-size-228) !important; }

.mt-232 { margin-top: var(--mico-size-232) !important; }

.mt-236 { margin-top: var(--mico-size-236) !important; }

.mt-240 { margin-top: calc(4px * 60) !important; }

.mt-244 { margin-top: var(--mico-size-244) !important; }

.mt-248 { margin-top: var(--mico-size-248) !important; }

.mt-252 { margin-top: var(--mico-size-252) !important; }

.mt-256 { margin-top: calc(4px * 64) !important; }

.mt-260 { margin-top: calc(4px * 68) !important; }

.mt-264 { margin-top: calc(4px * 72) !important; }

.mt-268 { margin-top: calc(4px * 76) !important; }

.mt-272 { margin-top: calc(4px * 80) !important; }

.mt-276 { margin-top: calc(4px * 84) !important; }

.mt-280 { margin-top: calc(4px * 88) !important; }

.mt-284 { margin-top: calc(4px * 92) !important; }

.mt-288 { margin-top: calc(4px * 96) !important; }

.mt-292 { margin-top: calc(4px * 100) !important; }

.mt-296 { margin-top: calc(4px * 104) !important; }

.mt-300 { margin-top: calc(4px * 108) !important; }

.mt-304 { margin-top: calc(4px * 112) !important; }

.mt-308 { margin-top: calc(4px * 116) !important; }

.mt-312 { margin-top: calc(4px * 120) !important; }

.mt-316 { margin-top: calc(4px * 124) !important; }

.mt-320 { margin-top: calc(4px * 128) !important; }

.mt-324 { margin-top: calc(4px * 132) !important; }

.mt-328 { margin-top: calc(4px * 136) !important; }

.mt-332 { margin-top: calc(4px * 140) !important; }

.mt-336 { margin-top: calc(4px * 144) !important; }

.mt-340 { margin-top: calc(4px * 148) !important; }

.mt-344 { margin-top: calc(4px * 152) !important; }

.mt-348 { margin-top: calc(4px * 156) !important; }

.mt-352 { margin-top: calc(4px * 160) !important; }

.mt-356 { margin-top: calc(4px * 164) !important; }

.mt-360 { margin-top: calc(4px * 168) !important; }

.mt-364 { margin-top: calc(4px * 172) !important; }

.mt-368 { margin-top: calc(4px * 176) !important; }

.mt-372 { margin-top: calc(4px * 180) !important; }

.mt-376 { margin-top: calc(4px * 184) !important; }

.mt-380 { margin-top: calc(4px * 188) !important; }

.mt-384 { margin-top: calc(4px * 192) !important; }

.mt-388 { margin-top: calc(4px * 196) !important; }

.mt-392 { margin-top: calc(4px * 200) !important; }

.mt-396 { margin-top: calc(4px * 204) !important; }

.mt-400 { margin-top: calc(4px * 208) !important; }

/* Margin-bottom classes from 4 to 100 */

.mb-4 { margin-bottom: calc(4px * 1) !important; }

.mb-8 { margin-bottom: calc(4px * 2) !important; }

.mb-12 { margin-bottom: calc(4px * 3) !important; }

.mb-16 { margin-bottom: calc(4px * 4) !important; }

.mb-20 { margin-bottom: calc(4px * 5) !important; }

.mb-24 { margin-bottom: calc(4px * 6) !important; }

.mb-28 { margin-bottom: calc(4px * 7) !important; }

.mb-32 { margin-bottom: calc(4px * 8) !important; }

.mb-36 { margin-bottom: calc(4px * 9) !important; }

.mb-40 { margin-bottom: calc(4px * 10) !important; }

.mb-44 { margin-bottom: calc(4px * 11) !important; }

.mb-48 { margin-bottom: calc(4px * 12) !important; }

.mb-52 { margin-bottom: calc(4px * 13) !important; }

.mb-56 { margin-bottom: calc(4px * 14) !important; }

.mb-60 { margin-bottom: calc(4px * 15) !important; }

.mb-64 { margin-bottom: calc(4px * 16) !important; }

.mb-68 { margin-bottom: var(--mico-size-68) !important; }

.mb-72 { margin-bottom: calc(4px * 18) !important; }

.mb-76 { margin-bottom: var(--mico-size-76) !important; }

.mb-80 { margin-bottom: calc(4px * 20) !important; }

.mb-84 { margin-bottom: var(--mico-size-84) !important; }

.mb-88 { margin-bottom: var(--mico-size-88) !important; }

.mb-92 { margin-bottom: var(--mico-size-92) !important; }

.mb-96 { margin-bottom: calc(4px * 24) !important; }

.mb-100 { margin-bottom: calc(4px * 25) !important; }

.mb-104 { margin-bottom: var(--mico-size-104) !important; }

.mb-108 { margin-bottom: var(--mico-size-108) !important; }

.mb-112 { margin-bottom: calc(4px * 28) !important; }

.mb-116 { margin-bottom: var(--mico-size-116) !important; }

.mb-120 { margin-bottom: var(--mico-size-120) !important; }

.mb-124 { margin-bottom: var(--mico-size-124) !important; }

.mb-128 { margin-bottom: calc(4px * 32) !important; }

.mb-132 { margin-bottom: var(--mico-size-132) !important; }

.mb-136 { margin-bottom: var(--mico-size-136) !important; }

.mb-140 { margin-bottom: var(--mico-size-140) !important; }

.mb-144 { margin-bottom: calc(4px * 36) !important; }

.mb-148 { margin-bottom: var(--mico-size-148) !important; }

.mb-152 { margin-bottom: var(--mico-size-152) !important; }

.mb-156 { margin-bottom: var(--mico-size-156) !important; }

.mb-160 { margin-bottom: calc(4px * 40) !important; }

.mb-164 { margin-bottom: var(--mico-size-164) !important; }

.mb-168 { margin-bottom: var(--mico-size-168) !important; }

.mb-172 { margin-bottom: var(--mico-size-172) !important; }

.mb-176 { margin-bottom: calc(4px * 44) !important; }

.mb-180 { margin-bottom: var(--mico-size-180) !important; }

.mb-184 { margin-bottom: var(--mico-size-184) !important; }

.mb-188 { margin-bottom: var(--mico-size-188) !important; }

.mb-192 { margin-bottom: calc(4px * 48) !important; }

.mb-196 { margin-bottom: var(--mico-size-196) !important; }

.mb-200 { margin-bottom: var(--mico-size-200) !important; }

.mb-204 { margin-bottom: var(--mico-size-204) !important; }

.mb-208 { margin-bottom: calc(4px * 52) !important; }

.mb-212 { margin-bottom: var(--mico-size-212) !important; }

.mb-216 { margin-bottom: var(--mico-size-216) !important; }

.mb-220 { margin-bottom: var(--mico-size-220) !important; }

.mb-224 { margin-bottom: calc(4px * 56) !important; }

.mb-228 { margin-bottom: var(--mico-size-228) !important; }

.mb-232 { margin-bottom: var(--mico-size-232) !important; }

.mb-236 { margin-bottom: var(--mico-size-236) !important; }

.mb-240 { margin-bottom: calc(4px * 60) !important; }

.mb-244 { margin-bottom: var(--mico-size-244) !important; }

.mb-248 { margin-bottom: var(--mico-size-248) !important; }

.mb-252 { margin-bottom: var(--mico-size-252) !important; }

.mb-256 { margin-bottom: calc(4px * 64) !important; }

.mb-260 { margin-bottom: calc(4px * 68) !important; }

.mb-264 { margin-bottom: calc(4px * 72) !important; }

.mb-268 { margin-bottom: calc(4px * 76) !important; }

.mb-272 { margin-bottom: calc(4px * 80) !important; }

.mb-276 { margin-bottom: calc(4px * 84) !important; }

.mb-280 { margin-bottom: calc(4px * 88) !important; }

.mb-284 { margin-bottom: calc(4px * 92) !important; }

.mb-288 { margin-bottom: calc(4px * 96) !important; }

.mb-292 { margin-bottom: calc(4px * 100) !important; }

.mb-296 { margin-bottom: calc(4px * 104) !important; }

.mb-300 { margin-bottom: calc(4px * 108) !important; }

.mb-304 { margin-bottom: calc(4px * 112) !important; }

.mb-308 { margin-bottom: calc(4px * 116) !important; }

.mb-312 { margin-bottom: calc(4px * 120) !important; }

.mb-316 { margin-bottom: calc(4px * 124) !important; }

.mb-320 { margin-bottom: calc(4px * 128) !important; }

.mb-324 { margin-bottom: calc(4px * 132) !important; }

.mb-328 { margin-bottom: calc(4px * 136) !important; }

.mb-332 { margin-bottom: calc(4px * 140) !important; }

.mb-336 { margin-bottom: calc(4px * 144) !important; }

.mb-340 { margin-bottom: calc(4px * 148) !important; }

.mb-344 { margin-bottom: calc(4px * 152) !important; }

.mb-348 { margin-bottom: calc(4px * 156) !important; }

.mb-352 { margin-bottom: calc(4px * 160) !important; }

.mb-356 { margin-bottom: calc(4px * 164) !important; }

.mb-360 { margin-bottom: calc(4px * 168) !important; }

.mb-364 { margin-bottom: calc(4px * 172) !important; }

.mb-368 { margin-bottom: calc(4px * 176) !important; }

.mb-372 { margin-bottom: calc(4px * 180) !important; }

.mb-376 { margin-bottom: calc(4px * 184) !important; }

.mb-380 { margin-bottom: calc(4px * 188) !important; }

.mb-384 { margin-bottom: calc(4px * 192) !important; }

.mb-388 { margin-bottom: calc(4px * 196) !important; }

.mb-392 { margin-bottom: calc(4px * 200) !important; }

.mb-396 { margin-bottom: calc(4px * 204) !important; }

.mb-400 { margin-bottom: calc(4px * 208) !important; }

/* Margin-left classes from 4 to 100 */

.ml-4 { margin-left: calc(4px * 1) !important; }

.ml-8 { margin-left: calc(4px * 2) !important; }

.ml-12 { margin-left: calc(4px * 3) !important; }

.ml-16 { margin-left: calc(4px * 4) !important; }

.ml-20 { margin-left: calc(4px * 5) !important; }

.ml-24 { margin-left: calc(4px * 6) !important; }

.ml-28 { margin-left: calc(4px * 7) !important; }

.ml-32 { margin-left: calc(4px * 8) !important; }

.ml-36 { margin-left: calc(4px * 9) !important; }

.ml-40 { margin-left: calc(4px * 10) !important; }

.ml-44 { margin-left: calc(4px * 11) !important; }

.ml-48 { margin-left: calc(4px * 12) !important; }

.ml-52 { margin-left: calc(4px * 13) !important; }

.ml-56 { margin-left: calc(4px * 14) !important; }

.ml-60 { margin-left: calc(4px * 15) !important; }

.ml-64 { margin-left: calc(4px * 16) !important; }

.ml-68 { margin-left: var(--mico-size-68) !important; }

.ml-72 { margin-left: calc(4px * 18) !important; }

.ml-76 { margin-left: var(--mico-size-76) !important; }

.ml-80 { margin-left: calc(4px * 20) !important; }

.ml-84 { margin-left: var(--mico-size-84) !important; }

.ml-88 { margin-left: var(--mico-size-88) !important; }

.ml-92 { margin-left: var(--mico-size-92) !important; }

.ml-96 { margin-left: calc(4px * 24) !important; }

.ml-100 { margin-left: calc(4px * 25) !important; }

.ml-104 { margin-left: var(--mico-size-104) !important; }

.ml-108 { margin-left: var(--mico-size-108) !important; }

.ml-112 { margin-left: calc(4px * 28) !important; }

.ml-116 { margin-left: var(--mico-size-116) !important; }

.ml-120 { margin-left: var(--mico-size-120) !important; }

.ml-124 { margin-left: var(--mico-size-124) !important; }

.ml-128 { margin-left: calc(4px * 32) !important; }

.ml-132 { margin-left: var(--mico-size-132) !important; }

.ml-136 { margin-left: var(--mico-size-136) !important; }

.ml-140 { margin-left: var(--mico-size-140) !important; }

.ml-144 { margin-left: calc(4px * 36) !important; }

.ml-148 { margin-left: var(--mico-size-148) !important; }

.ml-152 { margin-left: var(--mico-size-152) !important; }

.ml-156 { margin-left: var(--mico-size-156) !important; }

.ml-160 { margin-left: calc(4px * 40) !important; }

.ml-164 { margin-left: var(--mico-size-164) !important; }

.ml-168 { margin-left: var(--mico-size-168) !important; }

.ml-172 { margin-left: var(--mico-size-172) !important; }

.ml-176 { margin-left: calc(4px * 44) !important; }

.ml-180 { margin-left: var(--mico-size-180) !important; }

.ml-184 { margin-left: var(--mico-size-184) !important; }

.ml-188 { margin-left: var(--mico-size-188) !important; }

.ml-192 { margin-left: calc(4px * 48) !important; }

.ml-196 { margin-left: var(--mico-size-196) !important; }

.ml-200 { margin-left: var(--mico-size-200) !important; }

.ml-204 { margin-left: var(--mico-size-204) !important; }

.ml-208 { margin-left: calc(4px * 52) !important; }

.ml-212 { margin-left: var(--mico-size-212) !important; }

.ml-216 { margin-left: var(--mico-size-216) !important; }

.ml-220 { margin-left: var(--mico-size-220) !important; }

.ml-224 { margin-left: calc(4px * 56) !important; }

.ml-228 { margin-left: var(--mico-size-228) !important; }

.ml-232 { margin-left: var(--mico-size-232) !important; }

.ml-236 { margin-left: var(--mico-size-236) !important; }

.ml-240 { margin-left: calc(4px * 60) !important; }

.ml-244 { margin-left: var(--mico-size-244) !important; }

.ml-248 { margin-left: var(--mico-size-248) !important; }

.ml-252 { margin-left: var(--mico-size-252) !important; }

.ml-256 { margin-left: calc(4px * 64) !important; }

.ml-260 { margin-left: calc(4px * 68) !important; }

.ml-264 { margin-left: calc(4px * 72) !important; }

.ml-268 { margin-left: calc(4px * 76) !important; }

.ml-272 { margin-left: calc(4px * 80) !important; }

.ml-276 { margin-left: calc(4px * 84) !important; }

.ml-280 { margin-left: calc(4px * 88) !important; }

.ml-284 { margin-left: calc(4px * 92) !important; }

.ml-288 { margin-left: calc(4px * 96) !important; }

.ml-292 { margin-left: calc(4px * 100) !important; }

.ml-296 { margin-left: calc(4px * 104) !important; }

.ml-300 { margin-left: calc(4px * 108) !important; }

.ml-304 { margin-left: calc(4px * 112) !important; }

.ml-308 { margin-left: calc(4px * 116) !important; }

.ml-312 { margin-left: calc(4px * 120) !important; }

.ml-316 { margin-left: calc(4px * 124) !important; }

.ml-320 { margin-left: calc(4px * 128) !important; }

.ml-324 { margin-left: calc(4px * 132) !important; }

.ml-328 { margin-left: calc(4px * 136) !important; }

.ml-332 { margin-left: calc(4px * 140) !important; }

.ml-336 { margin-left: calc(4px * 144) !important; }

.ml-340 { margin-left: calc(4px * 148) !important; }

.ml-344 { margin-left: calc(4px * 152) !important; }

.ml-348 { margin-left: calc(4px * 156) !important; }

.ml-352 { margin-left: calc(4px * 160) !important; }

.ml-356 { margin-left: calc(4px * 164) !important; }

.ml-360 { margin-left: calc(4px * 168) !important; }

.ml-364 { margin-left: calc(4px * 172) !important; }

.ml-368 { margin-left: calc(4px * 176) !important; }

.ml-372 { margin-left: calc(4px * 180) !important; }

.ml-376 { margin-left: calc(4px * 184) !important; }

.ml-380 { margin-left: calc(4px * 188) !important; }

.ml-384 { margin-left: calc(4px * 192) !important; }

.ml-388 { margin-left: calc(4px * 196) !important; }

.ml-392 { margin-left: calc(4px * 200) !important; }

.ml-396 { margin-left: calc(4px * 204) !important; }

.ml-400 { margin-left: calc(4px * 208) !important; }

/* Margin-right classes from 4 to 100 */

.mr-4 { margin-right: calc(4px * 1) !important; }

.mr-8 { margin-right: calc(4px * 2) !important; }

.mr-12 { margin-right: calc(4px * 3) !important; }

.mr-16 { margin-right: calc(4px * 4) !important; }

.mr-20 { margin-right: calc(4px * 5) !important; }

.mr-24 { margin-right: calc(4px * 6) !important; }

.mr-28 { margin-right: calc(4px * 7) !important; }

.mr-32 { margin-right: calc(4px * 8) !important; }

.mr-36 { margin-right: calc(4px * 9) !important; }

.mr-40 { margin-right: calc(4px * 10) !important; }

.mr-44 { margin-right: calc(4px * 11) !important; }

.mr-48 { margin-right: calc(4px * 12) !important; }

.mr-52 { margin-right: calc(4px * 13) !important; }

.mr-56 { margin-right: calc(4px * 14) !important; }

.mr-60 { margin-right: calc(4px * 15) !important; }

.mr-64 { margin-right: calc(4px * 16) !important; }

.mr-68 { margin-right: var(--mico-size-68) !important; }

.mr-72 { margin-right: calc(4px * 18) !important; }

.mr-76 { margin-right: var(--mico-size-76) !important; }

.mr-80 { margin-right: calc(4px * 20) !important; }

.mr-84 { margin-right: var(--mico-size-84) !important; }

.mr-88 { margin-right: var(--mico-size-88) !important; }

.mr-92 { margin-right: var(--mico-size-92) !important; }

.mr-96 { margin-right: calc(4px * 24) !important; }

.mr-100 { margin-right: calc(4px * 25) !important; }

.mr-104 { margin-right: var(--mico-size-104) !important; }

.mr-108 { margin-right: var(--mico-size-108) !important; }

.mr-112 { margin-right: calc(4px * 28) !important; }

.mr-116 { margin-right: var(--mico-size-116) !important; }

.mr-120 { margin-right: var(--mico-size-120) !important; }

.mr-124 { margin-right: var(--mico-size-124) !important; }

.mr-128 { margin-right: calc(4px * 32) !important; }

.mr-132 { margin-right: var(--mico-size-132) !important; }

.mr-136 { margin-right: var(--mico-size-136) !important; }

.mr-140 { margin-right: var(--mico-size-140) !important; }

.mr-144 { margin-right: calc(4px * 36) !important; }

.mr-148 { margin-right: var(--mico-size-148) !important; }

.mr-152 { margin-right: var(--mico-size-152) !important; }

.mr-156 { margin-right: var(--mico-size-156) !important; }

.mr-160 { margin-right: calc(4px * 40) !important; }

.mr-164 { margin-right: var(--mico-size-164) !important; }

.mr-168 { margin-right: var(--mico-size-168) !important; }

.mr-172 { margin-right: var(--mico-size-172) !important; }

.mr-176 { margin-right: calc(4px * 44) !important; }

.mr-180 { margin-right: var(--mico-size-180) !important; }

.mr-184 { margin-right: var(--mico-size-184) !important; }

.mr-188 { margin-right: var(--mico-size-188) !important; }

.mr-192 { margin-right: calc(4px * 48) !important; }

.mr-196 { margin-right: var(--mico-size-196) !important; }

.mr-200 { margin-right: var(--mico-size-200) !important; }

.mr-204 { margin-right: var(--mico-size-204) !important; }

.mr-208 { margin-right: calc(4px * 52) !important; }

.mr-212 { margin-right: var(--mico-size-212) !important; }

.mr-216 { margin-right: var(--mico-size-216) !important; }

.mr-220 { margin-right: var(--mico-size-220) !important; }

.mr-224 { margin-right: calc(4px * 56) !important; }

.mr-228 { margin-right: var(--mico-size-228) !important; }

.mr-232 { margin-right: var(--mico-size-232) !important; }

.mr-236 { margin-right: var(--mico-size-236) !important; }

.mr-240 { margin-right: calc(4px * 60) !important; }

.mr-244 { margin-right: var(--mico-size-244) !important; }

.mr-248 { margin-right: var(--mico-size-248) !important; }

.mr-252 { margin-right: var(--mico-size-252) !important; }

.mr-256 { margin-right: calc(4px * 64) !important; }

.mr-260 { margin-right: calc(4px * 68) !important; }

.mr-264 { margin-right: calc(4px * 72) !important; }

.mr-268 { margin-right: calc(4px * 76) !important; }

.mr-272 { margin-right: calc(4px * 80) !important; }

.mr-276 { margin-right: calc(4px * 84) !important; }

.mr-280 { margin-right: calc(4px * 88) !important; }

.mr-284 { margin-right: calc(4px * 92) !important; }

.mr-288 { margin-right: calc(4px * 96) !important; }

.mr-292 { margin-right: calc(4px * 100) !important; }

.mr-296 { margin-right: calc(4px * 104) !important; }

.mr-300 { margin-right: calc(4px * 108) !important; }

.mr-304 { margin-right: calc(4px * 112) !important; }

.mr-308 { margin-right: calc(4px * 116) !important; }

.mr-312 { margin-right: calc(4px * 120) !important; }

.mr-316 { margin-right: calc(4px * 124) !important; }

.mr-320 { margin-right: calc(4px * 128) !important; }

.mr-324 { margin-right: calc(4px * 132) !important; }

.mr-328 { margin-right: calc(4px * 136) !important; }

.mr-332 { margin-right: calc(4px * 140) !important; }

.mr-336 { margin-right: calc(4px * 144) !important; }

.mr-340 { margin-right: calc(4px * 148) !important; }

.mr-344 { margin-right: calc(4px * 152) !important; }

.mr-348 { margin-right: calc(4px * 156) !important; }

.mr-352 { margin-right: calc(4px * 160) !important; }

.mr-356 { margin-right: calc(4px * 164) !important; }

.mr-360 { margin-right: calc(4px * 168) !important; }

.mr-364 { margin-right: calc(4px * 172) !important; }

.mr-368 { margin-right: calc(4px * 176) !important; }

.mr-372 { margin-right: calc(4px * 180) !important; }

.mr-376 { margin-right: calc(4px * 184) !important; }

.mr-380 { margin-right: calc(4px * 188) !important; }

.mr-384 { margin-right: calc(4px * 192) !important; }

.mr-388 { margin-right: calc(4px * 196) !important; }

.mr-392 { margin-right: calc(4px * 200) !important; }

.mr-396 { margin-right: calc(4px * 204) !important; }

.mr-400 { margin-right: calc(4px * 208) !important; }

/* Margin-inline classes from 4 to 100 */

.mx-4 { margin-left: calc(4px * 1) !important; margin-right: calc(4px * 1) !important; }

.mx-8 { margin-left: calc(4px * 2) !important; margin-right: calc(4px * 2) !important; }

.mx-12 { margin-left: calc(4px * 3) !important; margin-right: calc(4px * 3) !important; }

.mx-16 { margin-left: calc(4px * 4) !important; margin-right: calc(4px * 4) !important; }

.mx-20 { margin-left: calc(4px * 5) !important; margin-right: calc(4px * 5) !important; }

.mx-24 { margin-left: calc(4px * 6) !important; margin-right: calc(4px * 6) !important; }

.mx-28 { margin-left: calc(4px * 7) !important; margin-right: calc(4px * 7) !important; }

.mx-32 { margin-left: calc(4px * 8) !important; margin-right: calc(4px * 8) !important; }

.mx-36 { margin-left: calc(4px * 9) !important; margin-right: calc(4px * 9) !important; }

.mx-40 { margin-left: calc(4px * 10) !important; margin-right: calc(4px * 10) !important; }

.mx-44 { margin-left: calc(4px * 11) !important; margin-right: calc(4px * 11) !important; }

.mx-48 { margin-left: calc(4px * 12) !important; margin-right: calc(4px * 12) !important; }

.mx-52 { margin-left: calc(4px * 13) !important; margin-right: calc(4px * 13) !important; }

.mx-56 { margin-left: calc(4px * 14) !important; margin-right: calc(4px * 14) !important; }

.mx-60 { margin-left: calc(4px * 15) !important; margin-right: calc(4px * 15) !important; }

.mx-64 { margin-left: calc(4px * 16) !important; margin-right: calc(4px * 16) !important; }

.mx-68 { margin-left: var(--mico-size-68) !important; margin-right: var(--mico-size-68) !important; }

.mx-72 { margin-left: calc(4px * 18) !important; margin-right: calc(4px * 18) !important; }

.mx-76 { margin-left: var(--mico-size-76) !important; margin-right: var(--mico-size-76) !important; }

.mx-80 { margin-left: calc(4px * 20) !important; margin-right: calc(4px * 20) !important; }

.mx-84 { margin-left: var(--mico-size-84) !important; margin-right: var(--mico-size-84) !important; }

.mx-88 { margin-left: var(--mico-size-88) !important; margin-right: var(--mico-size-88) !important; }

.mx-92 { margin-left: var(--mico-size-92) !important; margin-right: var(--mico-size-92) !important; }

.mx-96 { margin-left: calc(4px * 24) !important; margin-right: calc(4px * 24) !important; }

.mx-100 { margin-left: calc(4px * 25) !important; margin-right: calc(4px * 25) !important; }

.mx-104 { margin-left: var(--mico-size-104) !important; margin-right: var(--mico-size-104) !important; }

.mx-108 { margin-left: var(--mico-size-108) !important; margin-right: var(--mico-size-108) !important; }

.mx-112 { margin-left: calc(4px * 28) !important; margin-right: calc(4px * 28) !important; }

.mx-116 { margin-left: var(--mico-size-116) !important; margin-right: var(--mico-size-116) !important; }

.mx-120 { margin-left: var(--mico-size-120) !important; margin-right: var(--mico-size-120) !important; }

.mx-124 { margin-left: var(--mico-size-124) !important; margin-right: var(--mico-size-124) !important; }

.mx-128 { margin-left: calc(4px * 32) !important; margin-right: calc(4px * 32) !important; }

.mx-132 { margin-left: var(--mico-size-132) !important; margin-right: var(--mico-size-132) !important; }

.mx-136 { margin-left: var(--mico-size-136) !important; margin-right: var(--mico-size-136) !important; }

.mx-140 { margin-left: var(--mico-size-140) !important; margin-right: var(--mico-size-140) !important; }

.mx-144 { margin-left: calc(4px * 36) !important; margin-right: calc(4px * 36) !important; }

.mx-148 { margin-left: var(--mico-size-148) !important; margin-right: var(--mico-size-148) !important; }

.mx-152 { margin-left: var(--mico-size-152) !important; margin-right: var(--mico-size-152) !important; }

.mx-156 { margin-left: var(--mico-size-156) !important; margin-right: var(--mico-size-156) !important; }

.mx-160 { margin-left: calc(4px * 40) !important; margin-right: calc(4px * 40) !important; }

.mx-164 { margin-left: var(--mico-size-164) !important; margin-right: var(--mico-size-164) !important; }

.mx-168 { margin-left: var(--mico-size-168) !important; margin-right: var(--mico-size-168) !important; }

.mx-172 { margin-left: var(--mico-size-172) !important; margin-right: var(--mico-size-172) !important; }

.mx-176 { margin-left: calc(4px * 44) !important; margin-right: calc(4px * 44) !important; }

.mx-180 { margin-left: var(--mico-size-180) !important; margin-right: var(--mico-size-180) !important; }

.mx-184 { margin-left: var(--mico-size-184) !important; margin-right: var(--mico-size-184) !important; }

.mx-188 { margin-left: var(--mico-size-188) !important; margin-right: var(--mico-size-188) !important; }

.mx-192 { margin-left: calc(4px * 48) !important; margin-right: calc(4px * 48) !important; }

.mx-196 { margin-left: var(--mico-size-196) !important; margin-right: var(--mico-size-196) !important; }

.mx-200 { margin-left: var(--mico-size-200) !important; margin-right: var(--mico-size-200) !important; }

.mx-204 { margin-left: var(--mico-size-204) !important; margin-right: var(--mico-size-204) !important; }

.mx-208 { margin-left: calc(4px * 52) !important; margin-right: calc(4px * 52) !important; }

.mx-212 { margin-left: var(--mico-size-212) !important; margin-right: var(--mico-size-212) !important; }

.mx-216 { margin-left: var(--mico-size-216) !important; margin-right: var(--mico-size-216) !important; }

.mx-220 { margin-left: var(--mico-size-220) !important; margin-right: var(--mico-size-220) !important; }

.mx-224 { margin-left: calc(4px * 56) !important; margin-right: calc(4px * 56) !important; }

.mx-228 { margin-left: var(--mico-size-228) !important; margin-right: var(--mico-size-228) !important; }

.mx-232 { margin-left: var(--mico-size-232) !important; margin-right: var(--mico-size-232) !important; }

.mx-236 { margin-left: var(--mico-size-236) !important; margin-right: var(--mico-size-236) !important; }

.mx-240 { margin-left: calc(4px * 60) !important; margin-right: calc(4px * 60) !important; }

.mx-244 { margin-left: var(--mico-size-244) !important; margin-right: var(--mico-size-244) !important; }

.mx-248 { margin-left: var(--mico-size-248) !important; margin-right: var(--mico-size-248) !important; }

.mx-252 { margin-left: var(--mico-size-252) !important; margin-right: var(--mico-size-252) !important; }

.mx-256 { margin-left: calc(4px * 64) !important; margin-right: calc(4px * 64) !important; }

.mx-260 { margin-left: calc(4px * 68) !important; margin-right: calc(4px * 68) !important; }

.mx-264 { margin-left: calc(4px * 72) !important; margin-right: calc(4px * 72) !important; }

.mx-268 { margin-left: calc(4px * 76) !important; margin-right: calc(4px * 76) !important; }

.mx-272 { margin-left: calc(4px * 80) !important; margin-right: calc(4px * 80) !important; }

.mx-276 { margin-left: calc(4px * 84) !important; margin-right: calc(4px * 84) !important; }

.mx-280 { margin-left: calc(4px * 88) !important; margin-right: calc(4px * 88) !important; }

.mx-284 { margin-left: calc(4px * 92) !important; margin-right: calc(4px * 92) !important; }

.mx-288 { margin-left: calc(4px * 96) !important; margin-right: calc(4px * 96) !important; }

.mx-292 { margin-left: calc(4px * 100) !important; margin-right: calc(4px * 100) !important; }

.mx-296 { margin-left: calc(4px * 104) !important; margin-right: calc(4px * 104) !important; }

.mx-300 { margin-left: calc(4px * 108) !important; margin-right: calc(4px * 108) !important; }

.mx-304 { margin-left: calc(4px * 112) !important; margin-right: calc(4px * 112) !important; }

.mx-308 { margin-left: calc(4px * 116) !important; margin-right: calc(4px * 116) !important; }

.mx-312 { margin-left: calc(4px * 120) !important; margin-right: calc(4px * 120) !important; }

.mx-316 { margin-left: calc(4px * 124) !important; margin-right: calc(4px * 124) !important; }

.mx-320 { margin-left: calc(4px * 128) !important; margin-right: calc(4px * 128) !important; }

.mx-324 { margin-left: calc(4px * 132) !important; margin-right: calc(4px * 132) !important; }

.mx-328 { margin-left: calc(4px * 136) !important; margin-right: calc(4px * 136) !important; }

.mx-332 { margin-left: calc(4px * 140) !important; margin-right: calc(4px * 140) !important; }

.mx-336 { margin-left: calc(4px * 144) !important; margin-right: calc(4px * 144) !important; }

.mx-340 { margin-left: calc(4px * 148) !important; margin-right: calc(4px * 148) !important; }

.mx-344 { margin-left: calc(4px * 152) !important; margin-right: calc(4px * 152) !important; }

.mx-348 { margin-left: calc(4px * 156) !important; margin-right: calc(4px * 156) !important; }

.mx-352 { margin-left: calc(4px * 160) !important; margin-right: calc(4px * 160) !important; }

.mx-356 { margin-left: calc(4px * 164) !important; margin-right: calc(4px * 164) !important; }

.mx-360 { margin-left: calc(4px * 168) !important; margin-right: calc(4px * 168) !important; }

.mx-364 { margin-left: calc(4px * 172) !important; margin-right: calc(4px * 172) !important; }

.mx-368 { margin-left: calc(4px * 176) !important; margin-right: calc(4px * 176) !important; }

.mx-372 { margin-left: calc(4px * 180) !important; margin-right: calc(4px * 180) !important; }

.mx-376 { margin-left: calc(4px * 184) !important; margin-right: calc(4px * 184) !important; }

.mx-380 { margin-left: calc(4px * 188) !important; margin-right: calc(4px * 188) !important; }

.mx-384 { margin-left: calc(4px * 192) !important; margin-right: calc(4px * 192) !important; }

.mx-388 { margin-left: calc(4px * 196) !important; margin-right: calc(4px * 196) !important; }

.mx-392 { margin-left: calc(4px * 200) !important; margin-right: calc(4px * 200) !important; }

.mx-396 { margin-left: calc(4px * 204) !important; margin-right: calc(4px * 204) !important; }

.mx-400 { margin-left: calc(4px * 208) !important; margin-right: calc(4px * 208) !important; }

/* Margin-block classes from 4 to 100 */

.my-4 { margin-top: calc(4px * 1) !important; margin-bottom: calc(4px * 1) !important; }

.my-8 { margin-top: calc(4px * 2) !important; margin-bottom: calc(4px * 2) !important; }

.my-12 { margin-top: calc(4px * 3) !important; margin-bottom: calc(4px * 3) !important; }

.my-16 { margin-top: calc(4px * 4) !important; margin-bottom: calc(4px * 4) !important; }

.my-20 { margin-top: calc(4px * 5) !important; margin-bottom: calc(4px * 5) !important; }

.my-24 { margin-top: calc(4px * 6) !important; margin-bottom: calc(4px * 6) !important; }

.my-28 { margin-top: calc(4px * 7) !important; margin-bottom: calc(4px * 7) !important; }

.my-32 { margin-top: calc(4px * 8) !important; margin-bottom: calc(4px * 8) !important; }

.my-36 { margin-top: calc(4px * 9) !important; margin-bottom: calc(4px * 9) !important; }

.my-40 { margin-top: calc(4px * 10) !important; margin-bottom: calc(4px * 10) !important; }

.my-44 { margin-top: calc(4px * 11) !important; margin-bottom: calc(4px * 11) !important; }

.my-48 { margin-top: calc(4px * 12) !important; margin-bottom: calc(4px * 12) !important; }

.my-52 { margin-top: calc(4px * 13) !important; margin-bottom: calc(4px * 13) !important; }

.my-56 { margin-top: calc(4px * 14) !important; margin-bottom: calc(4px * 14) !important; }

.my-60 { margin-top: calc(4px * 15) !important; margin-bottom: calc(4px * 15) !important; }

.my-64 { margin-top: calc(4px * 16) !important; margin-bottom: calc(4px * 16) !important; }

.my-68 { margin-top: var(--mico-size-68) !important; margin-bottom: var(--mico-size-68) !important; }

.my-72 { margin-top: calc(4px * 18) !important; margin-bottom: calc(4px * 18) !important; }

.my-76 { margin-top: var(--mico-size-76) !important; margin-bottom: var(--mico-size-76) !important; }

.my-80 { margin-top: calc(4px * 20) !important; margin-bottom: calc(4px * 20) !important; }

.my-84 { margin-top: var(--mico-size-84) !important; margin-bottom: var(--mico-size-84) !important; }

.my-88 { margin-top: var(--mico-size-88) !important; margin-bottom: var(--mico-size-88) !important; }

.my-92 { margin-top: var(--mico-size-92) !important; margin-bottom: var(--mico-size-92) !important; }

.my-96 { margin-top: calc(4px * 24) !important; margin-bottom: calc(4px * 24) !important; }

.my-100 { margin-top: calc(4px * 25) !important; margin-bottom: calc(4px * 25) !important; }

.my-104 { margin-top: var(--mico-size-104) !important; margin-bottom: var(--mico-size-104) !important; }

.my-108 { margin-top: var(--mico-size-108) !important; margin-bottom: var(--mico-size-108) !important; }

.my-112 { margin-top: calc(4px * 28) !important; margin-bottom: calc(4px * 28) !important; }

.my-116 { margin-top: var(--mico-size-116) !important; margin-bottom: var(--mico-size-116) !important; }

.my-120 { margin-top: var(--mico-size-120) !important; margin-bottom: var(--mico-size-120) !important; }

.my-124 { margin-top: var(--mico-size-124) !important; margin-bottom: var(--mico-size-124) !important; }

.my-128 { margin-top: calc(4px * 32) !important; margin-bottom: calc(4px * 32) !important; }

.my-132 { margin-top: var(--mico-size-132) !important; margin-bottom: var(--mico-size-132) !important; }

.my-136 { margin-top: var(--mico-size-136) !important; margin-bottom: var(--mico-size-136) !important; }

.my-140 { margin-top: var(--mico-size-140) !important; margin-bottom: var(--mico-size-140) !important; }

.my-144 { margin-top: calc(4px * 36) !important; margin-bottom: calc(4px * 36) !important; }

.my-148 { margin-top: var(--mico-size-148) !important; margin-bottom: var(--mico-size-148) !important; }

.my-152 { margin-top: var(--mico-size-152) !important; margin-bottom: var(--mico-size-152) !important; }

.my-156 { margin-top: var(--mico-size-156) !important; margin-bottom: var(--mico-size-156) !important; }

.my-160 { margin-top: calc(4px * 40) !important; margin-bottom: calc(4px * 40) !important; }

.my-164 { margin-top: var(--mico-size-164) !important; margin-bottom: var(--mico-size-164) !important; }

.my-168 { margin-top: var(--mico-size-168) !important; margin-bottom: var(--mico-size-168) !important; }

.my-172 { margin-top: var(--mico-size-172) !important; margin-bottom: var(--mico-size-172) !important; }

.my-176 { margin-top: calc(4px * 44) !important; margin-bottom: calc(4px * 44) !important; }

.my-180 { margin-top: var(--mico-size-180) !important; margin-bottom: var(--mico-size-180) !important; }

.my-184 { margin-top: var(--mico-size-184) !important; margin-bottom: var(--mico-size-184) !important; }

.my-188 { margin-top: var(--mico-size-188) !important; margin-bottom: var(--mico-size-188) !important; }

.my-192 { margin-top: calc(4px * 48) !important; margin-bottom: calc(4px * 48) !important; }

.my-196 { margin-top: var(--mico-size-196) !important; margin-bottom: var(--mico-size-196) !important; }

.my-200 { margin-top: var(--mico-size-200) !important; margin-bottom: var(--mico-size-200) !important; }

.my-204 { margin-top: var(--mico-size-204) !important; margin-bottom: var(--mico-size-204) !important; }

.my-208 { margin-top: calc(4px * 52) !important; margin-bottom: calc(4px * 52) !important; }

.my-212 { margin-top: var(--mico-size-212) !important; margin-bottom: var(--mico-size-212) !important; }

.my-216 { margin-top: var(--mico-size-216) !important; margin-bottom: var(--mico-size-216) !important; }

.my-220 { margin-top: var(--mico-size-220) !important; margin-bottom: var(--mico-size-220) !important; }

.my-224 { margin-top: calc(4px * 56) !important; margin-bottom: calc(4px * 56) !important; }

.my-228 { margin-top: var(--mico-size-228) !important; margin-bottom: var(--mico-size-228) !important; }

.my-232 { margin-top: var(--mico-size-232) !important; margin-bottom: var(--mico-size-232) !important; }

.my-236 { margin-top: var(--mico-size-236) !important; margin-bottom: var(--mico-size-236) !important; }

.my-240 { margin-top: calc(4px * 60) !important; margin-bottom: calc(4px * 60) !important; }

.my-244 { margin-top: var(--mico-size-244) !important; margin-bottom: var(--mico-size-244) !important; }

.my-248 { margin-top: var(--mico-size-248) !important; margin-bottom: var(--mico-size-248) !important; }

.my-252 { margin-top: var(--mico-size-252) !important; margin-bottom: var(--mico-size-252) !important; }

.my-256 { margin-top: calc(4px * 64) !important; margin-bottom: calc(4px * 64) !important; }

.my-260 { margin-top: calc(4px * 68) !important; margin-bottom: calc(4px * 68) !important; }

.my-264 { margin-top: calc(4px * 72) !important; margin-bottom: calc(4px * 72) !important; }

.my-268 { margin-top: calc(4px * 76) !important; margin-bottom: calc(4px * 76) !important; }

.my-272 { margin-top: calc(4px * 80) !important; margin-bottom: calc(4px * 80) !important; }

.my-276 { margin-top: calc(4px * 84) !important; margin-bottom: calc(4px * 84) !important; }

.my-280 { margin-top: calc(4px * 88) !important; margin-bottom: calc(4px * 88) !important; }

.my-284 { margin-top: calc(4px * 92) !important; margin-bottom: calc(4px * 92) !important; }

.my-288 { margin-top: calc(4px * 96) !important; margin-bottom: calc(4px * 96) !important; }

.my-292 { margin-top: calc(4px * 100) !important; margin-bottom: calc(4px * 100) !important; }

.my-296 { margin-top: calc(4px * 104) !important; margin-bottom: calc(4px * 104) !important; }

.my-300 { margin-top: calc(4px * 108) !important; margin-bottom: calc(4px * 108) !important; }

.my-304 { margin-top: calc(4px * 112) !important; margin-bottom: calc(4px * 112) !important; }

.my-308 { margin-top: calc(4px * 116) !important; margin-bottom: calc(4px * 116) !important; }

.my-312 { margin-top: calc(4px * 120) !important; margin-bottom: calc(4px * 120) !important; }

.my-316 { margin-top: calc(4px * 124) !important; margin-bottom: calc(4px * 124) !important; }

.my-320 { margin-top: calc(4px * 128) !important; margin-bottom: calc(4px * 128) !important; }

.my-324 { margin-top: calc(4px * 132) !important; margin-bottom: calc(4px * 132) !important; }

.my-328 { margin-top: calc(4px * 136) !important; margin-bottom: calc(4px * 136) !important; }

.my-332 { margin-top: calc(4px * 140) !important; margin-bottom: calc(4px * 140) !important; }

.my-336 { margin-top: calc(4px * 144) !important; margin-bottom: calc(4px * 144) !important; }

.my-340 { margin-top: calc(4px * 148) !important; margin-bottom: calc(4px * 148) !important; }

.my-344 { margin-top: calc(4px * 152) !important; margin-bottom: calc(4px * 152) !important; }

.my-348 { margin-top: calc(4px * 156) !important; margin-bottom: calc(4px * 156) !important; }

.my-352 { margin-top: calc(4px * 160) !important; margin-bottom: calc(4px * 160) !important; }

.my-356 { margin-top: calc(4px * 164) !important; margin-bottom: calc(4px * 164) !important; }

.my-360 { margin-top: calc(4px * 168) !important; margin-bottom: calc(4px * 168) !important; }

.my-364 { margin-top: calc(4px * 172) !important; margin-bottom: calc(4px * 172) !important; }

.my-368 { margin-top: calc(4px * 176) !important; margin-bottom: calc(4px * 176) !important; }

.my-372 { margin-top: calc(4px * 180) !important; margin-bottom: calc(4px * 180) !important; }

.my-376 { margin-top: calc(4px * 184) !important; margin-bottom: calc(4px * 184) !important; }

.my-380 { margin-top: calc(4px * 188) !important; margin-bottom: calc(4px * 188) !important; }

.my-384 { margin-top: calc(4px * 192) !important; margin-bottom: calc(4px * 192) !important; }

.my-388 { margin-top: calc(4px * 196) !important; margin-bottom: calc(4px * 196) !important; }

.my-392 { margin-top: calc(4px * 200) !important; margin-bottom: calc(4px * 200) !important; }

.my-396 { margin-top: calc(4px * 204) !important; margin-bottom: calc(4px * 204) !important; }

.my-400 { margin-top: calc(4px * 208) !important; margin-bottom: calc(4px * 208) !important; }

/* Padding classes from 4 to 100 */

.p-4 { padding: calc(4px * 1) !important; }

.p-8 { padding: calc(4px * 2) !important; }

.p-12 { padding: calc(4px * 3) !important; }

.p-16 { padding: calc(4px * 4) !important; }

.p-20 { padding: calc(4px * 5) !important; }

.p-24 { padding: calc(4px * 6) !important; }

.p-28 { padding: calc(4px * 7) !important; }

.p-32 { padding: calc(4px * 8) !important; }

.p-36 { padding: calc(4px * 9) !important; }

.p-40 { padding: calc(4px * 10) !important; }

.p-44 { padding: calc(4px * 11) !important; }

.p-48 { padding: calc(4px * 12) !important; }

.p-52 { padding: calc(4px * 13) !important; }

.p-56 { padding: calc(4px * 14) !important; }

.p-60 { padding: calc(4px * 15) !important; }

.p-64 { padding: calc(4px * 16) !important; }

.p-68 { padding: var(--mico-size-68) !important; }

.p-72 { padding: calc(4px * 18) !important; }

.p-76 { padding: var(--mico-size-76) !important; }

.p-80 { padding: calc(4px * 20) !important; }

.p-84 { padding: var(--mico-size-84) !important; }

.p-88 { padding: var(--mico-size-88) !important; }

.p-92 { padding: var(--mico-size-92) !important; }

.p-96 { padding: calc(4px * 24) !important; }

.p-100 { padding: calc(4px * 25) !important; }

.p-104 { padding: var(--mico-size-104) !important; }

.p-108 { padding: var(--mico-size-108) !important; }

.p-112 { padding: calc(4px * 28) !important; }

.p-116 { padding: var(--mico-size-116) !important; }

.p-120 { padding: var(--mico-size-120) !important; }

.p-124 { padding: var(--mico-size-124) !important; }

.p-128 { padding: calc(4px * 32) !important; }

.p-132 { padding: var(--mico-size-132) !important; }

.p-136 { padding: var(--mico-size-136) !important; }

.p-140 { padding: var(--mico-size-140) !important; }

.p-144 { padding: calc(4px * 36) !important; }

.p-148 { padding: var(--mico-size-148) !important; }

.p-152 { padding: var(--mico-size-152) !important; }

.p-156 { padding: var(--mico-size-156) !important; }

.p-160 { padding: calc(4px * 40) !important; }

.p-164 { padding: var(--mico-size-164) !important; }

.p-168 { padding: var(--mico-size-168) !important; }

.p-172 { padding: var(--mico-size-172) !important; }

.p-176 { padding: calc(4px * 44) !important; }

.p-180 { padding: var(--mico-size-180) !important; }

.p-184 { padding: var(--mico-size-184) !important; }

.p-188 { padding: var(--mico-size-188) !important; }

.p-192 { padding: calc(4px * 48) !important; }

.p-196 { padding: var(--mico-size-196) !important; }

.p-200 { padding: var(--mico-size-200) !important; }

.p-204 { padding: var(--mico-size-204) !important; }

.p-208 { padding: calc(4px * 52) !important; }

.p-212 { padding: var(--mico-size-212) !important; }

.p-216 { padding: var(--mico-size-216) !important; }

.p-220 { padding: var(--mico-size-220) !important; }

.p-224 { padding: calc(4px * 56) !important; }

.p-228 { padding: var(--mico-size-228) !important; }

.p-232 { padding: var(--mico-size-232) !important; }

.p-236 { padding: var(--mico-size-236) !important; }

.p-240 { padding: calc(4px * 60) !important; }

.p-244 { padding: var(--mico-size-244) !important; }

.p-248 { padding: var(--mico-size-248) !important; }

.p-252 { padding: var(--mico-size-252) !important; }

.p-256 { padding: calc(4px * 64) !important; }

.p-260 { padding: calc(4px * 68) !important; }

.p-264 { padding: calc(4px * 72) !important; }

.p-268 { padding: calc(4px * 76) !important; }

.p-272 { padding: calc(4px * 80) !important; }

.p-276 { padding: calc(4px * 84) !important; }

.p-280 { padding: calc(4px * 88) !important; }

.p-284 { padding: calc(4px * 92) !important; }

.p-288 { padding: calc(4px * 96) !important; }

.p-292 { padding: calc(4px * 100) !important; }

.p-296 { padding: calc(4px * 104) !important; }

.p-300 { padding: calc(4px * 108) !important; }

.p-304 { padding: calc(4px * 112) !important; }

.p-308 { padding: calc(4px * 116) !important; }

.p-312 { padding: calc(4px * 120) !important; }

.p-316 { padding: calc(4px * 124) !important; }

.p-320 { padding: calc(4px * 128) !important; }

.p-324 { padding: calc(4px * 132) !important; }

.p-328 { padding: calc(4px * 136) !important; }

.p-332 { padding: calc(4px * 140) !important; }

.p-336 { padding: calc(4px * 144) !important; }

.p-340 { padding: calc(4px * 148) !important; }

.p-344 { padding: calc(4px * 152) !important; }

.p-348 { padding: calc(4px * 156) !important; }

.p-352 { padding: calc(4px * 160) !important; }

.p-356 { padding: calc(4px * 164) !important; }

.p-360 { padding: calc(4px * 168) !important; }

.p-364 { padding: calc(4px * 172) !important; }

.p-368 { padding: calc(4px * 176) !important; }

.p-372 { padding: calc(4px * 180) !important; }

.p-376 { padding: calc(4px * 184) !important; }

.p-380 { padding: calc(4px * 188) !important; }

.p-384 { padding: calc(4px * 192) !important; }

.p-388 { padding: calc(4px * 196) !important; }

.p-392 { padding: calc(4px * 200) !important; }

.p-396 { padding: calc(4px * 204) !important; }

.p-400 { padding: calc(4px * 208) !important; }

/* Padding-top classes from 4 to 100 */

.pt-4 { padding-top: calc(4px * 1) !important; }

.pt-8 { padding-top: calc(4px * 2) !important; }

.pt-12 { padding-top: calc(4px * 3) !important; }

.pt-16 { padding-top: calc(4px * 4) !important; }

.pt-20 { padding-top: calc(4px * 5) !important; }

.pt-24 { padding-top: calc(4px * 6) !important; }

.pt-28 { padding-top: calc(4px * 7) !important; }

.pt-32 { padding-top: calc(4px * 8) !important; }

.pt-36 { padding-top: calc(4px * 9) !important; }

.pt-40 { padding-top: calc(4px * 10) !important; }

.pt-44 { padding-top: calc(4px * 11) !important; }

.pt-48 { padding-top: calc(4px * 12) !important; }

.pt-52 { padding-top: calc(4px * 13) !important; }

.pt-56 { padding-top: calc(4px * 14) !important; }

.pt-60 { padding-top: calc(4px * 15) !important; }

.pt-64 { padding-top: calc(4px * 16) !important; }

.pt-68 { padding-top: var(--mico-size-68) !important; }

.pt-72 { padding-top: calc(4px * 18) !important; }

.pt-76 { padding-top: var(--mico-size-76) !important; }

.pt-80 { padding-top: calc(4px * 20) !important; }

.pt-84 { padding-top: var(--mico-size-84) !important; }

.pt-88 { padding-top: var(--mico-size-88) !important; }

.pt-92 { padding-top: var(--mico-size-92) !important; }

.pt-96 { padding-top: calc(4px * 24) !important; }

.pt-100 { padding-top: calc(4px * 25) !important; }

.pt-104 { padding-top: var(--mico-size-104) !important; }

.pt-108 { padding-top: var(--mico-size-108) !important; }

.pt-112 { padding-top: calc(4px * 28) !important; }

.pt-116 { padding-top: var(--mico-size-116) !important; }

.pt-120 { padding-top: var(--mico-size-120) !important; }

.pt-124 { padding-top: var(--mico-size-124) !important; }

.pt-128 { padding-top: calc(4px * 32) !important; }

.pt-132 { padding-top: var(--mico-size-132) !important; }

.pt-136 { padding-top: var(--mico-size-136) !important; }

.pt-140 { padding-top: var(--mico-size-140) !important; }

.pt-144 { padding-top: calc(4px * 36) !important; }

.pt-148 { padding-top: var(--mico-size-148) !important; }

.pt-152 { padding-top: var(--mico-size-152) !important; }

.pt-156 { padding-top: var(--mico-size-156) !important; }

.pt-160 { padding-top: calc(4px * 40) !important; }

.pt-164 { padding-top: var(--mico-size-164) !important; }

.pt-168 { padding-top: var(--mico-size-168) !important; }

.pt-172 { padding-top: var(--mico-size-172) !important; }

.pt-176 { padding-top: calc(4px * 44) !important; }

.pt-180 { padding-top: var(--mico-size-180) !important; }

.pt-184 { padding-top: var(--mico-size-184) !important; }

.pt-188 { padding-top: var(--mico-size-188) !important; }

.pt-192 { padding-top: calc(4px * 48) !important; }

.pt-196 { padding-top: var(--mico-size-196) !important; }

.pt-200 { padding-top: var(--mico-size-200) !important; }

.pt-204 { padding-top: var(--mico-size-204) !important; }

.pt-208 { padding-top: calc(4px * 52) !important; }

.pt-212 { padding-top: var(--mico-size-212) !important; }

.pt-216 { padding-top: var(--mico-size-216) !important; }

.pt-220 { padding-top: var(--mico-size-220) !important; }

.pt-224 { padding-top: calc(4px * 56) !important; }

.pt-228 { padding-top: var(--mico-size-228) !important; }

.pt-232 { padding-top: var(--mico-size-232) !important; }

.pt-236 { padding-top: var(--mico-size-236) !important; }

.pt-240 { padding-top: calc(4px * 60) !important; }

.pt-244 { padding-top: var(--mico-size-244) !important; }

.pt-248 { padding-top: var(--mico-size-248) !important; }

.pt-252 { padding-top: var(--mico-size-252) !important; }

.pt-256 { padding-top: calc(4px * 64) !important; }

.pt-260 { padding-top: calc(4px * 68) !important; }

.pt-264 { padding-top: calc(4px * 72) !important; }

.pt-268 { padding-top: calc(4px * 76) !important; }

.pt-272 { padding-top: calc(4px * 80) !important; }

.pt-276 { padding-top: calc(4px * 84) !important; }

.pt-280 { padding-top: calc(4px * 88) !important; }

.pt-284 { padding-top: calc(4px * 92) !important; }

.pt-288 { padding-top: calc(4px * 96) !important; }

.pt-292 { padding-top: calc(4px * 100) !important; }

.pt-296 { padding-top: calc(4px * 104) !important; }

.pt-300 { padding-top: calc(4px * 108) !important; }

.pt-304 { padding-top: calc(4px * 112) !important; }

.pt-308 { padding-top: calc(4px * 116) !important; }

.pt-312 { padding-top: calc(4px * 120) !important; }

.pt-316 { padding-top: calc(4px * 124) !important; }

.pt-320 { padding-top: calc(4px * 128) !important; }

.pt-324 { padding-top: calc(4px * 132) !important; }

.pt-328 { padding-top: calc(4px * 136) !important; }

.pt-332 { padding-top: calc(4px * 140) !important; }

.pt-336 { padding-top: calc(4px * 144) !important; }

.pt-340 { padding-top: calc(4px * 148) !important; }

.pt-344 { padding-top: calc(4px * 152) !important; }

.pt-348 { padding-top: calc(4px * 156) !important; }

.pt-352 { padding-top: calc(4px * 160) !important; }

.pt-356 { padding-top: calc(4px * 164) !important; }

.pt-360 { padding-top: calc(4px * 168) !important; }

.pt-364 { padding-top: calc(4px * 172) !important; }

.pt-368 { padding-top: calc(4px * 176) !important; }

.pt-372 { padding-top: calc(4px * 180) !important; }

.pt-376 { padding-top: calc(4px * 184) !important; }

.pt-380 { padding-top: calc(4px * 188) !important; }

.pt-384 { padding-top: calc(4px * 192) !important; }

.pt-388 { padding-top: calc(4px * 196) !important; }

.pt-392 { padding-top: calc(4px * 200) !important; }

.pt-396 { padding-top: calc(4px * 204) !important; }

.pt-400 { padding-top: calc(4px * 208) !important; }

/* Padding-bottom classes from 4 to 100 */

.pb-4 { padding-bottom: calc(4px * 1) !important; }

.pb-8 { padding-bottom: calc(4px * 2) !important; }

.pb-12 { padding-bottom: calc(4px * 3) !important; }

.pb-16 { padding-bottom: calc(4px * 4) !important; }

.pb-20 { padding-bottom: calc(4px * 5) !important; }

.pb-24 { padding-bottom: calc(4px * 6) !important; }

.pb-28 { padding-bottom: calc(4px * 7) !important; }

.pb-32 { padding-bottom: calc(4px * 8) !important; }

.pb-36 { padding-bottom: calc(4px * 9) !important; }

.pb-40 { padding-bottom: calc(4px * 10) !important; }

.pb-44 { padding-bottom: calc(4px * 11) !important; }

.pb-48 { padding-bottom: calc(4px * 12) !important; }

.pb-52 { padding-bottom: calc(4px * 13) !important; }

.pb-56 { padding-bottom: calc(4px * 14) !important; }

.pb-60 { padding-bottom: calc(4px * 15) !important; }

.pb-64 { padding-bottom: calc(4px * 16) !important; }

.pb-68 { padding-bottom: var(--mico-size-68) !important; }

.pb-72 { padding-bottom: calc(4px * 18) !important; }

.pb-76 { padding-bottom: var(--mico-size-76) !important; }

.pb-80 { padding-bottom: calc(4px * 20) !important; }

.pb-84 { padding-bottom: var(--mico-size-84) !important; }

.pb-88 { padding-bottom: var(--mico-size-88) !important; }

.pb-92 { padding-bottom: var(--mico-size-92) !important; }

.pb-96 { padding-bottom: calc(4px * 24) !important; }

.pb-100 { padding-bottom: calc(4px * 25) !important; }

.pb-104 { padding-bottom: var(--mico-size-104) !important; }

.pb-108 { padding-bottom: var(--mico-size-108) !important; }

.pb-112 { padding-bottom: calc(4px * 28) !important; }

.pb-116 { padding-bottom: var(--mico-size-116) !important; }

.pb-120 { padding-bottom: var(--mico-size-120) !important; }

.pb-124 { padding-bottom: var(--mico-size-124) !important; }

.pb-128 { padding-bottom: calc(4px * 32) !important; }

.pb-132 { padding-bottom: var(--mico-size-132) !important; }

.pb-136 { padding-bottom: var(--mico-size-136) !important; }

.pb-140 { padding-bottom: var(--mico-size-140) !important; }

.pb-144 { padding-bottom: calc(4px * 36) !important; }

.pb-148 { padding-bottom: var(--mico-size-148) !important; }

.pb-152 { padding-bottom: var(--mico-size-152) !important; }

.pb-156 { padding-bottom: var(--mico-size-156) !important; }

.pb-160 { padding-bottom: calc(4px * 40) !important; }

.pb-164 { padding-bottom: var(--mico-size-164) !important; }

.pb-168 { padding-bottom: var(--mico-size-168) !important; }

.pb-172 { padding-bottom: var(--mico-size-172) !important; }

.pb-176 { padding-bottom: calc(4px * 44) !important; }

.pb-180 { padding-bottom: var(--mico-size-180) !important; }

.pb-184 { padding-bottom: var(--mico-size-184) !important; }

.pb-188 { padding-bottom: var(--mico-size-188) !important; }

.pb-192 { padding-bottom: calc(4px * 48) !important; }

.pb-196 { padding-bottom: var(--mico-size-196) !important; }

.pb-200 { padding-bottom: var(--mico-size-200) !important; }

.pb-204 { padding-bottom: var(--mico-size-204) !important; }

.pb-208 { padding-bottom: calc(4px * 52) !important; }

.pb-212 { padding-bottom: var(--mico-size-212) !important; }

.pb-216 { padding-bottom: var(--mico-size-216) !important; }

.pb-220 { padding-bottom: var(--mico-size-220) !important; }

.pb-224 { padding-bottom: calc(4px * 56) !important; }

.pb-228 { padding-bottom: var(--mico-size-228) !important; }

.pb-232 { padding-bottom: var(--mico-size-232) !important; }

.pb-236 { padding-bottom: var(--mico-size-236) !important; }

.pb-240 { padding-bottom: calc(4px * 60) !important; }

.pb-244 { padding-bottom: var(--mico-size-244) !important; }

.pb-248 { padding-bottom: var(--mico-size-248) !important; }

.pb-252 { padding-bottom: var(--mico-size-252) !important; }

.pb-256 { padding-bottom: calc(4px * 64) !important; }

.pb-260 { padding-bottom: calc(4px * 68) !important; }

.pb-264 { padding-bottom: calc(4px * 72) !important; }

.pb-268 { padding-bottom: calc(4px * 76) !important; }

.pb-272 { padding-bottom: calc(4px * 80) !important; }

.pb-276 { padding-bottom: calc(4px * 84) !important; }

.pb-280 { padding-bottom: calc(4px * 88) !important; }

.pb-284 { padding-bottom: calc(4px * 92) !important; }

.pb-288 { padding-bottom: calc(4px * 96) !important; }

.pb-292 { padding-bottom: calc(4px * 100) !important; }

.pb-296 { padding-bottom: calc(4px * 104) !important; }

.pb-300 { padding-bottom: calc(4px * 108) !important; }

.pb-304 { padding-bottom: calc(4px * 112) !important; }

.pb-308 { padding-bottom: calc(4px * 116) !important; }

.pb-312 { padding-bottom: calc(4px * 120) !important; }

.pb-316 { padding-bottom: calc(4px * 124) !important; }

.pb-320 { padding-bottom: calc(4px * 128) !important; }

.pb-324 { padding-bottom: calc(4px * 132) !important; }

.pb-328 { padding-bottom: calc(4px * 136) !important; }

.pb-332 { padding-bottom: calc(4px * 140) !important; }

.pb-336 { padding-bottom: calc(4px * 144) !important; }

.pb-340 { padding-bottom: calc(4px * 148) !important; }

.pb-344 { padding-bottom: calc(4px * 152) !important; }

.pb-348 { padding-bottom: calc(4px * 156) !important; }

.pb-352 { padding-bottom: calc(4px * 160) !important; }

.pb-356 { padding-bottom: calc(4px * 164) !important; }

.pb-360 { padding-bottom: calc(4px * 168) !important; }

.pb-364 { padding-bottom: calc(4px * 172) !important; }

.pb-368 { padding-bottom: calc(4px * 176) !important; }

.pb-372 { padding-bottom: calc(4px * 180) !important; }

.pb-376 { padding-bottom: calc(4px * 184) !important; }

.pb-380 { padding-bottom: calc(4px * 188) !important; }

.pb-384 { padding-bottom: calc(4px * 192) !important; }

.pb-388 { padding-bottom: calc(4px * 196) !important; }

.pb-392 { padding-bottom: calc(4px * 200) !important; }

.pb-396 { padding-bottom: calc(4px * 204) !important; }

.pb-400 { padding-bottom: calc(4px * 208) !important; }

/* Padding-left classes from 4 to 100 */

.pl-4 { padding-left: calc(4px * 1) !important; }

.pl-8 { padding-left: calc(4px * 2) !important; }

.pl-12 { padding-left: calc(4px * 3) !important; }

.pl-16 { padding-left: calc(4px * 4) !important; }

.pl-20 { padding-left: calc(4px * 5) !important; }

.pl-24 { padding-left: calc(4px * 6) !important; }

.pl-28 { padding-left: calc(4px * 7) !important; }

.pl-32 { padding-left: calc(4px * 8) !important; }

.pl-36 { padding-left: calc(4px * 9) !important; }

.pl-40 { padding-left: calc(4px * 10) !important; }

.pl-44 { padding-left: calc(4px * 11) !important; }

.pl-48 { padding-left: calc(4px * 12) !important; }

.pl-52 { padding-left: calc(4px * 13) !important; }

.pl-56 { padding-left: calc(4px * 14) !important; }

.pl-60 { padding-left: calc(4px * 15) !important; }

.pl-64 { padding-left: calc(4px * 16) !important; }

.pl-68 { padding-left: var(--mico-size-68) !important; }

.pl-72 { padding-left: calc(4px * 18) !important; }

.pl-76 { padding-left: var(--mico-size-) !important; }

.pl-72 { padding-left: calc(4px * 18) !important; }

.pl-76 { padding-left: var(--mico-size-76) !important; }

.pl-80 { padding-left: calc(4px * 20) !important; }

.pl-84 { padding-left: var(--mico-size-84) !important; }

.pl-88 { padding-left: var(--mico-size-88) !important; }

.pl-92 { padding-left: var(--mico-size-92) !important; }

.pl-96 { padding-left: calc(4px * 24) !important; }

.pl-100 { padding-left: calc(4px * 25) !important; }

.pl-104 { padding-left: var(--mico-size-104) !important; }

.pl-108 { padding-left: var(--mico-size-108) !important; }

.pl-112 { padding-left: calc(4px * 28) !important; }

.pl-116 { padding-left: var(--mico-size-116) !important; }

.pl-120 { padding-left: var(--mico-size-120) !important; }

.pl-124 { padding-left: var(--mico-size-124) !important; }

.pl-128 { padding-left: calc(4px * 32) !important; }

.pl-132 { padding-left: var(--mico-size-132) !important; }

.pl-136 { padding-left: var(--mico-size-136) !important; }

.pl-140 { padding-left: var(--mico-size-140) !important; }

.pl-144 { padding-left: calc(4px * 36) !important; }

.pl-148 { padding-left: var(--mico-size-148) !important; }

.pl-152 { padding-left: var(--mico-size-152) !important; }

.pl-156 { padding-left: var(--mico-size-156) !important; }

.pl-160 { padding-left: calc(4px * 40) !important; }

.pl-164 { padding-left: var(--mico-size-164) !important; }

.pl-168 { padding-left: var(--mico-size-168) !important; }

.pl-172 { padding-left: var(--mico-size-172) !important; }

.pl-176 { padding-left: calc(4px * 44) !important; }

.pl-180 { padding-left: var(--mico-size-180) !important; }

.pl-184 { padding-left: var(--mico-size-184) !important; }

.pl-188 { padding-left: var(--mico-size-188) !important; }

.pl-192 { padding-left: calc(4px * 48) !important; }

.pl-196 { padding-left: var(--mico-size-196) !important; }

.pl-200 { padding-left: var(--mico-size-200) !important; }

.pl-204 { padding-left: var(--mico-size-204) !important; }

.pl-208 { padding-left: calc(4px * 52) !important; }

.pl-212 { padding-left: var(--mico-size-212) !important; }

.pl-216 { padding-left: var(--mico-size-216) !important; }

.pl-220 { padding-left: var(--mico-size-220) !important; }

.pl-224 { padding-left: calc(4px * 56) !important; }

.pl-228 { padding-left: var(--mico-size-228) !important; }

.pl-232 { padding-left: var(--mico-size-232) !important; }

.pl-236 { padding-left: var(--mico-size-236) !important; }

.pl-240 { padding-left: calc(4px * 60) !important; }

.pl-244 { padding-left: var(--mico-size-244) !important; }

.pl-248 { padding-left: var(--mico-size-248) !important; }

.pl-252 { padding-left: var(--mico-size-252) !important; }

.pl-256 { padding-left: calc(4px * 64) !important; }

.pl-260 { padding-left: calc(4px * 68) !important; }

.pl-264 { padding-left: calc(4px * 72) !important; }

.pl-268 { padding-left: calc(4px * 76) !important; }

.pl-272 { padding-left: calc(4px * 80) !important; }

.pl-276 { padding-left: calc(4px * 84) !important; }

.pl-280 { padding-left: calc(4px * 88) !important; }

.pl-284 { padding-left: calc(4px * 92) !important; }

.pl-288 { padding-left: calc(4px * 96) !important; }

.pl-292 { padding-left: calc(4px * 100) !important; }

.pl-296 { padding-left: calc(4px * 104) !important; }

.pl-300 { padding-left: calc(4px * 108) !important; }

.pl-304 { padding-left: calc(4px * 112) !important; }

.pl-308 { padding-left: calc(4px * 116) !important; }

.pl-312 { padding-left: calc(4px * 120) !important; }

.pl-316 { padding-left: calc(4px * 124) !important; }

.pl-320 { padding-left: calc(4px * 128) !important; }

.pl-324 { padding-left: calc(4px * 132) !important; }

.pl-328 { padding-left: calc(4px * 136) !important; }

.pl-332 { padding-left: calc(4px * 140) !important; }

.pl-336 { padding-left: calc(4px * 144) !important; }

.pl-340 { padding-left: calc(4px * 148) !important; }

.pl-344 { padding-left: calc(4px * 152) !important; }

.pl-348 { padding-left: calc(4px * 156) !important; }

.pl-352 { padding-left: calc(4px * 160) !important; }

.pl-356 { padding-left: calc(4px * 164) !important; }

.pl-360 { padding-left: calc(4px * 168) !important; }

.pl-364 { padding-left: calc(4px * 172) !important; }

.pl-368 { padding-left: calc(4px * 176) !important; }

.pl-372 { padding-left: calc(4px * 180) !important; }

.pl-376 { padding-left: calc(4px * 184) !important; }

.pl-380 { padding-left: calc(4px * 188) !important; }

.pl-384 { padding-left: calc(4px * 192) !important; }

.pl-388 { padding-left: calc(4px * 196) !important; }

.pl-392 { padding-left: calc(4px * 200) !important; }

.pl-396 { padding-left: calc(4px * 204) !important; }

.pl-400 { padding-left: calc(4px * 208) !important; }

/* Padding-right classes from 4 to 100 */

.pr-4 { padding-right: calc(4px * 1) !important; }

.pr-8 { padding-right: calc(4px * 2) !important; }

.pr-12 { padding-right: calc(4px * 3) !important; }

.pr-16 { padding-right: calc(4px * 4) !important; }

.pr-20 { padding-right: calc(4px * 5) !important; }

.pr-24 { padding-right: calc(4px * 6) !important; }

.pr-28 { padding-right: calc(4px * 7) !important; }

.pr-32 { padding-right: calc(4px * 8) !important; }

.pr-36 { padding-right: calc(4px * 9) !important; }

.pr-40 { padding-right: calc(4px * 10) !important; }

.pr-44 { padding-right: calc(4px * 11) !important; }

.pr-48 { padding-right: calc(4px * 12) !important; }

.pr-52 { padding-right: calc(4px * 13) !important; }

.pr-56 { padding-right: calc(4px * 14) !important; }

.pr-60 { padding-right: calc(4px * 15) !important; }

.pr-64 { padding-right: calc(4px * 16) !important; }

.pr-68 { padding-right: var(--mico-size-68) !important; }

.pr-72 { padding-right: calc(4px * 18) !important; }

.pr-76 { padding-right: var(--mico-size-76) !important; }

.pr-80 { padding-right: calc(4px * 20) !important; }

.pr-84 { padding-right: var(--mico-size-84) !important; }

.pr-88 { padding-right: var(--mico-size-88) !important; }

.pr-92 { padding-right: var(--mico-size-92) !important; }

.pr-96 { padding-right: calc(4px * 24) !important; }

.pr-100 { padding-right: calc(4px * 25) !important; }

.pr-104 { padding-right: var(--mico-size-104) !important; }

.pr-108 { padding-right: var(--mico-size-108) !important; }

.pr-112 { padding-right: calc(4px * 28) !important; }

.pr-116 { padding-right: var(--mico-size-116) !important; }

.pr-120 { padding-right: var(--mico-size-120) !important; }

.pr-124 { padding-right: var(--mico-size-124) !important; }

.pr-128 { padding-right: calc(4px * 32) !important; }

.pr-132 { padding-right: var(--mico-size-132) !important; }

.pr-136 { padding-right: var(--mico-size-136) !important; }

.pr-140 { padding-right: var(--mico-size-140) !important; }

.pr-144 { padding-right: calc(4px * 36) !important; }

.pr-148 { padding-right: var(--mico-size-148) !important; }

.pr-152 { padding-right: var(--mico-size-152) !important; }

.pr-156 { padding-right: var(--mico-size-156) !important; }

.pr-160 { padding-right: calc(4px * 40) !important; }

.pr-164 { padding-right: var(--mico-size-164) !important; }

.pr-168 { padding-right: var(--mico-size-168) !important; }

.pr-172 { padding-right: var(--mico-size-172) !important; }

.pr-176 { padding-right: calc(4px * 44) !important; }

.pr-180 { padding-right: var(--mico-size-180) !important; }

.pr-184 { padding-right: var(--mico-size-184) !important; }

.pr-188 { padding-right: var(--mico-size-188) !important; }

.pr-192 { padding-right: calc(4px * 48) !important; }

.pr-196 { padding-right: var(--mico-size-196) !important; }

.pr-200 { padding-right: var(--mico-size-200) !important; }

.pr-204 { padding-right: var(--mico-size-204) !important; }

.pr-208 { padding-right: calc(4px * 52) !important; }

.pr-212 { padding-right: var(--mico-size-212) !important; }

.pr-216 { padding-right: var(--mico-size-216) !important; }

.pr-220 { padding-right: var(--mico-size-220) !important; }

.pr-224 { padding-right: calc(4px * 56) !important; }

.pr-228 { padding-right: var(--mico-size-228) !important; }

.pr-232 { padding-right: var(--mico-size-232) !important; }

.pr-236 { padding-right: var(--mico-size-236) !important; }

.pr-240 { padding-right: calc(4px * 60) !important; }

.pr-244 { padding-right: var(--mico-size-244) !important; }

.pr-248 { padding-right: var(--mico-size-248) !important; }

.pr-252 { padding-right: var(--mico-size-252) !important; }

.pr-256 { padding-right: calc(4px * 64) !important; }

.pr-260 { padding-right: calc(4px * 68) !important; }

.pr-264 { padding-right: calc(4px * 72) !important; }

.pr-268 { padding-right: calc(4px * 76) !important; }

.pr-272 { padding-right: calc(4px * 80) !important; }

.pr-276 { padding-right: calc(4px * 84) !important; }

.pr-280 { padding-right: calc(4px * 88) !important; }

.pr-284 { padding-right: calc(4px * 92) !important; }

.pr-288 { padding-right: calc(4px * 96) !important; }

.pr-292 { padding-right: calc(4px * 100) !important; }

.pr-296 { padding-right: calc(4px * 104) !important; }

.pr-300 { padding-right: calc(4px * 108) !important; }

.pr-304 { padding-right: calc(4px * 112) !important; }

.pr-308 { padding-right: calc(4px * 116) !important; }

.pr-312 { padding-right: calc(4px * 120) !important; }

.pr-316 { padding-right: calc(4px * 124) !important; }

.pr-320 { padding-right: calc(4px * 128) !important; }

.pr-324 { padding-right: calc(4px * 132) !important; }

.pr-328 { padding-right: calc(4px * 136) !important; }

.pr-332 { padding-right: calc(4px * 140) !important; }

.pr-336 { padding-right: calc(4px * 144) !important; }

.pr-340 { padding-right: calc(4px * 148) !important; }

.pr-344 { padding-right: calc(4px * 152) !important; }

.pr-348 { padding-right: calc(4px * 156) !important; }

.pr-352 { padding-right: calc(4px * 160) !important; }

.pr-356 { padding-right: calc(4px * 164) !important; }

.pr-360 { padding-right: calc(4px * 168) !important; }

.pr-364 { padding-right: calc(4px * 172) !important; }

.pr-368 { padding-right: calc(4px * 176) !important; }

.pr-372 { padding-right: calc(4px * 180) !important; }

.pr-376 { padding-right: calc(4px * 184) !important; }

.pr-380 { padding-right: calc(4px * 188) !important; }

.pr-384 { padding-right: calc(4px * 192) !important; }

.pr-388 { padding-right: calc(4px * 196) !important; }

.pr-392 { padding-right: calc(4px * 200) !important; }

.pr-396 { padding-right: calc(4px * 204) !important; }

.pr-400 { padding-right: calc(4px * 208) !important; }

/* Padding-inline classes from 4 to 100 */

.px-4 { padding-left: calc(4px * 1) !important; padding-right: calc(4px * 1) !important; }

.px-8 { padding-left: calc(4px * 2) !important; padding-right: calc(4px * 2) !important; }

.px-12 { padding-left: calc(4px * 3) !important; padding-right: calc(4px * 3) !important; }

.px-16 { padding-left: calc(4px * 4) !important; padding-right: calc(4px * 4) !important; }

.px-20 { padding-left: calc(4px * 5) !important; padding-right: calc(4px * 5) !important; }

.px-24 { padding-left: calc(4px * 6) !important; padding-right: calc(4px * 6) !important; }

.px-28 { padding-left: calc(4px * 7) !important; padding-right: calc(4px * 7) !important; }

.px-32 { padding-left: calc(4px * 8) !important; padding-right: calc(4px * 8) !important; }

.px-36 { padding-left: calc(4px * 9) !important; padding-right: calc(4px * 9) !important; }

.px-40 { padding-left: calc(4px * 10) !important; padding-right: calc(4px * 10) !important; }

.px-44 { padding-left: calc(4px * 11) !important; padding-right: calc(4px * 11) !important; }

.px-48 { padding-left: calc(4px * 12) !important; padding-right: calc(4px * 12) !important; }

.px-52 { padding-left: calc(4px * 13) !important; padding-right: calc(4px * 13) !important; }

.px-56 { padding-left: calc(4px * 14) !important; padding-right: calc(4px * 14) !important; }

.px-60 { padding-left: calc(4px * 15) !important; padding-right: calc(4px * 15) !important; }

.px-64 { padding-left: calc(4px * 16) !important; padding-right: calc(4px * 16) !important; }

.px-68 { padding-left: var(--mico-size-68) !important; padding-right: var(--mico-size-68) !important; }

.px-72 { padding-left: calc(4px * 18) !important; padding-right: calc(4px * 18) !important; }

.px-76 { padding-left: var(--mico-size-76) !important; padding-right: var(--mico-size-76) !important; }

.px-80 { padding-left: calc(4px * 20) !important; padding-right: calc(4px * 20) !important; }

.px-84 { padding-left: var(--mico-size-84) !important; padding-right: var(--mico-size-84) !important; }

.px-88 { padding-left: var(--mico-size-88) !important; padding-right: var(--mico-size-88) !important; }

.px-92 { padding-left: var(--mico-size-92) !important; padding-right: var(--mico-size-92) !important; }

.px-96 { padding-left: calc(4px * 24) !important; padding-right: calc(4px * 24) !important; }

.px-100 { padding-left: calc(4px * 25) !important; padding-right: calc(4px * 25) !important; }

.px-104 { padding-left: var(--mico-size-104) !important; padding-right: var(--mico-size-104) !important; }

.px-108 { padding-left: var(--mico-size-108) !important; padding-right: var(--mico-size-108) !important; }

.px-112 { padding-left: calc(4px * 28) !important; padding-right: calc(4px * 28) !important; }

.px-116 { padding-left: var(--mico-size-116) !important; padding-right: var(--mico-size-116) !important; }

.px-120 { padding-left: var(--mico-size-120) !important; padding-right: var(--mico-size-120) !important; }

.px-124 { padding-left: var(--mico-size-124) !important; padding-right: var(--mico-size-124) !important; }

.px-128 { padding-left: calc(4px * 32) !important; padding-right: calc(4px * 32) !important; }

.px-132 { padding-left: var(--mico-size-132) !important; padding-right: var(--mico-size-132) !important; }

.px-136 { padding-left: var(--mico-size-136) !important; padding-right: var(--mico-size-136) !important; }

.px-140 { padding-left: var(--mico-size-140) !important; padding-right: var(--mico-size-140) !important; }

.px-144 { padding-left: calc(4px * 36) !important; padding-right: calc(4px * 36) !important; }

.px-148 { padding-left: var(--mico-size-148) !important; padding-right: var(--mico-size-148) !important; }

.px-152 { padding-left: var(--mico-size-152) !important; padding-right: var(--mico-size-152) !important; }

.px-156 { padding-left: var(--mico-size-156) !important; padding-right: var(--mico-size-156) !important; }

.px-160 { padding-left: calc(4px * 40) !important; padding-right: calc(4px * 40) !important; }

.px-164 { padding-left: var(--mico-size-164) !important; padding-right: var(--mico-size-164) !important; }

.px-168 { padding-left: var(--mico-size-168) !important; padding-right: var(--mico-size-168) !important; }

.px-172 { padding-left: var(--mico-size-172) !important; padding-right: var(--mico-size-172) !important; }

.px-176 { padding-left: calc(4px * 44) !important; padding-right: calc(4px * 44) !important; }

.px-180 { padding-left: var(--mico-size-180) !important; padding-right: var(--mico-size-180) !important; }

.px-184 { padding-left: var(--mico-size-184) !important; padding-right: var(--mico-size-184) !important; }

.px-188 { padding-left: var(--mico-size-188) !important; padding-right: var(--mico-size-188) !important; }

.px-192 { padding-left: calc(4px * 48) !important; padding-right: calc(4px * 48) !important; }

.px-196 { padding-left: var(--mico-size-196) !important; padding-right: var(--mico-size-196) !important; }

.px-200 { padding-left: var(--mico-size-200) !important; padding-right: var(--mico-size-200) !important; }

.px-204 { padding-left: var(--mico-size-204) !important; padding-right: var(--mico-size-204) !important; }

.px-208 { padding-left: calc(4px * 52) !important; padding-right: calc(4px * 52) !important; }

.px-212 { padding-left: var(--mico-size-212) !important; padding-right: var(--mico-size-212) !important; }

.px-216 { padding-left: var(--mico-size-216) !important; padding-right: var(--mico-size-216) !important; }

.px-220 { padding-left: var(--mico-size-220) !important; padding-right: var(--mico-size-220) !important; }

.px-224 { padding-left: calc(4px * 56) !important; padding-right: calc(4px * 56) !important; }

.px-228 { padding-left: var(--mico-size-228) !important; padding-right: var(--mico-size-228) !important; }

.px-232 { padding-left: var(--mico-size-232) !important; padding-right: var(--mico-size-232) !important; }

.px-236 { padding-left: var(--mico-size-236) !important; padding-right: var(--mico-size-236) !important; }

.px-240 { padding-left: calc(4px * 60) !important; padding-right: calc(4px * 60) !important; }

.px-244 { padding-left: var(--mico-size-244) !important; padding-right: var(--mico-size-244) !important; }

.px-248 { padding-left: var(--mico-size-248) !important; padding-right: var(--mico-size-248) !important; }

.px-252 { padding-left: var(--mico-size-252) !important; padding-right: var(--mico-size-252) !important; }

.px-256 { padding-left: calc(4px * 64) !important; padding-right: calc(4px * 64) !important; }

.px-260 { padding-left: calc(4px * 68) !important; padding-right: calc(4px * 68) !important; }

.px-264 { padding-left: calc(4px * 72) !important; padding-right: calc(4px * 72) !important; }

.px-268 { padding-left: calc(4px * 76) !important; padding-right: calc(4px * 76) !important; }

.px-272 { padding-left: calc(4px * 80) !important; padding-right: calc(4px * 80) !important; }

.px-276 { padding-left: calc(4px * 84) !important; padding-right: calc(4px * 84) !important; }

.px-280 { padding-left: calc(4px * 88) !important; padding-right: calc(4px * 88) !important; }

.px-284 { padding-left: calc(4px * 92) !important; padding-right: calc(4px * 92) !important; }

.px-288 { padding-left: calc(4px * 96) !important; padding-right: calc(4px * 96) !important; }

.px-292 { padding-left: calc(4px * 100) !important; padding-right: calc(4px * 100) !important; }

.px-296 { padding-left: calc(4px * 104) !important; padding-right: calc(4px * 104) !important; }

.px-300 { padding-left: calc(4px * 108) !important; padding-right: calc(4px * 108) !important; }

.px-304 { padding-left: calc(4px * 112) !important; padding-right: calc(4px * 112) !important; }

.px-308 { padding-left: calc(4px * 116) !important; padding-right: calc(4px * 116) !important; }

.px-312 { padding-left: calc(4px * 120) !important; padding-right: calc(4px * 120) !important; }

.px-316 { padding-left: calc(4px * 124) !important; padding-right: calc(4px * 124) !important; }

.px-320 { padding-left: calc(4px * 128) !important; padding-right: calc(4px * 128) !important; }

.px-324 { padding-left: calc(4px * 132) !important; padding-right: calc(4px * 132) !important; }

.px-328 { padding-left: calc(4px * 136) !important; padding-right: calc(4px * 136) !important; }

.px-332 { padding-left: calc(4px * 140) !important; padding-right: calc(4px * 140) !important; }

.px-336 { padding-left: calc(4px * 144) !important; padding-right: calc(4px * 144) !important; }

.px-340 { padding-left: calc(4px * 148) !important; padding-right: calc(4px * 148) !important; }

.px-344 { padding-left: calc(4px * 152) !important; padding-right: calc(4px * 152) !important; }

.px-348 { padding-left: calc(4px * 156) !important; padding-right: calc(4px * 156) !important; }

.px-352 { padding-left: calc(4px * 160) !important; padding-right: calc(4px * 160) !important; }

.px-356 { padding-left: calc(4px * 164) !important; padding-right: calc(4px * 164) !important; }

.px-360 { padding-left: calc(4px * 168) !important; padding-right: calc(4px * 168) !important; }

.px-364 { padding-left: calc(4px * 172) !important; padding-right: calc(4px * 172) !important; }

.px-368 { padding-left: calc(4px * 176) !important; padding-right: calc(4px * 176) !important; }

.px-372 { padding-left: calc(4px * 180) !important; padding-right: calc(4px * 180) !important; }

.px-376 { padding-left: calc(4px * 184) !important; padding-right: calc(4px * 184) !important; }

.px-380 { padding-left: calc(4px * 188) !important; padding-right: calc(4px * 188) !important; }

.px-384 { padding-left: calc(4px * 192) !important; padding-right: calc(4px * 192) !important; }

.px-388 { padding-left: calc(4px * 196) !important; padding-right: calc(4px * 196) !important; }

.px-392 { padding-left: calc(4px * 200) !important; padding-right: calc(4px * 200) !important; }

.px-396 { padding-left: calc(4px * 204) !important; padding-right: calc(4px * 204) !important; }

.px-400 { padding-left: calc(4px * 208) !important; padding-right: calc(4px * 208) !important; }

/* Padding-block classes from 4 to 100 */

.py-4 { padding-top: calc(4px * 1) !important; padding-bottom: calc(4px * 1) !important; }

.py-8 { padding-top: calc(4px * 2) !important; padding-bottom: calc(4px * 2) !important; }

.py-12 { padding-top: calc(4px * 3) !important; padding-bottom: calc(4px * 3) !important; }

.py-16 { padding-top: calc(4px * 4) !important; padding-bottom: calc(4px * 4) !important; }

.py-20 { padding-top: calc(4px * 5) !important; padding-bottom: calc(4px * 5) !important; }

.py-24 { padding-top: calc(4px * 6) !important; padding-bottom: calc(4px * 6) !important; }

.py-28 { padding-top: calc(4px * 7) !important; padding-bottom: calc(4px * 7) !important; }

.py-32 { padding-top: calc(4px * 8) !important; padding-bottom: calc(4px * 8) !important; }

.py-36 { padding-top: calc(4px * 9) !important; padding-bottom: calc(4px * 9) !important; }

.py-40 { padding-top: calc(4px * 10) !important; padding-bottom: calc(4px * 10) !important; }

.py-44 { padding-top: calc(4px * 11) !important; padding-bottom: calc(4px * 11) !important; }

.py-48 { padding-top: calc(4px * 12) !important; padding-bottom: calc(4px * 12) !important; }

.py-52 { padding-top: calc(4px * 13) !important; padding-bottom: calc(4px * 13) !important; }

.py-56 { padding-top: calc(4px * 14) !important; padding-bottom: calc(4px * 14) !important; }

.py-60 { padding-top: calc(4px * 15) !important; padding-bottom: calc(4px * 15) !important; }

.py-64 { padding-top: calc(4px * 16) !important; padding-bottom: calc(4px * 16) !important; }

.py-68 { padding-top: var(--mico-size-68) !important; padding-bottom: var(--mico-size-68) !important; }

.py-72 { padding-top: calc(4px * 18) !important; padding-bottom: calc(4px * 18) !important; }

.py-76 { padding-top: var(--mico-size-76) !important; padding-bottom: var(--mico-size-76) !important; }

.py-80 { padding-top: calc(4px * 20) !important; padding-bottom: calc(4px * 20) !important; }

.py-84 { padding-top: var(--mico-size-84) !important; padding-bottom: var(--mico-size-84) !important; }

.py-88 { padding-top: var(--mico-size-88) !important; padding-bottom: var(--mico-size-88) !important; }

.py-92 { padding-top: var(--mico-size-92) !important; padding-bottom: var(--mico-size-92) !important; }

.py-96 { padding-top: calc(4px * 24) !important; padding-bottom: calc(4px * 24) !important; }

.py-100 { padding-top: calc(4px * 25) !important; padding-bottom: calc(4px * 25) !important; }

.py-104 { padding-top: var(--mico-size-104) !important; padding-bottom: var(--mico-size-104) !important; }

.py-108 { padding-top: var(--mico-size-108) !important; padding-bottom: var(--mico-size-108) !important; }

.py-112 { padding-top: calc(4px * 28) !important; padding-bottom: calc(4px * 28) !important; }

.py-116 { padding-top: var(--mico-size-116) !important; padding-bottom: var(--mico-size-116) !important; }

.py-120 { padding-top: var(--mico-size-120) !important; padding-bottom: var(--mico-size-120) !important; }

.py-124 { padding-top: var(--mico-size-124) !important; padding-bottom: var(--mico-size-124) !important; }

.py-128 { padding-top: calc(4px * 32) !important; padding-bottom: calc(4px * 32) !important; }

.py-132 { padding-top: var(--mico-size-132) !important; padding-bottom: var(--mico-size-132) !important; }

.py-136 { padding-top: var(--mico-size-136) !important; padding-bottom: var(--mico-size-136) !important; }

.py-140 { padding-top: var(--mico-size-140) !important; padding-bottom: var(--mico-size-140) !important; }

.py-144 { padding-top: calc(4px * 36) !important; padding-bottom: calc(4px * 36) !important; }

.py-148 { padding-top: var(--mico-size-148) !important; padding-bottom: var(--mico-size-148) !important; }

.py-152 { padding-top: var(--mico-size-152) !important; padding-bottom: var(--mico-size-152) !important; }

.py-156 { padding-top: var(--mico-size-156) !important; padding-bottom: var(--mico-size-156) !important; }

.py-160 { padding-top: calc(4px * 40) !important; padding-bottom: calc(4px * 40) !important; }

.py-164 { padding-top: var(--mico-size-164) !important; padding-bottom: var(--mico-size-164) !important; }

.py-168 { padding-top: var(--mico-size-168) !important; padding-bottom: var(--mico-size-168) !important; }

.py-172 { padding-top: var(--mico-size-172) !important; padding-bottom: var(--mico-size-172) !important; }

.py-176 { padding-top: calc(4px * 44) !important; padding-bottom: calc(4px * 44) !important; }

.py-180 { padding-top: var(--mico-size-180) !important; padding-bottom: var(--mico-size-180) !important; }

.py-184 { padding-top: var(--mico-size-184) !important; padding-bottom: var(--mico-size-184) !important; }

.py-188 { padding-top: var(--mico-size-188) !important; padding-bottom: var(--mico-size-188) !important; }

.py-192 { padding-top: calc(4px * 48) !important; padding-bottom: calc(4px * 48) !important; }

.py-196 { padding-top: var(--mico-size-196) !important; padding-bottom: var(--mico-size-196) !important; }

.py-200 { padding-top: var(--mico-size-200) !important; padding-bottom: var(--mico-size-200) !important; }

.py-204 { padding-top: var(--mico-size-204) !important; padding-bottom: var(--mico-size-204) !important; }

.py-208 { padding-top: calc(4px * 52) !important; padding-bottom: calc(4px * 52) !important; }

.py-212 { padding-top: var(--mico-size-212) !important; padding-bottom: var(--mico-size-212) !important; }

.py-216 { padding-top: var(--mico-size-216) !important; padding-bottom: var(--mico-size-216) !important; }

.py-220 { padding-top: var(--mico-size-220) !important; padding-bottom: var(--mico-size-220) !important; }

.py-224 { padding-top: calc(4px * 56) !important; padding-bottom: calc(4px * 56) !important; }

.py-228 { padding-top: var(--mico-size-228) !important; padding-bottom: var(--mico-size-228) !important; }

.py-232 { padding-top: var(--mico-size-232) !important; padding-bottom: var(--mico-size-232) !important; }

.py-236 { padding-top: var(--mico-size-236) !important; padding-bottom: var(--mico-size-236) !important; }

.py-240 { padding-top: calc(4px * 60) !important; padding-bottom: calc(4px * 60) !important; }

.py-244 { padding-top: var(--mico-size-244) !important; padding-bottom: var(--mico-size-244) !important; }

.py-248 { padding-top: var(--mico-size-248) !important; padding-bottom: var(--mico-size-248) !important; }

.py-252 { padding-top: var(--mico-size-252) !important; padding-bottom: var(--mico-size-252) !important; }

.py-256 { padding-top: calc(4px * 64) !important; padding-bottom: calc(4px * 64) !important; }

.py-260 { padding-top: calc(4px * 68) !important; padding-bottom: calc(4px * 68) !important; }

.py-264 { padding-top: calc(4px * 72) !important; padding-bottom: calc(4px * 72) !important; }

.py-268 { padding-top: calc(4px * 76) !important; padding-bottom: calc(4px * 76) !important; }

.py-272 { padding-top: calc(4px * 80) !important; padding-bottom: calc(4px * 80) !important; }

.py-276 { padding-top: calc(4px * 84) !important; padding-bottom: calc(4px * 84) !important; }

.py-280 { padding-top: calc(4px * 88) !important; padding-bottom: calc(4px * 88) !important; }

.py-284 { padding-top: calc(4px * 92) !important; padding-bottom: calc(4px * 92) !important; }

.py-288 { padding-top: calc(4px * 96) !important; padding-bottom: calc(4px * 96) !important; }

.py-292 { padding-top: calc(4px * 100) !important; padding-bottom: calc(4px * 100) !important; }

.py-296 { padding-top: calc(4px * 104) !important; padding-bottom: calc(4px * 104) !important; }

.py-300 { padding-top: calc(4px * 108) !important; padding-bottom: calc(4px * 108) !important; }

.py-304 { padding-top: calc(4px * 112) !important; padding-bottom: calc(4px * 112) !important; }

.py-308 { padding-top: calc(4px * 116) !important; padding-bottom: calc(4px * 116) !important; }

.py-312 { padding-top: calc(4px * 120) !important; padding-bottom: calc(4px * 120) !important; }

.py-316 { padding-top: calc(4px * 124) !important; padding-bottom: calc(4px * 124) !important; }

.py-320 { padding-top: calc(4px * 128) !important; padding-bottom: calc(4px * 128) !important; }

.py-324 { padding-top: calc(4px * 132) !important; padding-bottom: calc(4px * 132) !important; }

.py-328 { padding-top: calc(4px * 136) !important; padding-bottom: calc(4px * 136) !important; }

.py-332 { padding-top: calc(4px * 140) !important; padding-bottom: calc(4px * 140) !important; }

.py-336 { padding-top: calc(4px * 144) !important; padding-bottom: calc(4px * 144) !important; }

.py-340 { padding-top: calc(4px * 148) !important; padding-bottom: calc(4px * 148) !important; }

.py-344 { padding-top: calc(4px * 152) !important; padding-bottom: calc(4px * 152) !important; }

.py-348 { padding-top: calc(4px * 156) !important; padding-bottom: calc(4px * 156) !important; }

.py-352 { padding-top: calc(4px * 160) !important; padding-bottom: calc(4px * 160) !important; }

.py-356 { padding-top: calc(4px * 164) !important; padding-bottom: calc(4px * 164) !important; }

.py-360 { padding-top: calc(4px * 168) !important; padding-bottom: calc(4px * 168) !important; }

.py-364 { padding-top: calc(4px * 172) !important; padding-bottom: calc(4px * 172) !important; }

.py-368 { padding-top: calc(4px * 176) !important; padding-bottom: calc(4px * 176) !important; }

.py-372 { padding-top: calc(4px * 180) !important; padding-bottom: calc(4px * 180) !important; }

.py-376 { padding-top: calc(4px * 184) !important; padding-bottom: calc(4px * 184) !important; }

.py-380 { padding-top: calc(4px * 188) !important; padding-bottom: calc(4px * 188) !important; }

.py-384 { padding-top: calc(4px * 192) !important; padding-bottom: calc(4px * 192) !important; }

.py-388 { padding-top: calc(4px * 196) !important; padding-bottom: calc(4px * 196) !important; }

.py-392 { padding-top: calc(4px * 200) !important; padding-bottom: calc(4px * 200) !important; }

.py-396 { padding-top: calc(4px * 204) !important; padding-bottom: calc(4px * 204) !important; }

.py-400 { padding-top: calc(4px * 208) !important; padding-bottom: calc(4px * 208) !important; }

/**
 * Mico CSS Framework - Border, Outline, Shadow & Ring Utilities
 *
 * This file provides comprehensive border and visual enhancement utilities:
 *
 * - Border width, style, and color control
 * - Border radius for rounded corners
 * - Outline utilities for accessibility and focus states
 * - Box shadow utilities for depth and elevation
 * - Ring utilities for focus indicators and highlights
 * - Divide utilities for separating child elements
 *
 * FEATURES:
 * - Complete border control (width, style, color, radius)
 * - Accessible outline and focus management
 * - Flexible shadow system for visual hierarchy
 * - Modern ring utilities using box-shadow
 * - Child element dividers with consistent styling
 *
 * USAGE:
 * Borders: .border, .border-2, .border-primary
 * Radius: .rounded, .rounded-lg, .rounded-full
 * Outlines: .outline, .outline-2, .outline-primary
 * Shadows: .shadow, .shadow-lg, .shadow-none
 * Rings: .ring, .ring-2, .ring-primary
 * Dividers: .divide-y, .divide-x, .divide-gray-200
 */

/* ========================================================================== */

/* BORDER WIDTH UTILITIES                                                    */

/* ========================================================================== */

/**
 * Border Width Control
 *
 * These utilities control the thickness of borders on all sides or specific sides.
 * Use .border-0 to remove borders completely.
 */

/* All sides */

.border-0 { border-width: 0px !important; }

.border-1  { border-width: 1px !important; }

.border-2 { border-width: 2px !important; }

.border-4 { border-width: 4px !important; }

.border-8 { border-width: 8px !important; }

.border-s-0 { border-width: 0px !important; border-style: solid !important; }

.border-s-1  { border-width: 1px !important; border-style: solid !important; }

.border-s-2 { border-width: 2px !important; border-style: solid !important; }

.border-s-4 { border-width: 4px !important; border-style: solid !important; }

.border-s-8 { border-width: 8px !important; border-style: solid !important; }

/* Individual sides */

.border-t-0 { border-top-width: 0px !important; }

.border-t-1 { border-top-width: 1px !important; }

.border-t-2 { border-top-width: 2px !important; }

.border-t-4 { border-top-width: 4px !important; }

.border-t-8 { border-top-width: 8px !important; }

.border-r-0 { border-right-width: 0px !important; }

.border-r-1 { border-right-width: 1px !important; }

.border-r-2 { border-right-width: 2px !important; }

.border-r-4 { border-right-width: 4px !important; }

.border-r-8 { border-right-width: 8px !important; }

.border-b-0 { border-bottom-width: 0px !important; }

.border-b-1  { border-bottom-width: 1px !important; }

.border-b-2 { border-bottom-width: 2px !important; }

.border-b-4 { border-bottom-width: 4px !important; }

.border-b-8 { border-bottom-width: 8px !important; }

.border-l-0 { border-left-width: 0px !important; }

.border-l-1  { border-left-width: 1px !important; }

.border-l-2 { border-left-width: 2px !important; }

.border-l-4 { border-left-width: 4px !important; }

.border-l-8 { border-left-width: 8px !important; }

/* Horizontal and vertical */

.border-x-0 { border-left-width: 0px !important; border-right-width: 0px !important; }

.border-x-1   { border-left-width: 1px !important; border-right-width: 1px !important; }

.border-x-2 { border-left-width: 2px !important; border-right-width: 2px !important; }

.border-x-4 { border-left-width: 4px !important; border-right-width: 4px !important; }

.border-x-8 { border-left-width: 8px !important; border-right-width: 8px !important; }

.border-y-0 { border-top-width: 0px !important; border-bottom-width: 0px !important; }

.border-y-1  { border-top-width: 1px !important; border-bottom-width: 1px !important; }

.border-y-2 { border-top-width: 2px !important; border-bottom-width: 2px !important; }

.border-y-4 { border-top-width: 4px !important; border-bottom-width: 4px !important; }

.border-y-8 { border-top-width: 8px !important; border-bottom-width: 8px !important; }

/* ========================================================================== */

/* BORDER STYLE UTILITIES                                                    */

/* ========================================================================== */

/**
 * Border Style Control
 *
 * These utilities control the style of borders (solid, dashed, dotted, etc.).
 */

.border-solid  { border-style: solid !important; }

.border-dashed { border-style: dashed !important; }

.border-dotted { border-style: dotted !important; }

.border-double { border-style: double !important; }

.border-none   { border-style: none !important; }

/* ========================================================================== */

/* BORDER COLOR UTILITIES                                                    */

/* ========================================================================== */

/**
 * Border Color Control
 *
 * These utilities control the color of borders using the framework's color system.
 */

/* Brand Colors */

.border-primary   { border-color: rgb(39, 132, 213) !important; }

.border-secondary { border-color: rgb(81, 97, 145) !important; }

.border-accent    { border-color: rgb(236, 125, 0) !important; border-color: color(display-p3 0.86926 0.51255 0.09455) !important; }

/* State Colors */

.border-success { border-color: rgb(58, 168, 91) !important; }

.border-error   { border-color: rgb(215, 71, 69) !important; }

.border-warning { border-color: rgb(223, 161, 26) !important; }

.border-info    { border-color: rgb(39, 132, 213) !important; }

/* Neutral Colors - OKLCH System */

.border-white { border-color: rgb(237, 238, 242) !important; }

.border-black { border-color: rgb(3, 3, 4) !important; }

/* Gray Scale - OKLCH System */

.border-gray { border-color: rgb(97, 99, 105) !important; }

.border-gray-2xlight { border-color: rgb(141, 143, 148) !important; }

.border-gray-3xlight { border-color: rgb(172, 174, 178) !important; }

.border-gray-4xlight { border-color: rgb(205, 206, 208) !important; }

.border-gray-5xlight { border-color: rgb(229, 230, 231) !important; }

.border-gray-2xdark { border-color: rgb(69, 72, 78) !important; }

.border-gray-3xdark { border-color: rgb(43, 46, 52) !important; }

.border-gray-4xdark { border-color: rgb(20, 22, 28) !important; }

.border-gray-5xdark { border-color: rgb(5, 6, 12) !important; }

/* Extended Color Palette - OKLCH System */

.border-red { border-color: rgb(212, 12, 26) !important; }

.border-yellow { border-color: rgb(212, 167, 62) !important; }

.border-green { border-color: rgb(61, 151, 53) !important; }

.border-blue { border-color: rgb(0, 127, 218) !important; border-color: color(display-p3 0.0875 0.49016 0.83544) !important; }

.border-indigo { border-color: rgb(80, 90, 200) !important; }

.border-purple { border-color: rgb(150, 74, 198) !important; }

.border-pink { border-color: rgb(231, 104, 139) !important; }

/* Special Colors */

.border-transparent { border-color: transparent !important; }

.border-current     { border-color: currentColor !important; }

/* ========================================================================== */

/* BORDER RADIUS UTILITIES                                                   */

/* ========================================================================== */

/**
 * Border Radius Control
 *
 * These utilities control the roundness of corners for visual design.
 */

/* All corners */

.rounded-none { border-radius: 0; }

.rounded-sm   { border-radius: 2px; }

.rounded-md   { border-radius: 4px; }

.rounded-lg   { border-radius: 8px; }

.rounded-xl   { border-radius: 12px; }

.rounded-2xl  { border-radius: 16px; }

.rounded-3xl  { border-radius: var(--mico-radius-3xl); }

.rounded-full { border-radius: 9999px; }

/* Individual corners */

.rounded-t-none { border-top-left-radius: 0; border-top-right-radius: 0; }

.rounded-t-sm   { border-top-left-radius: 2px; border-top-right-radius: 2px; }

.rounded-t-md   { border-top-left-radius: 4px; border-top-right-radius: 4px; }

.rounded-t-lg   { border-top-left-radius: 8px; border-top-right-radius: 8px; }

.rounded-t-xl   { border-top-left-radius: 12px; border-top-right-radius: 12px; }

.rounded-t-2xl  { border-top-left-radius: 16px; border-top-right-radius: 16px; }

.rounded-t-3xl  { border-top-left-radius: var(--mico-radius-3xl); border-top-right-radius: var(--mico-radius-3xl); }

.rounded-t-full { border-top-left-radius: 9999px; border-top-right-radius: 9999px; }

.rounded-r-none { border-top-right-radius: 0; border-bottom-right-radius: 0; }

.rounded-r-sm   { border-top-right-radius: 2px; border-bottom-right-radius: 2px; }

.rounded-r-md   { border-top-right-radius: 4px; border-bottom-right-radius: 4px; }

.rounded-r-lg   { border-top-right-radius: 8px; border-bottom-right-radius: 8px; }

.rounded-r-xl   { border-top-right-radius: 12px; border-bottom-right-radius: 12px; }

.rounded-r-2xl  { border-top-right-radius: 16px; border-bottom-right-radius: 16px; }

.rounded-r-3xl  { border-top-right-radius: var(--mico-radius-3xl); border-bottom-right-radius: var(--mico-radius-3xl); }

.rounded-r-full { border-top-right-radius: 9999px; border-bottom-right-radius: 9999px; }

.rounded-b-none { border-bottom-left-radius: 0; border-bottom-right-radius: 0; }

.rounded-b-sm   { border-bottom-left-radius: 2px; border-bottom-right-radius: 2px; }

.rounded-b-md   { border-bottom-left-radius: 4px; border-bottom-right-radius: 4px; }

.rounded-b-lg   { border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; }

.rounded-b-xl   { border-bottom-left-radius: 12px; border-bottom-right-radius: 12px; }

.rounded-b-2xl  { border-bottom-left-radius: 16px; border-bottom-right-radius: 16px; }

.rounded-b-3xl  { border-bottom-left-radius: var(--mico-radius-3xl); border-bottom-right-radius: var(--mico-radius-3xl); }

.rounded-b-full { border-bottom-left-radius: 9999px; border-bottom-right-radius: 9999px; }

.rounded-l-none { border-top-left-radius: 0; border-bottom-left-radius: 0; }

.rounded-l-sm   { border-top-left-radius: 2px; border-bottom-left-radius: 2px; }

.rounded-l-md   { border-top-left-radius: 4px; border-bottom-left-radius: 4px; }

.rounded-l-lg   { border-top-left-radius: 8px; border-bottom-left-radius: 8px; }

.rounded-l-xl   { border-top-left-radius: 12px; border-bottom-left-radius: 12px; }

.rounded-l-2xl  { border-top-left-radius: 16px; border-bottom-left-radius: 16px; }

.rounded-l-3xl  { border-top-left-radius: var(--mico-radius-3xl); border-bottom-left-radius: var(--mico-radius-3xl); }

.rounded-l-full { border-top-left-radius: 9999px; border-bottom-left-radius: 9999px; }

/* ========================================================================== */

/* OUTLINE UTILITIES                                                         */

/* ========================================================================== */

/**
 * Outline Control
 *
 * These utilities control outlines for accessibility and focus states.
 * Outlines are essential for keyboard navigation and accessibility compliance.
 */

/* Outline Width */

.outline-0 { outline-width: 0px; }

.outline-1 { outline-width: 1px; }

.outline-2 { outline-width: 2px; }

.outline-4 { outline-width: 4px; }

.outline-8 { outline-width: 8px; }

.outline-s-0 { outline-width: 0px; outline-style: solid; }

.outline-s-1 { outline-width: 1px; outline-style: solid; }

.outline-s-2 { outline-width: 2px; outline-style: solid; }

.outline-s-4 { outline-width: 4px; outline-style: solid; }

.outline-s-8 { outline-width: 8px; outline-style: solid; }

/* Outline Style */

.outline-none   { outline-style: none; }

.outline-solid  { outline-style: solid; }

.outline-dashed { outline-style: dashed; }

.outline-dotted { outline-style: dotted; }

.outline-double { outline-style: double; }

/* Outline Color - OKLCH System */

.outline-primary   { outline-color: rgb(39, 132, 213); }

.outline-secondary { outline-color: rgb(81, 97, 145); }

.outline-accent    { outline-color: rgb(236, 125, 0); outline-color: color(display-p3 0.86926 0.51255 0.09455); }

.outline-success   { outline-color: rgb(58, 168, 91); }

.outline-error     { outline-color: rgb(215, 71, 69); }

.outline-warning   { outline-color: rgb(223, 161, 26); }

.outline-info      { outline-color: rgb(39, 132, 213); }

.outline-white     { outline-color: rgb(237, 238, 242); }

.outline-black     { outline-color: rgb(3, 3, 4); }

.outline-gray      { outline-color: rgb(97, 99, 105); }

.outline-gray-3xlight { outline-color: rgb(172, 174, 178); }

.outline-gray-3xdark  { outline-color: rgb(43, 46, 52); }

/* Outline Offset */

.outline-offset-0 { outline-offset: 0px; }

.outline-offset-1 { outline-offset: 1px; }

.outline-offset-2 { outline-offset: 2px; }

.outline-offset-4 { outline-offset: 4px; }

.outline-offset-8 { outline-offset: 8px; }

/* ========================================================================== */

/* BOX SHADOW UTILITIES                                                      */

/* ========================================================================== */

/**
 * Box Shadow Control
 *
 * These utilities control box shadows for depth, elevation, and visual hierarchy.
 */

/* Light Shadow Presets */

.shadow-none { box-shadow: none !important; }

.shadow-xs-light   { box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05) !important; }

.shadow-sm-light   { box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05) !important; }

.shadow-md-light   { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important; }

.shadow-lg-light   { box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1) !important; }

.shadow-xl-light   { box-shadow: 0 20px 25px rgba(0, 0, 0, 0.15) !important; }

.shadow-2xl-light  { box-shadow:   !important; }

.shadow-3xl-light  { box-shadow:   !important; }

/* Dark Mode Shadow Presets */

.shadow-xs-dark   { box-shadow: var(--mico-shadow-xs-dark) !important; }

.shadow-sm-dark   { box-shadow: var(--mico-shadow-sm-dark) !important; }

.shadow-md-dark   { box-shadow: var(--mico-shadow-md-dark) !important; }

.shadow-lg-dark   { box-shadow: var(--mico-shadow-lg-dark) !important; }

.shadow-xl-dark   { box-shadow: var(--mico-shadow-xl-dark) !important; }

.shadow-2xl-dark  { box-shadow: var(--mico-shadow-2xl-dark) !important; }

.shadow-3xl-dark  { box-shadow: var(--mico-shadow-3xl-dark) !important; }

/* Inner Shadow */

.shadow-inner-light { box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05); }

.shadow-inner-dark { box-shadow: var(--mico-shadow-inset-sm-dark); }

/* Colored Shadows */

.shadow-primary { box-shadow: 0px 4.5px 4px -2.8px rgb(79, 129, 174) !important; }

.shadow-secondary { box-shadow: 0px 4.5px 4px -2.8px rgb(96, 104, 131) !important; }

.shadow-accent { box-shadow: 0px 4.5px 4px -2.8px rgb(174, 113, 22) !important; }

.shadow-success   { box-shadow: 0px 4.5px 4px -2.8px rgb(83, 143, 101) !important; }

.shadow-error     { box-shadow: 0px 4.5px 4px -2.8px rgb(182, 103, 102) !important; }

.shadow-warning   { box-shadow: 0px 4.5px 4px -2.8px rgb(179, 145, 70) !important; }

.shadow-info      { box-shadow: 0px 4.5px 4px -2.8px rgb(79, 129, 174) !important; }

/* ========================================================================== */

/* DIVIDE UTILITIES                                                          */

/* ========================================================================== */

/**
 * Divide Control
 *
 * These utilities add borders between child elements for visual separation.
 * Perfect for lists, navigation items, and grouped content.
 */

/* Divide Y (Horizontal borders between children) */

.divide-y-0 > * + * { border-top-width: 0px; }

.divide-y-1  > * + * { border-top-width: 1px; }

.divide-y-2 > * + * { border-top-width: 2px; }

.divide-y-4 > * + * { border-top-width: 4px; }

.divide-y-8 > * + * { border-top-width: 8px; }

/* Divide X (Vertical borders between children) */

.divide-x-0 > * + * { border-left-width: 0px; }

.divide-x-1  > * + * { border-left-width: 1px; }

.divide-x-2 > * + * { border-left-width: 2px; }

.divide-x-4 > * + * { border-left-width: 4px; }

.divide-x-8 > * + * { border-left-width: 8px; }

/* Divide Colors - OKLCH System */

.divide-primary   > * + * { border-color: rgb(39, 132, 213); }

.divide-secondary > * + * { border-color: rgb(81, 97, 145); }

.divide-accent    > * + * { border-color: rgb(236, 125, 0); border-color: color(display-p3 0.86926 0.51255 0.09455); }

.divide-gray      > * + * { border-color: rgb(97, 99, 105); }

.divide-gray-2xlight > * + * { border-color: rgb(141, 143, 148); }

.divide-gray-3xlight > * + * { border-color: rgb(172, 174, 178); }

.divide-gray-2xdark  > * + * { border-color: rgb(69, 72, 78); }

/* Divide Styles */

.divide-solid  > * + * { border-style: solid; }

.divide-dashed > * + * { border-style: dashed; }

.divide-dotted > * + * { border-style: dotted; }

/* ========================================================================== */

/* ACCESSIBILITY & BROWSER SUPPORT                                           */

/* ========================================================================== */

/**
 * High Contrast Mode Support
 *
 * Enhanced visibility for high contrast environments.
 */

@media (prefers-contrast: high) {
  .border-1,
  .border-2,
  .border-4,
  .border-8 {
    border-color: currentColor;
  }

  .outline-1,
  .outline-2,
  .outline-4,
  .outline-8 {
    outline-color: currentColor;
  }

}

/**
 * Reduced Motion Support
 *
 * Respects user preferences for reduced motion.
 */

@media (prefers-reduced-motion: reduce) {
  .shadow-xs,
  .shadow-sm,
  .shadow-md,
  .shadow-lg,
  .shadow-xl,
  .shadow-2xl,
  .shadow-3xl {
    transition: none;
  }
}

/**
 * Print Media Support
 *
 * Optimized styles for print media.
 */

@media print {
  .shadow-xs,
  .shadow-sm,
  .shadow-md,
  .shadow-lg,
  .shadow-xl,
  .shadow-2xl,
  .shadow-3xl {
    box-shadow: none !important;
  }

}

/* ========================================================================== */

/* USAGE EXAMPLES AND DOCUMENTATION                                          */

/* ========================================================================== */

/**
 * USAGE EXAMPLES:
 *
 * 1. Basic border:
 *    <div class="border border-gray-300 rounded">Content</div>
 *
 * 2. Focus ring:
 *    <button class="ring ring-primary ring-offset-2">Button</button>
 *
 * 3. Card with shadow:
 *    <div class="border border-gray-200 rounded-lg shadow-md">Card</div>
 *
 * 4. Divided list:
 *    <ul class="divide-y divide-gray-200">
 *      <li>Item 1</li>
 *      <li>Item 2</li>
 *    </ul>
 *
 * 5. Accessible outline:
 *    <input class="outline outline-2 outline-primary outline-offset-2">
 *
 * 6. Complex border styling:
 *    <div class="border-2 border-dashed border-primary rounded-xl">
 *
 * ACCESSIBILITY NOTES:
 * - Always provide sufficient contrast for borders and outlines
 * - Use outline utilities for focus indicators
 * - Test with high contrast mode
 * - Ensure ring utilities are visible for keyboard navigation
 */

/**
 * Mico CSS Framework - Button & Link Utilities
 *
 * This file provides comprehensive button and link styling utilities following
 * the guidelines for modern, accessible, and flexible button design.
 *
 * FEATURES:
 * - Base button class with consistent styling
 * - Color variants (primary, secondary, success, etc.)
 * - Style modifiers (outline, text, ghost)
 * - Size modifiers (xs, sm, md, lg, xl)
 * - Shape modifiers (pill, square, circle)
 * - Shadow modifiers for depth
 * - State modifiers (active, loading, disabled)
 * - Layout modifiers (block, icon support)
 * - Link utilities with advanced underline control
 *
 * USAGE:
 * Basic: .btn .btn-primary
 * Variants: .btn .btn-secondary .btn-outline
 * Sizes: .btn .btn-primary .btn-lg
 * Links: .link-primary .link-underline-thick
 */

/* ========================================================================== */

/* BASE BUTTON CLASS                                                         */

/* ========================================================================== */

/**
 * Base Button Class
 *
 * The foundational class for all button styling. Provides consistent
 * base styles that work across all button variants.
 */

.btn {
  -webkit-text-decoration: none !important;
  text-decoration: none !important;
}

.btn {
  /* Layout and positioning */
  position: relative;
  display: inline-block;

  /* Typography */
  font-size: max(0.980rem, min(2vw, 1.2rem));
  font-family: inherit;
  font-weight: 400;
  line-height: 1.25;
  text-align: center;
  white-space: nowrap;

  /* Spacing */
  padding: calc(4px * 4) calc(4px * 8);
  margin: 0;

  /* Borders and appearance */
  border: 1px solid transparent;
  border-radius: 4px;

  /* Interaction */
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;

  /* Accessibility */
  vertical-align: middle;

  /* Prevent text selection and improve touch targets */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* ========================================================================== */

/* BUTTON COLOR VARIANTS                                                     */

/* ========================================================================== */

/**
 * Button Color Variants
 *
 * These classes control the background color, text color, and border color
 * for different button purposes and emphasis levels.
 */

/* Brand Colors - OKLCH System */

.btn-primary {
  background-color: rgb(39, 132, 213) !important;
  color: rgb(237, 238, 242) !important;
  border-color: rgb(103, 170, 237) !important;
}

.btn-secondary {
  background-color: rgb(81, 97, 145) !important;
  color: rgb(237, 238, 242) !important;
  border-color: rgb(81, 97, 145) !important;
}

.btn-accent {
  background-color: rgb(236, 125, 0) !important;
  background-color: color(display-p3 0.86926 0.51255 0.09455) !important;
  color: rgb(237, 238, 242) !important;
  border-color: rgb(236, 125, 0) !important;
  border-color: color(display-p3 0.86926 0.51255 0.09455) !important;
}

/* State Colors - OKLCH System */

.btn-success {
  background-color: rgb(58, 168, 91) !important;
  color: rgb(237, 238, 242) !important;
  border-color: rgb(58, 168, 91) !important;
}

.btn-danger {
  background-color: rgb(215, 71, 69) !important;
  color: rgb(237, 238, 242) !important;
  border-color: rgb(215, 71, 69) !important;
}

.btn-warning {
  background-color: rgb(223, 161, 26) !important;
  color: rgb(1, 2, 3) !important;
  border-color: rgb(223, 161, 26) !important;
}

.btn-info {
  background-color: rgb(39, 132, 213) !important;
  color: rgb(237, 238, 242) !important;
  border-color: rgb(39, 132, 213) !important;
}

/* Neutral Colors - OKLCH System */

.btn-light {
  background-color: rgb(229, 230, 231) !important;
  color: rgb(5, 6, 12) !important;
  border-color: rgb(229, 230, 231) !important;
}

.btn-dark {
  background-color: rgb(5, 6, 12) !important;
  color: rgb(237, 238, 242) !important;
  border-color: rgb(5, 6, 12) !important;
}

.btn-transparent {
  background-color: transparent !important;
  color: inherit !important;
  border-color: transparent !important;
}

/* ========================================================================== */

/* BUTTON STYLE MODIFIERS                                                    */

/* ========================================================================== */

/**
 * Button Style Modifiers
 *
 * These classes modify the visual style of button variants.
 * They should be combined with color variant classes.
 */

/* Outline Style - Transparent background with colored border and text */

.btn-outline {
  background-color: transparent !important;
  border-width: 1px !important;
}

.btn-outline {
  border-style: solid;
}

.btn-outline.btn-primary {
  color: rgb(39, 132, 213);
  border-color: rgb(39, 132, 213);
}

.btn-outline.btn-secondary {
  color: rgb(81, 97, 145);
  border-color: rgb(81, 97, 145);
}

.btn-outline.btn-accent {
  color: rgb(236, 125, 0);
  color: color(display-p3 0.86926 0.51255 0.09455);
  border-color: rgb(236, 125, 0);
  border-color: color(display-p3 0.86926 0.51255 0.09455);
}

.btn-outline.btn-success {
  color: rgb(58, 168, 91);
  border-color: rgb(58, 168, 91);
}

.btn-outline.btn-danger {
  color: rgb(215, 71, 69);
  border-color: rgb(215, 71, 69);
}

.btn-outline.btn-warning {
  color: rgb(223, 161, 26);
  border-color: rgb(223, 161, 26);
}

.btn-outline.btn-info {
  color: rgb(39, 132, 213);
  border-color: rgb(39, 132, 213);
}

.btn-outline.btn-light {
  color: rgb(43, 46, 52) !important;
  border-color: rgb(172, 174, 178) !important;
}

.btn-outline.btn-dark {
  color: rgb(20, 22, 28) !important;
  border-color: rgb(20, 22, 28) !important;
}

/* Text Style - No background or border, only colored text */

.btn-text {
  background-color: transparent !important;
}

.btn-text {
  border-color: transparent;
}

/* Ghost Style - Minimal styling with subtle hover effects */

.btn-ghost {
  background-color: transparent !important;
}

.btn-ghost {
  border-color: transparent;
  color: rgb(43, 46, 52);
}

/* ========================================================================== */

/* BUTTON SIZE MODIFIERS                                                     */

/* ========================================================================== */

/**
 * Button Size Modifiers
 *
 * These classes control the padding and font size of buttons.
 * The .btn class provides the default/medium size.
 */

.btn-xs {
  padding: calc(4px * 2) calc(4px * 3) !important;
  font-size: max(0.995rem, min(1.5vw, 0.975rem)) !important;
}

.btn-sm {
  padding: calc(4px * 3) calc(4px * 4) !important;
  font-size: max(0.980rem, min(2vw, 1.2rem)) !important;
}

.btn-lg {
  padding: calc(4px * 5) calc(4px * 8) !important;
  font-size: max(1.25rem, min(3vw, 1.675rem)) !important;
}

.btn-xl {
  padding: calc(4px * 6) calc(4px * 10) !important;
  font-size: max(1.5rem, min(4vw, 2rem)) !important;
}

/* ========================================================================== */

/* BUTTON SHAPE MODIFIERS                                                    */

/* ========================================================================== */

/**
 * Button Shape Modifiers
 *
 * These classes control the border-radius of buttons.
 */

.btn-pill {
  border-radius: 9999px !important;
}

.btn-square {
  border-radius: 0 !important;
}

.btn-circle {
  border-radius: 9999px !important;
  width: calc(4px * 10) !important;
  height: calc(4px * 10) !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* ========================================================================== */

/* BUTTON SHADOW MODIFIERS                                                   */

/* ========================================================================== */

/**
 * Button Shadow Modifiers
 *
 * These classes apply box-shadow for depth and elevation.
 */

.btn-shadow {
  box-shadow: var(--mico-btn-shadow-md) !important;
}

.btn-shadow-sm {
  box-shadow: var(--mico-btn-shadow-sm) !important;
}

.btn-shadow-lg {
  box-shadow: var(--mico-btn-shadow-lg) !important;
}

.btn-shadow-none {
  box-shadow: none !important;
}

/* ========================================================================== */

/* BUTTON STATE MODIFIERS                                                    */

/* ========================================================================== */

/**
 * Button State Modifiers
 *
 * These classes provide visual feedback for different button states.
 */

/* Active State */

.is-active,
.btn-active {
  transform: translateY(1px) !important;
  box-shadow: var(--mico-shadow-sm) !important;
}

/* Loading State */

.is-loading:active,
.btn-loading:active {
  color: transparent !important;
  pointer-events: none !important;
  position: relative !important;
}

.is-loading::after,
.btn-loading::after {
  content: "";
  position: absolute;
  width: 1em;
  height: 1em;
  top: 50%;
  left: 50%;
  margin-top: -0.5em;
  margin-left: -0.5em;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: currentColor;
  border-radius: 9999px;
  animation: btn-loading-spinner 1s linear infinite;
}

@keyframes btn-loading-spinner {
  from { transform: rotate(0turn); }
  to { transform: rotate(1turn); }
}

/* Disabled State */

.btn:disabled,
.btn-disabled,
.is-disabled {
  opacity: var(--mico-opacity-50);
  cursor: not-allowed;
  pointer-events: none;
}

/* ========================================================================== */

/* BUTTON LAYOUT MODIFIERS                                                   */

/* ========================================================================== */

/**
 * Button Layout Modifiers
 *
 * These classes control button layout and icon integration.
 */

/* Block Button */

.btn-block {
  display: block;
  width: 100%;
}

/* Icon Buttons */

.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: calc(4px * 2);
}

.btn-icon-left {
  display: inline-flex;
  align-items: center;
  gap: calc(4px * 2);
}

.btn-icon-right {
  display: inline-flex;
  align-items: center;
  flex-direction: row-reverse;
  gap: calc(4px * 2);
}

.btn-icon-only {
  padding: calc(4px * 3);
  width: calc(4px * 10);
  height: calc(4px * 10);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* ========================================================================== */

/* BUTTON GROUPING UTILITIES                                                 */

/* ========================================================================== */

/**
 * Button Grouping Utilities
 *
 * These classes create grouped button layouts.
 */

/* Horizontal Button Group */

.btn-group {
  display: inline-flex;
  vertical-align: middle;
}

.btn-group > .btn {
  position: relative;
  flex: 1 1 auto;
  margin: 0;
}

.btn-group > .btn:not(:first-child) {
  margin-left: calc(1px * -1);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group > .btn:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active {
  z-index: 1;
}

/* Vertical Button Group */

.btn-group-vertical {
  display: inline-flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.btn-group-vertical > .btn {
  width: 100%;
  margin: 0;
}

.btn-group-vertical > .btn:not(:first-child) {
  margin-top: calc(1px * -1);
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.btn-group-vertical > .btn:not(:last-child) {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* Button Toolbar */

.btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: calc(4px * 2);
}

/* ========================================================================== */

/* LINK UTILITIES                                                            */

/* ========================================================================== */

/**
 * Link Color Modifiers
 *
 * These classes control the color of links for different purposes.
 */

/* Brand Link Colors */

.link-primary { color: rgb(39, 132, 213); }

.link-secondary { color: rgb(81, 97, 145); }

.link-accent { color: rgb(236, 125, 0); color: color(display-p3 0.86926 0.51255 0.09455); }

/* State Link Colors */

.link-success { color: rgb(58, 168, 91); }

.link-danger { color: rgb(215, 71, 69); }

.link-warning { color: rgb(223, 161, 26); }

.link-info { color: rgb(39, 132, 213); }

/* Neutral Link Colors */

.link-light { color: rgb(172, 174, 178); }

.link-dark { color: rgb(5, 6, 12); }

.link-muted { color: rgb(43, 46, 52); }

.link-body { color: inherit; }

/**
 * Link Underline Control
 *
 * These classes control the presence and behavior of link underlines.
 */

/* Basic Underline Control */

.link-underline { -webkit-text-decoration: underline; text-decoration: underline; }

.link-no-underline { -webkit-text-decoration: none; text-decoration: none; }

.link-underline-hover:hover { -webkit-text-decoration: underline; text-decoration: underline; }

.link-no-underline-hover:hover { -webkit-text-decoration: none; text-decoration: none; }

/* Underline Thickness */

.link-underline-auto { text-decoration-thickness: auto; }

.link-underline-from-font { text-decoration-thickness: from-font; }

.link-underline-thin { text-decoration-thickness: 1px; }

.link-underline-normal { text-decoration-thickness: 2px; }

.link-underline-thick { text-decoration-thickness: 4px; }

/* Underline Offset */

.link-underline-offset-auto { text-underline-offset: auto; }

.link-underline-offset-sm { text-underline-offset: 1px; }

.link-underline-offset-md { text-underline-offset: 2px; }

.link-underline-offset-lg { text-underline-offset: 4px; }

/* Underline Style */

.link-underline-solid { text-decoration-style: solid; }

.link-underline-dotted { text-decoration-style: dotted; }

.link-underline-dashed { text-decoration-style: dashed; }

.link-underline-wavy { text-decoration-style: wavy; }

.link-underline-double { text-decoration-style: double; }

/* Underline Color */

.link-underline-color-current { text-decoration-color: currentColor; }

.link-underline-color-transparent { text-decoration-color: transparent; }

.link-underline-color-primary { text-decoration-color: rgb(39, 132, 213); }

.link-underline-color-secondary { text-decoration-color: rgb(81, 97, 145); }

/**
 * Link Opacity Control
 *
 * These classes control link opacity for visual hierarchy.
 */

.link-opacity-100 { opacity: var(--mico-opacity-100); }

.link-opacity-75 { opacity: var(--mico-opacity-75); }

.link-opacity-50 { opacity: var(--mico-opacity-50); }

.link-opacity-25 { opacity: var(--mico-opacity-25); }

.link-hover-opacity-100:hover { opacity: var(--mico-opacity-100); }

.link-hover-opacity-75:hover { opacity: var(--mico-opacity-75); }

/**
 * Link Layout Control
 *
 * These classes control link display and layout behavior.
 */

.link-reset {
  color: inherit;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.link-block { display: block; }

.link-inline-block { display: inline-block; }

/* ========================================================================== */

/* ACCESSIBILITY & BROWSER SUPPORT                                           */

/* ========================================================================== */

/**
 * Focus Styles
 *
 * Accessible focus indicators for keyboard navigation.
 */

.btn:focus-visible {
  outline: 2px solid rgb(39, 132, 213);
  outline-offset: 2px;
}

/**
 * High Contrast Mode Support
 *
 * Enhanced visibility for high contrast environments.
 */

@media (prefers-contrast: high) {
  .btn {
    border: 2px solid currentColor;
  }

  .btn-outline {
    border-width: 4px;
  }

  .link-underline {
    text-decoration-thickness: 2px;
  }
}

/**
 * Reduced Motion Support
 *
 * Respects user preferences for reduced motion.
 */

@media (prefers-reduced-motion: reduce) {
  .btn,
  .btn::after,
  .btn::before {
    transition: none;
    animation: none;
  }

  .btn-loading::after {
    animation: none;
    border: 2px solid currentColor;
    border-right-color: transparent;
  }

}

/**
 * Print Media Support
 *
 * Optimized styles for print media.
 */

@media print {
  .btn {
    background: transparent !important;
    color: rgb(3, 3, 4) !important;
    border: 1px solid rgb(3, 3, 4) !important;
    box-shadow: none !important;
  }

  .link-underline {
    -webkit-text-decoration: underline !important;
    text-decoration: underline !important;
  }
}

/* ========================================================================== */

/* USAGE EXAMPLES AND DOCUMENTATION                                          */

/* ========================================================================== */

/**
 * USAGE EXAMPLES:
 *
 * 1. Primary button with shadow:
 *    <button class="btn btn-primary btn-shadow">Save Changes</button>
 *
 * 2. Outline button with hover lift:
 *    <button class="btn btn-secondary btn-outline hover-btn-lift">Cancel</button>
 *
 * 3. Icon button:
 *    <button class="btn btn-primary btn-icon">
 *      <svg>...</svg> Download
 *    </button>
 *
 * 4. Button group:
 *    <div class="btn-group">
 *      <button class="btn btn-primary">Left</button>
 *      <button class="btn btn-primary">Middle</button>
 *      <button class="btn btn-primary">Right</button>
 *    </div>
 *
 * 5. Advanced link styling:
 *    <a href="#" class="link-primary link-underline-thick link-underline-offset-sm link-underline-wavy">
 *      Styled Link
 *    </a>
 *
 * 6. Loading button:
 *    <button class="btn btn-primary btn-loading">Processing...</button>
 */

/**
 * Mico CSS Framework - Interactive States
 *
 * This file defines utility classes for interactive states (hover, focus, active).
 * These classes follow a consistent naming pattern for easy use and extension.
 *
 * NAMING CONVENTION:
 * - Hover: .hover-{property}-{value}
 * - Focus: .focus-{property}-{value}
 * - Active: .active-{property}-{value}
 *
 * USAGE EXAMPLES:
 * <button class="hover-bg-primary">Hover for primary background</button>
 * <a class="hover-text-accent focus-text-primary">Interactive link</a>
 */

/* ========================================================================== */

/* BASE STATE UTILITIES                                                       */

/* ========================================================================== */

/**
 * Base transition for all interactive state classes
 * This ensures smooth transitions when states change
 */

[class^="hover-"], [class*=" hover-"],
[class^="focus-"], [class*=" focus-"],
[class^="active-"], [class*=" active-"] {
    transition: all .4s cubic-bezier(0.25, 0.1, 0.25, 1.0);
}

/* ========================================================================== */

/* HOVER STATE UTILITIES                                                      */

/* ========================================================================== */

/**
 * BACKGROUND COLOR HOVER STATES
 *
 * These utilities apply background colors on hover
 */

/* ---------- Brand Colors ---------- */

.hover-bg-primary:hover { background-color: rgb(39, 132, 213) !important; }

.hover-bg-secondary:hover { background-color: rgb(81, 97, 145) !important; }

.hover-bg-accent:hover { background-color: rgb(236, 125, 0) !important; background-color: color(display-p3 0.86926 0.51255 0.09455) !important; }

/* ---------- Primary Color Variations ---------- */

/* Tints - Lighter variations */

.hover-bg-primary-2xlight:hover { background-color: rgb(103, 170, 237) !important; }

.hover-bg-primary-3xlight:hover { background-color: rgb(145, 195, 246) !important; }

.hover-bg-primary-4xlight:hover { background-color: rgb(186, 219, 254) !important; }

.hover-bg-primary-5xlight:hover { background-color: rgb(221, 237, 255) !important; background-color: color(display-p3 0.87736 0.92848 0.9935) !important; }

/* Shades - Darker variations */

.hover-bg-primary-2xdark:hover { background-color: rgb(0, 94, 181) !important; background-color: color(display-p3 0.06248 0.36112 0.68706) !important; }

.hover-bg-primary-3xdark:hover { background-color: rgb(0, 60, 129) !important; background-color: color(display-p3 0 0.21727 0.54103) !important; }

.hover-bg-primary-4xdark:hover { background-color: rgb(0, 29, 78) !important; background-color: color(display-p3 0 0.10188 0.3246) !important; }

.hover-bg-primary-5xdark:hover { background-color: rgb(0, 8, 44) !important; background-color: color(display-p3 0 0.02366 0.17799) !important; }

/* ---------- Secondary Color Variations ---------- */

/* Tints - Lighter variations */

.hover-bg-secondary-2xlight:hover { background-color: rgb(128, 142, 183) !important; }

.hover-bg-secondary-3xlight:hover { background-color: rgb(162, 173, 205) !important; }

.hover-bg-secondary-4xlight:hover { background-color: rgb(197, 205, 227) !important; }

.hover-bg-secondary-5xlight:hover { background-color: rgb(226, 230, 241) !important; }

/* Shades - Darker variations */

.hover-bg-secondary-2xdark:hover { background-color: rgb(53, 68, 119) !important; }

.hover-bg-secondary-3xdark:hover { background-color: rgb(28, 40, 93) !important; }

.hover-bg-secondary-4xdark:hover { background-color: rgb(7, 10, 68) !important; }

.hover-bg-secondary-5xdark:hover { background-color: rgb(3, 0, 47) !important; background-color: color(display-p3 0.00631 0 0.17626) !important; }

/* ---------- Accent Color Variations ---------- */

/* Tints - Lighter variations */

.hover-bg-accent-2xlight:hover { background-color: rgb(253, 162, 81) !important; }

.hover-bg-accent-3xlight:hover { background-color: rgb(255, 189, 132) !important; background-color: color(display-p3 0.97138 0.75229 0.55196) !important; }

.hover-bg-accent-4xlight:hover { background-color: rgb(255, 215, 178) !important; background-color: color(display-p3 0.99752 0.84938 0.71576) !important; }

.hover-bg-accent-5xlight:hover { background-color: rgb(255, 235, 217) !important; background-color: color(display-p3 1 0.92547 0.85819) !important; }

/* Shades - Darker variations */

.hover-bg-accent-2xdark:hover { background-color: rgb(178, 89, 0) !important; background-color: color(display-p3 0.67601 0.3501 0) !important; }

.hover-bg-accent-3xdark:hover { background-color: rgb(122, 56, 0) !important; background-color: color(display-p3 0.46076 0.22297 0) !important; }

.hover-bg-accent-4xdark:hover { background-color: rgb(70, 27, 0) !important; background-color: color(display-p3 0.26237 0.10629 0) !important; }

.hover-bg-accent-5xdark:hover { background-color: rgb(35, 7, 0) !important; background-color: color(display-p3 0.12787 0.02868 0) !important; }

/* ---------- Neutral Colors - OKLCH System ---------- */

/* Gray Color Variations */

.hover-bg-gray:hover { background-color: rgb(97, 99, 105) !important; }

.hover-bg-gray-2xlight:hover { background-color: rgb(141, 143, 148) !important; }

.hover-bg-gray-3xlight:hover { background-color: rgb(172, 174, 178) !important; }

.hover-bg-gray-4xlight:hover { background-color: rgb(205, 206, 208) !important; }

.hover-bg-gray-5xlight:hover { background-color: rgb(229, 230, 231) !important; }

.hover-bg-gray-2xdark:hover { background-color: rgb(69, 72, 78) !important; }

.hover-bg-gray-3xdark:hover { background-color: rgb(43, 46, 52) !important; }

.hover-bg-gray-4xdark:hover { background-color: rgb(20, 22, 28) !important; }

.hover-bg-gray-5xdark:hover { background-color: rgb(5, 6, 12) !important; }

/* Black Color Variations */

.hover-bg-black:hover { background-color: rgb(3, 3, 4) !important; }

.hover-bg-black-2xlight:hover { background-color: rgb(63, 64, 66) !important; }

.hover-bg-black-3xlight:hover { background-color: rgb(113, 113, 115) !important; }

.hover-bg-black-4xlight:hover { background-color: rgb(167, 168, 169) !important; }

.hover-bg-black-5xlight:hover { background-color: rgb(210, 210, 211) !important; }

.hover-bg-black-2xdark:hover { background-color: rgb(1, 2, 3) !important; }

.hover-bg-black-3xdark:hover { background-color: rgb(1, 1, 1) !important; }

.hover-bg-black-4xdark:hover { background-color: rgb(0, 0, 0) !important; }

.hover-bg-black-5xdark:hover { background-color: rgb(0, 0, 0) !important; }

/* White Color Variations */

.hover-bg-white:hover { background-color: rgb(237, 238, 242) !important; }

.hover-bg-white-2xlight:hover { background-color: rgb(242, 243, 246) !important; }

.hover-bg-white-3xlight:hover { background-color: rgb(246, 247, 249) !important; }

.hover-bg-white-4xlight:hover { background-color: rgb(249, 250, 251) !important; }

.hover-bg-white-5xlight:hover { background-color: rgb(252, 252, 253) !important; }

.hover-bg-white-2xdark:hover { background-color: rgb(176, 177, 181) !important; }

.hover-bg-white-3xdark:hover { background-color: rgb(118, 119, 123) !important; }

.hover-bg-white-4xdark:hover { background-color: rgb(65, 66, 70) !important; }

.hover-bg-white-5xdark:hover { background-color: rgb(29, 31, 34) !important; }

/* ---------- Creative Transparency Variations ---------- */

.hover-bg-black-whisper:hover { background-color: rgba(3, 3, 4, 0.1) !important; }

.hover-bg-black-breath:hover { background-color: rgba(3, 3, 4, 0.2) !important; }

.hover-bg-black-mist:hover { background-color: rgba(3, 3, 4, 0.3) !important; }

.hover-bg-black-veil:hover { background-color: rgba(3, 3, 4, 0.4) !important; }

.hover-bg-black-shadow:hover { background-color: rgba(3, 3, 4, 0.5) !important; }

.hover-bg-black-shroud:hover { background-color: rgba(3, 3, 4, 0.6) !important; }

.hover-bg-black-cloak:hover { background-color: rgba(3, 3, 4, 0.7) !important; }

.hover-bg-black-eclipse:hover { background-color: rgba(3, 3, 4, 0.8) !important; }

.hover-bg-black-void:hover { background-color: rgba(3, 3, 4, 0.9) !important; }

.hover-bg-white-whisper:hover { background-color: rgba(237, 238, 242, 0.1) !important; }

.hover-bg-white-breath:hover { background-color: rgba(237, 238, 242, 0.2) !important; }

.hover-bg-white-mist:hover { background-color: rgba(237, 238, 242, 0.3) !important; }

.hover-bg-white-veil:hover { background-color: rgba(237, 238, 242, 0.4) !important; }

.hover-bg-white-shadow:hover { background-color: rgba(237, 238, 242, 0.5) !important; }

/* ---------- State Colors ---------- */

.hover-bg-success:hover { background-color: rgb(58, 168, 91) !important; }

.hover-bg-warning:hover { background-color: rgb(223, 161, 26) !important; }

.hover-bg-error:hover { background-color: rgb(215, 71, 69) !important; }

.hover-bg-info:hover { background-color: rgb(39, 132, 213) !important; }

/**
 * TEXT COLOR HOVER STATES
 *
 * These utilities apply text colors on hover
 */

.hover-text-primary:hover { color: rgb(39, 132, 213) !important; }

.hover-text-secondary:hover { color: rgb(81, 97, 145) !important; }

.hover-text-accent:hover { color: rgb(236, 125, 0) !important; color: color(display-p3 0.86926 0.51255 0.09455) !important; }

/* ========================================================================== */

/* FOCUS STATE UTILITIES                                                      */

/* ========================================================================== */

/**
 * BACKGROUND COLOR FOCUS STATES
 *
 * These utilities apply background colors on focus
 */

/* ---------- Brand Colors ---------- */

.focus-bg-primary:focus { background-color: rgb(39, 132, 213) !important; }

.focus-bg-secondary:focus { background-color: rgb(81, 97, 145) !important; }

.focus-bg-accent:focus { background-color: rgb(236, 125, 0) !important; background-color: color(display-p3 0.86926 0.51255 0.09455) !important; }

/* ---------- Primary Color Variations ---------- */

/* Tints - Lighter variations */

.focus-bg-primary-2xlight:focus { background-color: rgb(103, 170, 237) !important; }

.focus-bg-primary-3xlight:focus { background-color: rgb(145, 195, 246) !important; }

.focus-bg-primary-4xlight:focus { background-color: rgb(186, 219, 254) !important; }

.focus-bg-primary-5xlight:focus { background-color: rgb(221, 237, 255) !important; background-color: color(display-p3 0.87736 0.92848 0.9935) !important; }

/* Shades - Darker variations */

.focus-bg-primary-2xdark:focus { background-color: rgb(0, 94, 181) !important; background-color: color(display-p3 0.06248 0.36112 0.68706) !important; }

.focus-bg-primary-3xdark:focus { background-color: rgb(0, 60, 129) !important; background-color: color(display-p3 0 0.21727 0.54103) !important; }

.focus-bg-primary-4xdark:focus { background-color: rgb(0, 29, 78) !important; background-color: color(display-p3 0 0.10188 0.3246) !important; }

.focus-bg-primary-5xdark:focus { background-color: rgb(0, 8, 44) !important; background-color: color(display-p3 0 0.02366 0.17799) !important; }

/* ---------- Secondary Color Variations ---------- */

/* Tints - Lighter variations */

.focus-bg-secondary-2xlight:focus { background-color: rgb(128, 142, 183) !important; }

.focus-bg-secondary-3xlight:focus { background-color: rgb(162, 173, 205) !important; }

.focus-bg-secondary-4xlight:focus { background-color: rgb(197, 205, 227) !important; }

.focus-bg-secondary-5xlight:focus { background-color: rgb(226, 230, 241) !important; }

/* Shades - Darker variations */

.focus-bg-secondary-2xdark:focus { background-color: rgb(53, 68, 119) !important; }

.focus-bg-secondary-3xdark:focus { background-color: rgb(28, 40, 93) !important; }

.focus-bg-secondary-4xdark:focus { background-color: rgb(7, 10, 68) !important; }

.focus-bg-secondary-5xdark:focus { background-color: rgb(3, 0, 47) !important; background-color: color(display-p3 0.00631 0 0.17626) !important; }

/* ---------- Accent Color Variations ---------- */

/* Tints - Lighter variations */

.focus-bg-accent-2xlight:focus { background-color: rgb(253, 162, 81) !important; }

.focus-bg-accent-3xlight:focus { background-color: rgb(255, 189, 132) !important; background-color: color(display-p3 0.97138 0.75229 0.55196) !important; }

.focus-bg-accent-4xlight:focus { background-color: rgb(255, 215, 178) !important; background-color: color(display-p3 0.99752 0.84938 0.71576) !important; }

.focus-bg-accent-5xlight:focus { background-color: rgb(255, 235, 217) !important; background-color: color(display-p3 1 0.92547 0.85819) !important; }

/* Shades - Darker variations */

.focus-bg-accent-2xdark:focus { background-color: rgb(178, 89, 0) !important; background-color: color(display-p3 0.67601 0.3501 0) !important; }

.focus-bg-accent-3xdark:focus { background-color: rgb(122, 56, 0) !important; background-color: color(display-p3 0.46076 0.22297 0) !important; }

.focus-bg-accent-4xdark:focus { background-color: rgb(70, 27, 0) !important; background-color: color(display-p3 0.26237 0.10629 0) !important; }

.focus-bg-accent-5xdark:focus { background-color: rgb(35, 7, 0) !important; background-color: color(display-p3 0.12787 0.02868 0) !important; }

/* ---------- Grayscale ---------- */

.focus-bg-gray-100:focus { background-color: var(--mico-color-gray-100) !important; }

.focus-bg-gray-200:focus { background-color: var(--mico-color-gray-200) !important; }

.focus-bg-gray-300:focus { background-color: var(--mico-color-gray-300) !important; }

.focus-bg-gray-400:focus { background-color: var(--mico-color-gray-400) !important; }

.focus-bg-gray-500:focus { background-color: var(--mico-color-gray-500) !important; }

.focus-bg-gray-600:focus { background-color: var(--mico-color-gray-600) !important; }

.focus-bg-gray-700:focus { background-color: var(--mico-color-gray-700) !important; }

.focus-bg-gray-800:focus { background-color: var(--mico-color-gray-800) !important; }

.focus-bg-gray-900:focus { background-color: var(--mico-color-gray-900) !important; }

/* ---------- Black Scale ---------- */

.focus-bg-black-100:focus { background-color: var(--mico-color-black-100) !important; }

.focus-bg-black-200:focus { background-color: var(--mico-color-black-200) !important; }

.focus-bg-black-300:focus { background-color: var(--mico-color-black-300) !important; }

.focus-bg-black-400:focus { background-color: var(--mico-color-black-400) !important; }

.focus-bg-black-500:focus { background-color: var(--mico-color-black-500) !important; }

.focus-bg-black-600:focus { background-color: var(--mico-color-black-600) !important; }

.focus-bg-black-700:focus { background-color: var(--mico-color-black-700) !important; }

.focus-bg-black-800:focus { background-color: var(--mico-color-black-800) !important; }

.focus-bg-black-900:focus { background-color: var(--mico-color-black-900) !important; }

/* ---------- Transparency ---------- */

.focus-bg-black-trans-100:focus { background-color: var(--mico-color-black-trans-100) !important; }

.focus-bg-black-trans-200:focus { background-color: var(--mico-color-black-trans-200) !important; }

.focus-bg-black-trans-300:focus { background-color: var(--mico-color-black-trans-300) !important; }

.focus-bg-black-trans-400:focus { background-color: var(--mico-color-black-trans-400) !important; }

.focus-bg-black-trans-500:focus { background-color: var(--mico-color-black-trans-500) !important; }

.focus-bg-black-trans-600:focus { background-color: var(--mico-color-black-trans-600) !important; }

.focus-bg-black-trans-700:focus { background-color: var(--mico-color-black-trans-700) !important; }

.focus-bg-black-trans-800:focus { background-color: var(--mico-color-black-trans-800) !important; }

.focus-bg-black-trans-900:focus { background-color: var(--mico-color-black-trans-900) !important; }

/* ---------- State Colors ---------- */

.focus-bg-success:focus { background-color: rgb(58, 168, 91) !important; }

.focus-bg-warning:focus { background-color: rgb(223, 161, 26) !important; }

.focus-bg-error:focus { background-color: rgb(215, 71, 69) !important; }

.focus-bg-info:focus { background-color: rgb(39, 132, 213) !important; }

/**
 * TEXT COLOR FOCUS STATES
 *
 * These utilities apply text colors on focus
 */

.focus-text-primary:focus { color: rgb(39, 132, 213) !important; }

.focus-text-secondary:focus { color: rgb(81, 97, 145) !important; }

.focus-text-accent:focus { color: rgb(236, 125, 0) !important; color: color(display-p3 0.86926 0.51255 0.09455) !important; }

/* ========================================================================== */

/* ACTIVE STATE UTILITIES                                                     */

/* ========================================================================== */

/**
 * BACKGROUND COLOR ACTIVE STATES
 *
 * These utilities apply background colors on active (pressed) state
 */

/* ---------- Brand Colors ---------- */

.active-bg-primary:active { background-color: rgb(39, 132, 213) !important; }

.active-bg-secondary:active { background-color: rgb(81, 97, 145) !important; }

.active-bg-accent:active { background-color: rgb(236, 125, 0) !important; background-color: color(display-p3 0.86926 0.51255 0.09455) !important; }

/* ---------- Primary Color Variations ---------- */

/* Tints - Lighter variations */

.active-bg-primary-light:active { background-color: var(--mico-color-primary-light) !important; }

.active-bg-primary-2xlight:active { background-color: rgb(103, 170, 237) !important; }

.active-bg-primary-3xlight:active { background-color: rgb(145, 195, 246) !important; }

.active-bg-primary-4xlight:active { background-color: rgb(186, 219, 254) !important; }

.active-bg-primary-5xlight:active { background-color: rgb(221, 237, 255) !important; background-color: color(display-p3 0.87736 0.92848 0.9935) !important; }

/* Shades - Darker variations */

.active-bg-primary-dark:active { background-color: var(--mico-color-primary-dark) !important; }

.active-bg-primary-2xdark:active { background-color: rgb(0, 94, 181) !important; background-color: color(display-p3 0.06248 0.36112 0.68706) !important; }

.active-bg-primary-3xdark:active { background-color: rgb(0, 60, 129) !important; background-color: color(display-p3 0 0.21727 0.54103) !important; }

.active-bg-primary-4xdark:active { background-color: rgb(0, 29, 78) !important; background-color: color(display-p3 0 0.10188 0.3246) !important; }

.active-bg-primary-5xdark:active { background-color: rgb(0, 8, 44) !important; background-color: color(display-p3 0 0.02366 0.17799) !important; }

/* ---------- Secondary Color Variations ---------- */

/* Tints - Lighter variations */

.active-bg-secondary-light:active { background-color: var(--mico-color-secondary-light) !important; }

.active-bg-secondary-2xlight:active { background-color: rgb(128, 142, 183) !important; }

.active-bg-secondary-3xlight:active { background-color: rgb(162, 173, 205) !important; }

.active-bg-secondary-4xlight:active { background-color: rgb(197, 205, 227) !important; }

.active-bg-secondary-5xlight:active { background-color: rgb(226, 230, 241) !important; }

/* Shades - Darker variations */

.active-bg-secondary-dark:active { background-color: var(--mico-color-secondary-dark) !important; }

.active-bg-secondary-2xdark:active { background-color: rgb(53, 68, 119) !important; }

.active-bg-secondary-3xdark:active { background-color: rgb(28, 40, 93) !important; }

.active-bg-secondary-4xdark:active { background-color: rgb(7, 10, 68) !important; }

.active-bg-secondary-5xdark:active { background-color: rgb(3, 0, 47) !important; background-color: color(display-p3 0.00631 0 0.17626) !important; }

/* ---------- Accent Color Variations ---------- */

/* Tints - Lighter variations */

.active-bg-accent-light:active { background-color: var(--mico-color-accent-light) !important; }

.active-bg-accent-2xlight:active { background-color: rgb(253, 162, 81) !important; }

.active-bg-accent-3xlight:active { background-color: rgb(255, 189, 132) !important; background-color: color(display-p3 0.97138 0.75229 0.55196) !important; }

.active-bg-accent-4xlight:active { background-color: rgb(255, 215, 178) !important; background-color: color(display-p3 0.99752 0.84938 0.71576) !important; }

.active-bg-accent-5xlight:active { background-color: rgb(255, 235, 217) !important; background-color: color(display-p3 1 0.92547 0.85819) !important; }

/* Shades - Darker variations */

.active-bg-accent-dark:active { background-color: var(--mico-color-accent-dark) !important; }

.active-bg-accent-2xdark:active { background-color: rgb(178, 89, 0) !important; background-color: color(display-p3 0.67601 0.3501 0) !important; }

.active-bg-accent-3xdark:active { background-color: rgb(122, 56, 0) !important; background-color: color(display-p3 0.46076 0.22297 0) !important; }

.active-bg-accent-4xdark:active { background-color: rgb(70, 27, 0) !important; background-color: color(display-p3 0.26237 0.10629 0) !important; }

.active-bg-accent-5xdark:active { background-color: rgb(35, 7, 0) !important; background-color: color(display-p3 0.12787 0.02868 0) !important; }

/* ========================================================================== */

/* BORDER STATE UTILITIES                                                    */

/* ========================================================================== */

/**
 * Border Color States
 *
 * These utilities change border colors on hover, focus, and active states.
 */

/* Hover Border Colors */

.hover-border-primary:hover { border-color: rgb(39, 132, 213) !important; }

.hover-border-secondary:hover { border-color: rgb(81, 97, 145) !important; }

.hover-border-accent:hover { border-color: rgb(236, 125, 0) !important; border-color: color(display-p3 0.86926 0.51255 0.09455) !important; }

.hover-border-success:hover { border-color: rgb(58, 168, 91) !important; }

.hover-border-error:hover { border-color: rgb(215, 71, 69) !important; }

.hover-border-warning:hover { border-color: rgb(223, 161, 26) !important; }

.hover-border-info:hover { border-color: rgb(39, 132, 213) !important; }

.hover-border-gray-300:hover { border-color: var(--mico-color-gray-300) !important; }

.hover-border-gray-500:hover { border-color: var(--mico-color-gray-500) !important; }

.hover-border-gray-700:hover { border-color: var(--mico-color-gray-700) !important; }

/* Focus Border Colors */

.focus-border-primary:focus { border-color: rgb(39, 132, 213) !important; }

.focus-border-secondary:focus { border-color: rgb(81, 97, 145) !important; }

.focus-border-accent:focus { border-color: rgb(236, 125, 0) !important; border-color: color(display-p3 0.86926 0.51255 0.09455) !important; }

.focus-border-success:focus { border-color: rgb(58, 168, 91) !important; }

.focus-border-error:focus { border-color: rgb(215, 71, 69) !important; }

.focus-border-warning:focus { border-color: rgb(223, 161, 26) !important; }

.focus-border-info:focus { border-color: rgb(39, 132, 213) !important; }

/* Active Border Colors */

.active-border-primary:active { border-color: rgb(0, 94, 181) !important; border-color: color(display-p3 0.06248 0.36112 0.68706) !important; }

.active-border-secondary:active { border-color: rgb(53, 68, 119) !important; }

.active-border-accent:active { border-color: rgb(178, 89, 0) !important; border-color: color(display-p3 0.67601 0.3501 0) !important; }

/* ========================================================================== */

/* SHADOW STATE UTILITIES                                                    */

/* ========================================================================== */

/**
 * Shadow States
 *
 * These utilities change box shadows on hover, focus, and active states.
 */

/* Hover Shadows */

.shadow-primary:hover { box-shadow: 0px 4.5px 4px -3.8px rgb(48, 131, 205) !important; }

.shadow-secondary:hover { box-shadow: 0px 4.5px 4px -3.8px rgb(85, 98, 142) !important; }

.shadow-accent:hover { box-shadow: 0px 4.5px 4px -3.8px rgb(222, 122, 0) !important; box-shadow: 0px 4.5px 4px -3.8px color(display-p3 0.8198 0.499 0.1369) !important; }

.hover-shadow-none:hover { box-shadow: none !important; }

.hover-shadow-light:hover { box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05) !important; }

.hover-shadow-dark:hover { box-shadow: var(--mico-shadow-xs-dark) !important; }

.hover-shadow-primary:hover { box-shadow: 0px 4.5px 4px -3.8px rgb(48, 131, 205) !important; }

.hover-shadow-secondary:hover { box-shadow: 0px 4.5px 4px -3.8px rgb(85, 98, 142) !important; }

.hover-shadow-accent:hover { box-shadow: 0px 4.5px 4px -3.8px rgb(222, 122, 0) !important; box-shadow: 0px 4.5px 4px -3.8px color(display-p3 0.8198 0.499 0.1369) !important; }

/* Focus Shadows */

.shadow-primary:focus { box-shadow: 0px 4.6px 4px -3.8px rgb(57, 131, 196) !important; }

.shadow-secondary:focus { box-shadow: 0px 4.6px 4px -3.8px rgb(88, 100, 139) !important; }

.shadow-accent:focus { box-shadow: 0px 4.6px 4px -3.8px rgb(209, 119, 0) !important; box-shadow: 0px 4.6px 4px -3.8px color(display-p3 0.77044 0.48557 0.15436) !important; }

.focus-shadow-none:focus { box-shadow: none !important; }

.focus-shadow-light:focus { box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05) !important; }

.focus-shadow-dark:focus { box-shadow: var(--mico-shadow-sm-dark) !important; }

.focus-shadow-primary:focus { box-shadow: 0px 4.6px 4px -3.8px rgb(57, 131, 196) !important; }

.focus-shadow-secondary:focus { box-shadow: 0px 4.6px 4px -3.8px rgb(88, 100, 139) !important; }

.focus-shadow-accent:focus { box-shadow: 0px 4.6px 4px -3.8px rgb(209, 119, 0) !important; box-shadow: 0px 4.6px 4px -3.8px color(display-p3 0.77044 0.48557 0.15436) !important; }

/* Active Shadows */

.shadow-primary:active { box-shadow: 0px 4.8px 4px -2.8px rgb(66, 130, 187) !important; }

.shadow-secondary:active { box-shadow: 0px 4.8px 4px -2.8px rgb(91, 102, 136) !important; }

.shadow-accent:active { box-shadow: 0px 4.8px 4px -2.8px rgb(195, 117, 1) !important; }

.active-shadow-none:active { box-shadow: none !important; }

.active-shadow-light:active { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important; }

.active-shadow-dark:active { box-shadow: var(--mico-shadow-md-dark) !important; }

.active-shadow-primary:active { box-shadow: 0px 4.8px 4px -2.8px rgb(66, 130, 187) !important; }

.active-shadow-secondary:active { box-shadow: 0px 4.8px 4px -2.8px rgb(91, 102, 136) !important; }

.active-shadow-accent:active { box-shadow: 0px 4.8px 4px -2.8px rgb(195, 117, 1) !important; }

/* ========================================================================== */

/* TRANSFORM STATE UTILITIES                                                 */

/* ========================================================================== */

/**
 * Transform States
 *
 * These utilities apply transforms on hover, focus, and active states.
 */

/* Hover Effects */

.hover-translate-up-xs:hover { transform: translateY(-1.8px); }

.hover-translate-up-sm:hover { transform: translateY(-2.5px); }

.hover-translate-up-md:hover { transform: translateY(-3.8px); }

.hover-translate-up-lg:hover { transform: translateY(-4.8px); }

.hover-translate-up-xl:hover { transform: translateY(-5.8px); }

.hover-translate-down-xs:hover { transform: translateY(1.8px); }

.hover-translate-down-sm:hover { transform: translateY(2.5px); }

.hover-translate-down-md:hover { transform: translateY(3.8px); }

.hover-translate-down-lg:hover { transform: translateY(4.8px); }

.hover-translate-down-xl:hover { transform: translateY(5.8px); }

.hover-scale-up-xs:hover { transform: scale(0.05); }

.hover-scale-up-sm:hover { transform: scale(1.05); }

.hover-scale-up-md:hover { transform: scale(2.05); }

.hover-scale-up-lg:hover { transform: scale(3.05); }

.hover-scale-up-xl:hover { transform: scale(4.05); }

.hover-scale-down-xs:hover { transform: scale(-0.05); }

.hover-scale-down-sm:hover { transform: scale(-1.05); }

.hover-scale-down-md:hover { transform: scale(-2.05); }

.hover-scale-down-lg:hover { transform: scale(-3.05); }

.hover-scale-down-xl:hover { transform: scale(-4.05); }

.hover-rotate-l-xs:hover { transform: rotate(1deg); }

.hover-rotate-l-sm:hover { transform: rotate(3deg); }

.hover-rotate-l-md:hover { transform: rotate(6deg); }

.hover-rotate-l-lg:hover { transform: rotate(9deg); }

.hover-rotate-l-xl:hover { transform: rotate(12deg); }

.hover-rotate-r-xs:hover { transform: rotate(1deg); }

.hover-rotate-r-sm:hover { transform: rotate(3deg); }

.hover-rotate-r-md:hover { transform: rotate(6deg); }

.hover-rotate-r-lg:hover { transform: rotate(9deg); }

.hover-rotate-r-xl:hover { transform: rotate(12deg); }

.hover-rotate-l-25:hover { transform: rotate(25deg); }

.hover-rotate-l-45:hover { transform: rotate(45deg); }

.hover-rotate-l-90:hover { transform: rotate(90deg); }

.hover-rotate-l-180:hover { transform: rotate(180deg); }

.hover-rotate-l-360:hover { transform: rotate(360deg); }

.hover-rotate-r-25:hover { transform: rotate(25deg); }

.hover-rotate-r-45:hover { transform: rotate(45deg); }

.hover-rotate-r-90:hover { transform: rotate(90deg); }

.hover-rotate-r-180:hover { transform: rotate(180deg); }

.hover-rotate-r-360:hover { transform: rotate(360deg); }

/* Focus Effects */

.focus-translate-up-xs:focus { transform: translateY(-1.8px); }

.focus-translate-up-sm:focus { transform: translateY(-2.5px); }

.focus-translate-up-md:focus { transform: translateY(-3.8px); }

.focus-translate-up-lg:focus { transform: translateY(-4.8px); }

.focus-translate-up-xl:focus { transform: translateY(-5.8px); }

.focus-translate-down-xs:focus { transform: translateY(1.8px); }

.focus-translate-down-sm:focus { transform: translateY(2.5px); }

.focus-translate-down-md:focus { transform: translateY(3.8px); }

.focus-translate-down-lg:focus { transform: translateY(4.8px); }

.focus-translate-down-xl:focus { transform: translateY(5.8px); }

.focus-scale-up-xs:focus { transform: scale(0.05); }

.focus-scale-up-sm:focus { transform: scale(1.05); }

.focus-scale-up-md:focus { transform: scale(2.05); }

.focus-scale-up-lg:focus { transform: scale(3.05); }

.focus-scale-up-xl:focus { transform: scale(4.05); }

.focus-scale-down-xs:focus { transform: scale(-0.05); }

.focus-scale-down-sm:focus { transform: scale(-1.05); }

.focus-scale-down-md:focus { transform: scale(-2.05); }

.focus-scale-down-lg:focus { transform: scale(-3.05); }

.focus-scale-down-xl:focus { transform: scale(-4.05); }

.focus-rotate-l-xs:focus { transform: rotate(1deg); }

.focus-rotate-l-sm:focus { transform: rotate(3deg); }

.focus-rotate-l-md:focus { transform: rotate(6deg); }

.focus-rotate-l-lg:focus { transform: rotate(9deg); }

.focus-rotate-l-xl:focus { transform: rotate(12deg); }

.focus-rotate-r-xs:focus { transform: rotate(1deg); }

.focus-rotate-r-sm:focus { transform: rotate(3deg); }

.focus-rotate-r-md:focus { transform: rotate(6deg); }

.focus-rotate-r-lg:focus { transform: rotate(9deg); }

.focus-rotate-r-xl:focus { transform: rotate(12deg); }

.focus-rotate-l-25:focus { transform: rotate(25deg); }

.focus-rotate-l-45:focus { transform: rotate(45deg); }

.focus-rotate-l-90:focus { transform: rotate(90deg); }

.focus-rotate-l-180:focus { transform: rotate(180deg); }

.focus-rotate-l-360:focus { transform: rotate(360deg); }

.focus-rotate-r-25:focus { transform: rotate(25deg); }

.focus-rotate-r-45:focus { transform: rotate(45deg); }

.focus-rotate-r-90:focus { transform: rotate(90deg); }

.focus-rotate-r-180:focus { transform: rotate(180deg); }

.focus-rotate-r-360:focus { transform: rotate(360deg); }

/* Active Effects */

.active-translate-up-xs:active { transform: translateY(-1.8px); }

.active-translate-up-sm:active { transform: translateY(-2.5px); }

.active-translate-up-md:active { transform: translateY(-3.8px); }

.active-translate-up-lg:active { transform: translateY(-4.8px); }

.active-translate-up-xl:active { transform: translateY(-5.8px); }

.active-translate-down-xs:active { transform: translateY(1.8px); }

.active-translate-down-sm:active { transform: translateY(2.5px); }

.active-translate-down-md:active { transform: translateY(3.8px); }

.active-translate-down-lg:active { transform: translateY(4.8px); }

.active-translate-down-xl:active { transform: translateY(5.8px); }

.active-scale-up-xs:active { transform: scale(0.05); }

.active-scale-up-sm:active { transform: scale(1.05); }

.active-scale-up-md:active { transform: scale(2.05); }

.active-scale-up-lg:active { transform: scale(3.05); }

.active-scale-up-xl:active { transform: scale(4.05); }

.active-scale-down-xs:active { transform: scale(-0.05); }

.active-scale-down-sm:active { transform: scale(-1.05); }

.active-scale-down-md:active { transform: scale(-2.05); }

.active-scale-down-lg:active { transform: scale(-3.05); }

.active-scale-down-xl:active { transform: scale(-4.05); }

.active-rotate-l-xs:active { transform: rotate(1deg); }

.active-rotate-l-sm:active { transform: rotate(3deg); }

.active-rotate-l-md:active { transform: rotate(6deg); }

.active-rotate-l-lg:active { transform: rotate(9deg); }

.active-rotate-l-xl:active { transform: rotate(12deg); }

.active-rotate-r-xs:active { transform: rotate(1deg); }

.active-rotate-r-sm:active { transform: rotate(3deg); }

.active-rotate-r-md:active { transform: rotate(6deg); }

.active-rotate-r-lg:active { transform: rotate(9deg); }

.active-rotate-r-xl:active { transform: rotate(12deg); }

.active-rotate-l-25:active { transform: rotate(25deg); }

.active-rotate-l-45:active { transform: rotate(45deg); }

.active-rotate-l-90:active { transform: rotate(90deg); }

.active-rotate-l-180:active { transform: rotate(180deg); }

.active-rotate-l-360:active { transform: rotate(360deg); }

.active-rotate-r-25:active { transform: rotate(25deg); }

.active-rotate-r-45:active { transform: rotate(45deg); }

.active-rotate-r-90:active { transform: rotate(90deg); }

.active-rotate-r-180:active { transform: rotate(180deg); }

.active-rotate-r-360:active { transform: rotate(360deg); }

/* ========================================================================== */

/* OPACITY STATE UTILITIES                                                   */

/* ========================================================================== */

/**
 * Opacity States
 *
 * These utilities change opacity on hover, focus, and active states.
 */

/* Hover Opacity */

.hover-opacity-0p,.hover-opacity-none:hover { opacity: 0 !important; }

.hover-opacity-10p:hover { opacity: 0.10 !important; }

.hover-opacity-20p:hover { opacity: 0.20 !important; }

.hover-opacity-30p:hover { opacity: 0.30 !important; }

.hover-opacity-50p:hover { opacity: 0.40 !important; }

.hover-opacity-50p:hover { opacity: 0.50 !important; }

.hover-opacity-60p:hover { opacity: 0.60 !important; }

.hover-opacity-70p:hover { opacity: 0.70 !important; }

.hover-opacity-80p:hover { opacity: 0.80 !important; }

.hover-opacity-90p:hover { opacity: 0.90 !important; }

.hover-opacity-100p:hover { opacity: 1 !important; }

/* Focus Opacity */

.focus-opacity-0p,.focus-opacity-none:focus { opacity: 0 !important; }

.focus-opacity-10p:focus { opacity: 0.10 !important; }

.focus-opacity-20p:focus { opacity: 0.20 !important; }

.focus-opacity-30p:focus { opacity: 0.30 !important; }

.focus-opacity-50p:focus { opacity: 0.40 !important; }

.focus-opacity-50p:focus { opacity: 0.50 !important; }

.focus-opacity-60p:focus { opacity: 0.60 !important; }

.focus-opacity-70p:focus { opacity: 0.70 !important; }

.focus-opacity-80p:focus { opacity: 0.80 !important; }

.focus-opacity-90p:focus { opacity: 0.90 !important; }

.focus-opacity-100p:focus { opacity: 1 !important; }

/* Active Opacity */

.active-opacity-0p,.active-opacity-none:active { opacity: 0 !important; }

.active-opacity-10p:active { opacity: 0.10 !important; }

.active-opacity-20p:active { opacity: 0.20 !important; }

.active-opacity-30p:active { opacity: 0.30 !important; }

.active-opacity-50p:active { opacity: 0.40 !important; }

.active-opacity-50p:active { opacity: 0.50 !important; }

.active-opacity-60p:active { opacity: 0.60 !important; }

.active-opacity-70p:active { opacity: 0.70 !important; }

.active-opacity-80p:active { opacity: 0.80 !important; }

.active-opacity-90p:active { opacity: 0.90 !important; }

.active-opacity-100p:active { opacity: 1 !important; }

/* ========================================================================== */

/* RING STATE UTILITIES                                                      */

/* ========================================================================== */

/* ---------- Grayscale ---------- */

.active-bg-gray-100:active { background-color: var(--mico-color-gray-100) !important; }

.active-bg-gray-200:active { background-color: var(--mico-color-gray-200) !important; }

.active-bg-gray-300:active { background-color: var(--mico-color-gray-300) !important; }

.active-bg-gray-400:active { background-color: var(--mico-color-gray-400) !important; }

.active-bg-gray-500:active { background-color: var(--mico-color-gray-500) !important; }

.active-bg-gray-600:active { background-color: var(--mico-color-gray-600) !important; }

.active-bg-gray-700:active { background-color: var(--mico-color-gray-700) !important; }

.active-bg-gray-800:active { background-color: var(--mico-color-gray-800) !important; }

.active-bg-gray-900:active { background-color: var(--mico-color-gray-900) !important; }

/* ---------- Black Scale ---------- */

.active-bg-black-100:active { background-color: var(--mico-color-black-100) !important; }

.active-bg-black-200:active { background-color: var(--mico-color-black-200) !important; }

.active-bg-black-300:active { background-color: var(--mico-color-black-300) !important; }

.active-bg-black-400:active { background-color: var(--mico-color-black-400) !important; }

.active-bg-black-500:active { background-color: var(--mico-color-black-500) !important; }

.active-bg-black-600:active { background-color: var(--mico-color-black-600) !important; }

.active-bg-black-700:active { background-color: var(--mico-color-black-700) !important; }

.active-bg-black-800:active { background-color: var(--mico-color-black-800) !important; }

.active-bg-black-900:active { background-color: var(--mico-color-black-900) !important; }

/* ---------- Transparency ---------- */

.active-bg-black-trans-100:active { background-color: var(--mico-color-black-trans-100) !important; }

.active-bg-black-trans-200:active { background-color: var(--mico-color-black-trans-200) !important; }

.active-bg-black-trans-300:active { background-color: var(--mico-color-black-trans-300) !important; }

.active-bg-black-trans-400:active { background-color: var(--mico-color-black-trans-400) !important; }

.active-bg-black-trans-500:active { background-color: var(--mico-color-black-trans-500) !important; }

.active-bg-black-trans-600:active { background-color: var(--mico-color-black-trans-600) !important; }

.active-bg-black-trans-700:active { background-color: var(--mico-color-black-trans-700) !important; }

.active-bg-black-trans-800:active { background-color: var(--mico-color-black-trans-800) !important; }

.active-bg-black-trans-900:active { background-color: var(--mico-color-black-trans-900) !important; }

/* ---------- State Colors ---------- */

.active-bg-success:active { background-color: rgb(58, 168, 91) !important; }

.active-bg-warning:active { background-color: rgb(223, 161, 26) !important; }

.active-bg-error:active { background-color: rgb(215, 71, 69) !important; }

.active-bg-info:active { background-color: rgb(39, 132, 213) !important; }

/**
 * TEXT COLOR ACTIVE STATES
 *
 * These utilities apply text colors on active state
 */

.active-text-primary:active { color: rgb(39, 132, 213) !important; }

.active-text-secondary:active { color: rgb(81, 97, 145) !important; }

.active-text-accent:active { color: rgb(236, 125, 0) !important; color: color(display-p3 0.86926 0.51255 0.09455) !important; }

/* ========================================================================== */

/* ADDITIONAL INTERACTIVE UTILITIES                                           */

/* ========================================================================== */

/**
 * OPACITY HOVER EFFECTS
 *
 * These utilities apply opacity changes on hover
 */

/**
 * FILTER HOVER EFFECTS
 *
 * These utilities apply filter effects on hover
 */

.hover-blur:hover { filter: blur(4px) !important; }

.hover-brightness-110:hover { filter: brightness(1.1) !important; }

.hover-brightness-125:hover { filter: brightness(1.25) !important; }

.hover-grayscale:hover { filter: grayscale(100%) !important; }

.hover-sepia:hover { filter: sepia(100%) !important; }

/* Complete hover effect for opacity, transforms*/

/* ========================================================================== */

/* USAGE EXAMPLES AND DOCUMENTATION                                           */

/* ========================================================================== */

/**
 * HOW TO USE INTERACTIVE STATE UTILITIES
 *
 * These utilities can be combined to create rich interactive experiences.
 * Here are some common patterns:
 *
 * 1. Basic hover effect for buttons:
 *    <button class="hover-bg-primary hover-text-white">Button</button>
 *
 * 2. Elevated card on hover:
 *    <div class="hover-elevate hover-bg-primary">Card content</div>
 *
 * 3. Interactive link:
 *    <a class="hover-text-primary focus-text-primary active-text-primary">Link</a>
 *
 * 4. Subtle image hover effect:
 *    <img class="hover-brightness-110 hover-scale-sm" src="image.jpg" alt="Image">
 *
 * 5. Button with multiple states:
 *    <button class="hover-bg-primary focus-bg-primary active-bg-primary">Button</button>
 */

/**
 * Mico CSS Framework - Animation & Motion Utilities
 *
 * This file provides a comprehensive animation engine with:
 * - Transition utilities for smooth property changes
 * - Scroll-triggered entrance animations
 * - Continuous animations for ongoing effects
 * - Interactive animations (ripple, typewriter)
 * - Animation control utilities
 *
 * USAGE:
 * Basic transitions: .transition-all, .transition-colors
 * Scroll animations: .animate-on-scroll .anim-fade-in
 * Continuous: .anim-pulse, .anim-subtle-bounce
 * Interactive: .btn-ripple, .anim-typewriter
 */

/* ========================================================================== */

/* TRANSITION UTILITIES                                                       */

/* ========================================================================== */

/**
 * Transition Property Utilities
 *
 * These utilities control which CSS properties are animated during transitions.
 * They provide smooth animations for interactive states like hover and focus.
 */

/* Property-specific transitions */

.transition-none {
  transition-property: none !important;
}

.transition-all {
  transition-property: all !important;
}

.transition-all {
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.42, 0, 0.58, 1.0);
  transition-delay: 0;
}

.transition-colors {
  transition-property: background-color, border-color, color, fill, stroke, text-decoration-color !important;
}

.transition-colors {
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.42, 0, 0.58, 1.0);
  transition-delay: 0;
}

.transition-opacity {
  transition-property: opacity !important;
}

.transition-opacity {
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.42, 0, 0.58, 1.0);
  transition-delay: 0;
}

.transition-transform {
  transition-property: transform !important;
}

.transition-transform {
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.42, 0, 0.58, 1.0);
  transition-delay: 0;
}

.transition-shadow {
  transition-property: box-shadow, filter !important;
}

.transition-shadow {
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.42, 0, 0.58, 1.0);
  transition-delay: 0;
}

.transition-background {
  transition-property: background-color, background-image, background-position, background-size !important;
}

.transition-background {
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.42, 0, 0.58, 1.0);
  transition-delay: 0;
}

.transition-border {
  transition-property: border-color, border-width, border-style !important;
}

.transition-border {
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.42, 0, 0.58, 1.0);
  transition-delay: 0;
}

/**
 * Transition Duration Utilities
 *
 * These utilities override the default transition duration.
 */

.duration-0    { transition-duration: 0ms !important; }

.duration-75   { transition-duration: 75ms !important; }

.duration-100  { transition-duration: 100ms !important; }

.duration-150  { transition-duration: 150ms !important; }

.duration-200  { transition-duration: 200ms !important; }

.duration-300  { transition-duration: 300ms !important; }

.duration-500  { transition-duration: 500ms !important; }

.duration-700  { transition-duration: 700ms !important; }

.duration-1000 { transition-duration: 1000ms !important; }

/**
 * Transition Timing Function Utilities
 *
 * These utilities control the acceleration curve of transitions.
 */

.ease-linear  { transition-timing-function: linear !important; }

.ease-in      { transition-timing-function: cubic-bezier(0.42, 0, 1.0, 1.0) !important; }

.ease-out     { transition-timing-function: cubic-bezier(0, 0, 0.58, 1.0) !important; }

.ease-in-out  { transition-timing-function: cubic-bezier(0.42, 0, 0.58, 1.0) !important; }

/**
 * Transition Delay Utilities
 *
 * These utilities add delays before transitions start.
 */

.delay-0    { transition-delay: 0ms !important; }

.delay-75   { transition-delay: 75ms !important; }

.delay-100  { transition-delay: 100ms !important; }

.delay-150  { transition-delay: 150ms !important; }

.delay-200  { transition-delay: 200ms !important; }

.delay-300  { transition-delay: 300ms !important; }

.delay-500  { transition-delay: 500ms !important; }

.delay-700  { transition-delay: 700ms !important; }

.delay-1000 { transition-delay: 1000ms !important; }

/* ========================================================================== */

/* ANIMATION ENGINE BASE & KEYFRAMES                                         */

/* ========================================================================== */

/**
 * Animation Engine Base Classes
 *
 * These classes provide the foundation for scroll-triggered animations.
 * Elements with .animate-on-scroll will be hidden initially and animated
 * when they come into view via JavaScript intersection observer.
 */

.animate-on-scroll {
  opacity: 0;
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
  will-change: opacity, transform;
}

.animate-on-scroll.is-visible {
  opacity: 1;
  transform: none; /* Reset initial transforms */
}

/* No-JS Fallback - Show all animations if JavaScript is disabled */

.no-js .animate-on-scroll {
  opacity: 1;
  transform: none;
}

/**
 * Animation Keyframes
 *
 * These keyframes define the actual animation movements.
 * They are applied to elements when the .is-visible class is added.
 */

/* Fade Animations */

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translate3d(0, 40px, 0); }
  to { opacity: 1; transform: translate3d(0, 0, 0); }
}

@keyframes fadeInDown {
  from { opacity: 0; transform: translate3d(0, -40px, 0); }
  to { opacity: 1; transform: translate3d(0, 0, 0); }
}

@keyframes fadeInLeft {
  from { opacity: 0; transform: translate3d(-40px, 0, 0); }
  to { opacity: 1; transform: translate3d(0, 0, 0); }
}

@keyframes fadeInRight {
  from { opacity: 0; transform: translate3d(40px, 0, 0); }
  to { opacity: 1; transform: translate3d(0, 0, 0); }
}

/* Slide Animations */

@keyframes slideInUp {
  from { transform: translate3d(0, 100%, 0); opacity: 0; visibility: visible; }
  to { transform: translate3d(0, 0, 0); opacity: 1; }
}

@keyframes slideInDown {
  from { transform: translate3d(0, -100%, 0); opacity: 0; visibility: visible; }
  to { transform: translate3d(0, 0, 0); opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translate3d(-100%, 0, 0); opacity: 0; visibility: visible; }
  to { transform: translate3d(0, 0, 0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translate3d(100%, 0, 0); opacity: 0; visibility: visible; }
  to { transform: translate3d(0, 0, 0); opacity: 1; }
}

/* Scale and Rotate Animations */

@keyframes scaleIn {
  from { opacity: 0; transform: scale3d(0.3, 0.3, 0.3); }
  to { opacity: 1; transform: scale3d(1, 1, 1); }
}

@keyframes rotateIn {
  from { opacity: 0; transform: rotate3d(0, 0, 1, -180deg); }
  to { opacity: 1; transform: rotate3d(0, 0, 1, 0deg); }
}

/* Continuous Animation Keyframes */

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes subtleBounce {
  0%, 100% { transform: translateY(0); }
  40% { transform: translateY(-8px); }
  60% { transform: translateY(-4px); }
}

@keyframes subtleFloat {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* Interactive Animation Keyframes */

@keyframes ripple-effect {
  to { transform: scale(4); opacity: 0; }
}

@keyframes blink-caret {
  from, to { opacity: 1; }
  50% { opacity: 0; }
}

/* ========================================================================== */

/* SCROLL-TRIGGERED ANIMATION UTILITIES                                      */

/* ========================================================================== */

/**
 * Entrance Animation Classes
 *
 * These classes apply specific animations when the .is-visible class is added
 * by the JavaScript intersection observer. They must be combined with .animate-on-scroll.
 */

/* Fade Entrance Animations */

.anim-fade-in.is-visible { animation-name: fadeIn; }

.anim-fade-in-up.is-visible { animation-name: fadeInUp; }

.anim-fade-in-down.is-visible { animation-name: fadeInDown; }

.anim-fade-in-left.is-visible { animation-name: fadeInLeft; }

.anim-fade-in-right.is-visible { animation-name: fadeInRight; }

/* Slide Entrance Animations */

.anim-slide-in-up.is-visible { animation-name: slideInUp; }

.anim-slide-in-down.is-visible { animation-name: slideInDown; }

.anim-slide-in-left.is-visible { animation-name: slideInLeft; }

.anim-slide-in-right.is-visible { animation-name: slideInRight; }

/* Scale and Rotate Entrance Animations */

.anim-scale-in.is-visible { animation-name: scaleIn; }

.anim-rotate-in.is-visible { animation-name: rotateIn; }

/**
 * Common Properties for Entrance Animations
 *
 * These properties are applied to all entrance animations when they become visible.
 */

.animate-on-scroll[class*="anim-fade-in"].is-visible,
.animate-on-scroll[class*="anim-slide-in"].is-visible,
.animate-on-scroll.anim-scale-in.is-visible,
.animate-on-scroll.anim-rotate-in.is-visible {
  animation-duration: 0.8s;
  animation-timing-function: ease-out;
  animation-fill-mode: both;
}

/* ========================================================================== */

/* CONTINUOUS ANIMATION UTILITIES                                            */

/* ========================================================================== */

/**
 * Continuous Animation Classes
 *
 * These animations run continuously and don't require JavaScript.
 * They can be applied directly to elements.
 */

/* Continuous Animations */

.anim-pulse {
  animation: pulse 2s infinite ease-in-out;
}

.anim-subtle-bounce {
  animation: subtleBounce 2.5s infinite ease-in-out;
}

.anim-subtle-float {
  animation: subtleFloat 5 s infinite ease-in-out;
}

/* Apply default duration to continuous animations if not overridden */

.anim-pulse,
.anim-subtle-bounce,
.anim-subtle-float {
  animation-duration: 0.8s;
}

/* ========================================================================== */

/* ANIMATION CONTROL UTILITIES                                               */

/* ========================================================================== */

/**
 * Animation Duration Controls
 *
 * These utilities override the default animation duration.
 */

.anim-duration-xs   { animation-duration: 0.2s !important; }

.anim-duration-sm   { animation-duration: 0.5s !important; }

.anim-duration-md   { animation-duration: 0.8s !important; }

.anim-duration-lg   { animation-duration: 1.2s !important; }

.anim-duration-xl   { animation-duration: 2s !important; }

/**
 * Animation Delay Controls
 *
 * These utilities add delays before animations start.
 */

.anim-delay-xs   { animation-delay: 0.1s !important; }

.anim-delay-sm   { animation-delay: 0.2s !important; }

.anim-delay-md   { animation-delay: 0.4s !important; }

.anim-delay-lg   { animation-delay: 0.6s !important; }

.anim-delay-xl   { animation-delay: 0.8s !important; }

/**
 * Animation Timing Function Controls
 *
 * These utilities control the acceleration curve of animations.
 */

.anim-ease-linear  { animation-timing-function: linear !important; }

.anim-ease-in      { animation-timing-function: ease-in !important; }

.anim-ease-out     { animation-timing-function: ease-out !important; }

.anim-ease-in-out  { animation-timing-function: ease-in-out !important; }

/**
 * Animation Iteration Controls
 *
 * These utilities control how many times animations repeat.
 */

.anim-infinite   { animation-iteration-count: infinite !important; }

.anim-iter-1     { animation-iteration-count: 1 !important; }

.anim-iter-2     { animation-iteration-count: 2 !important; }

.anim-iter-3     { animation-iteration-count: 3 !important; }

/**
 * Animation Direction Controls
 *
 * These utilities control the direction of animation playback.
 */

.anim-direction-normal           { animation-direction: normal !important; }

.anim-direction-reverse          { animation-direction: reverse !important; }

.anim-direction-alternate        { animation-direction: alternate !important; }

.anim-direction-alternate-reverse{ animation-direction: alternate-reverse !important; }

/**
 * Animation Fill Mode Controls
 *
 * These utilities control how animations apply styles before and after execution.
 */

.anim-fill-none     { animation-fill-mode: none !important; }

.anim-fill-forwards { animation-fill-mode: forwards !important; }

.anim-fill-backwards{ animation-fill-mode: backwards !important; }

.anim-fill-both     { animation-fill-mode: both !important; }

/**
 * Animation Play State Controls
 *
 * These utilities control whether animations are running or paused.
 */

.anim-paused  { animation-play-state: paused !important; }

.anim-running { animation-play-state: running !important; }

/* ========================================================================== */

/* INTERACTIVE ANIMATION UTILITIES                                           */

/* ========================================================================== */

/**
 * Ripple Effect for Buttons
 *
 * Creates a ripple animation effect on button interactions.
 * JavaScript is required to position and trigger the ripple.
 */

.btn-ripple {
  position: relative;
  overflow: hidden;
  -webkit-tap-highlight-color: transparent;
}

.ripple-element {
  position: absolute;
  border-radius: 9999px;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple-effect 0.6s linear;
  pointer-events: none;
}

/* Dark mode ripple */

@media (prefers-color-scheme: dark) {
  .ripple-element {
    background: rgba(0, 0, 0, 0.2);
  }
}

/**
 * Typewriter Effect
 *
 * Creates a typewriter animation effect for text.
 * JavaScript is required to control the typing animation.
 */

.typewriter-container {
  display: inline-block;
}

.anim-typewriter {
  display: inline;
}

.typewriter-cursor::after {
  content: '|';
  display: inline-block;
  animation: blink-caret 0.75s step-end infinite;
  margin-left: 0.05em;
  color: currentColor;
}

/* JavaScript-managed cursor for more control */

.typewriter-cursor-managed {
  display: inline-block;
  margin-left: 0.05em;
  color: currentColor;
}

.typewriter-cursor-paused::after,
.typewriter-cursor-managed.is-paused {
  animation-play-state: paused !important;
  opacity: 1 !important;
}

/* ========================================================================== */

/* ACCESSIBILITY & PERFORMANCE                                               */

/* ========================================================================== */

/**
 * Reduced Motion Support
 *
 * Respects user preferences for reduced motion by disabling or simplifying animations.
 * This is crucial for accessibility and user comfort.
 */

@media (prefers-reduced-motion: reduce) {
  /* Disable all animations and transitions for users who prefer reduced motion */
  .animate-on-scroll,
  .animate-on-scroll.is-visible,
  [class*="anim-"] {
    animation-name: none !important;
    animation-duration: 0s !important;
    animation-play-state: paused !important;
    opacity: 1 !important;
    transform: none !important;
  }

  /* Disable interactive animations */
  .typewriter-cursor::after,
  .typewriter-cursor-managed {
    animation: none !important;
    opacity: 1 !important;
  }

  .ripple-element {
    animation-name: none !important;
  }

  /* Reduce transition durations to minimal values */
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/**
 * High Contrast Mode Support
 *
 * Ensures animations work well in high contrast mode.
 */

@media (prefers-contrast: high) {
  .ripple-element {
    background: currentColor;
    opacity: 0.3;
  }
}

/**
 * Print Media Support
 *
 * Disables animations in print media to avoid issues.
 */

@media print {
  .animate-on-scroll,
  [class*="anim-"] {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
  }
}

/* ========================================================================== */

/* USAGE EXAMPLES AND DOCUMENTATION                                          */

/* ========================================================================== */

/**
 * USAGE EXAMPLES:
 *
 * 1. Basic scroll-triggered fade in:
 *    <div class="animate-on-scroll anim-fade-in">Content</div>
 *
 * 2. Slide in from left with delay:
 *    <div class="animate-on-scroll anim-slide-in-left anim-delay-sm">Content</div>
 *
 * 3. Continuous pulse animation:
 *    <div class="anim-pulse">Pulsing content</div>
 *
 * 4. Button with ripple effect:
 *    <button class="btn btn-ripple">Click me</button>
 *
 * 5. Typewriter effect:
 *    <span class="anim-typewriter typewriter-cursor" data-type-text="Hello World!">
 *
 * 6. Custom animation timing:
 *    <div class="animate-on-scroll anim-fade-in anim-duration-lg anim-ease-out">
 *
 * JAVASCRIPT REQUIREMENTS:
 * - Intersection Observer for scroll-triggered animations
 * - Event handlers for ripple effects
 * - Text animation logic for typewriter effects
 */

/**
 * Mico CSS Framework - Miscellaneous Utilities
 *
 * This file provides various utility classes that don't fit into other categories
 * but are essential for modern web development. These utilities cover:
 *
 * - Accessibility helpers (skip links, screen reader utilities)
 * - Appearance control (form elements, browser defaults)
 * - Pointer events and user interaction
 * - Performance optimization hints
 * - Layout bleeding effects
 * - CSS masking and visual effects
 * - Browser compatibility helpers
 *
 * USAGE:
 * Accessibility: .skip-link, .sr-only
 * Appearance: .appearance-none, .appearance-auto
 * Interaction: .pointer-events-none, .user-select-none
 * Performance: .will-change-transform, .will-change-opacity
 * Layout: .bleed-full, .bleed-column
 * Effects: .mask-fade-bottom, .mask-fade-top
 */

/* ========================================================================== */

/* ACCESSIBILITY UTILITIES                                                   */

/* ========================================================================== */

/**
 * Skip Link
 *
 * Provides keyboard users a way to skip to main content.
 * Essential for accessibility compliance.
 */

.skip-link {
  position: absolute !important;
  top: calc((4px * 10) * -1) !important;
  left: var(--mico-size-6) !important;
  background: rgb(3, 3, 4) !important;
  color: rgb(237, 238, 242) !important;
  padding: calc(4px * 2) !important;
  -webkit-text-decoration: none !important;
  text-decoration: none !important;
  z-index: var(--mico-z-50) !important;
  border-radius: 2px !important;
  font-weight: 500 !important;
  transition: all .4s cubic-bezier(0.25, 0.1, 0.25, 1.0) !important;
}

.skip-link:focus {
  top: var(--mico-size-6) !important;
  outline: 2px solid rgb(39, 132, 213) !important;
  outline-offset: 2px !important;
}

/**
 * Screen Reader Only
 *
 * Hides content visually but keeps it available to screen readers.
 * Use for descriptive text that aids accessibility.
 */

.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: calc(1px * -1) !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/**
 * Not Screen Reader Only
 *
 * Reverses .sr-only for responsive or conditional visibility.
 */

.not-sr-only {
  position: static !important;
  width: auto !important;
  height: auto !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
}

/* ========================================================================== */

/* APPEARANCE CONTROL                                                        */

/* ========================================================================== */

/**
 * Appearance Utilities
 *
 * Controls the appearance property to remove or restore browser default styling.
 * Particularly useful for form elements and custom components.
 */

.appearance-none {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.appearance-auto {
  -webkit-appearance: auto;
  -moz-appearance: auto;
  appearance: auto;
}

/* ========================================================================== */

/* POINTER EVENTS & USER INTERACTION                                         */

/* ========================================================================== */

/**
 * Pointer Events Control
 *
 * Controls whether elements can be the target of pointer events.
 * Useful for overlays, disabled states, and complex layouts.
 */

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

/**
 * User Select Control
 *
 * Controls text selection behavior for better user experience.
 */

.user-select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.user-select-text {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
}

.user-select-all {
  -webkit-user-select: all;
  -moz-user-select: all;
  user-select: all;
}

.user-select-auto {
  -webkit-user-select: auto;
  -moz-user-select: auto;
  user-select: auto;
}

/* ========================================================================== */

/* PERFORMANCE OPTIMIZATION                                                  */

/* ========================================================================== */

/**
 * Will Change Utilities
 *
 * Provides performance hints to the browser about what properties will change.
 * Use sparingly as they can consume more resources if overused.
 */

.will-change-auto {
  will-change: auto;
}

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-scroll {
  will-change: scroll-position;
}

/* ========================================================================== */

/* LAYOUT BLEEDING EFFECTS                                                   */

/* ========================================================================== */

/**
 * Bleed Utilities
 *
 * Creates full-bleed and column-bleed effects that extend beyond container bounds.
 * Useful for hero sections, backgrounds, and visual emphasis.
 */

/* Full viewport bleed */

.bleed-full {
  width: 100vw;
  margin-left: calc(50% - 50vw);
  margin-right: calc(50% - 50vw);
}

/* Column bleed with configurable offsets */

.bleed-column {
  margin-left: calc(20vw * -1);
  margin-right: calc(20vw * -1);
}

.bleed-column-sm {
  margin-left: calc(10vw * -1);
  margin-right: calc(10vw * -1);
}

.bleed-column-lg {
  margin-left: calc(30vw * -1);
  margin-right: calc(30vw * -1);
}

/* Horizontal only bleed */

.bleed-x {
  margin-left: calc(20vw * -1);
  margin-right: calc(20vw * -1);
}

/* Vertical only bleed */

.bleed-y {
  margin-top: calc(20vw * -1);
  margin-bottom: calc(20vw * -1);
}

/* ========================================================================== */

/* CSS MASKING & VISUAL EFFECTS                                              */

/* ========================================================================== */

/**
 * Mask Utilities
 *
 * Applies CSS masks for fade effects and visual transitions.
 * Useful for creating smooth visual boundaries and focus effects.
 */

/* Directional fade masks */

.mask-fade-top {
  -webkit-mask-image: linear-gradient(to top, black 50%, transparent 100%);
  mask-image: linear-gradient(to top, black 50%, transparent 100%);
}

.mask-fade-bottom {
  -webkit-mask-image: linear-gradient(to bottom, black 50%, transparent 100%);
  mask-image: linear-gradient(to bottom, black 50%, transparent 100%);
}

.mask-fade-left {
  -webkit-mask-image: linear-gradient(to left, black 50%, transparent 100%);
  mask-image: linear-gradient(to left, black 50%, transparent 100%);
}

.mask-fade-right {
  -webkit-mask-image: linear-gradient(to right, black 50%, transparent 100%);
  mask-image: linear-gradient(to right, black 50%, transparent 100%);
}

/* Fade intensity variations */

.mask-fade-short {
  -webkit-mask-image: linear-gradient(to bottom, black 80%, transparent 100%);
  mask-image: linear-gradient(to bottom, black 80%, transparent 100%);
}

.mask-fade-long {
  -webkit-mask-image: linear-gradient(to bottom, black 20%, transparent 100%);
  mask-image: linear-gradient(to bottom, black 20%, transparent 100%);
}

/* Remove mask */

.mask-none {
  -webkit-mask-image: none;
  mask-image: none;
}

/* ========================================================================== */

/* BROWSER COMPATIBILITY HELPERS                                             */

/* ========================================================================== */

/**
 * Scrollbar Styling
 *
 * Provides consistent scrollbar styling across browsers.
 * Note: Limited support in Firefox.
 */

.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-none {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

/**
 * Webkit Scrollbar Styling
 *
 * Detailed scrollbar customization for Webkit browsers.
 */

.scrollbar-custom::-webkit-scrollbar {
  width: calc(4px * 3);
  height: calc(4px * 3);
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: rgb(229, 230, 231);
  border-radius: 4px;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background: rgb(141, 143, 148);
  border-radius: 4px;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background: rgb(97, 99, 105);
}

/**
 * Touch Action Control
 *
 * Controls touch behavior for better mobile experience.
 */

.touch-auto {
  touch-action: auto;
}

.touch-none {
  touch-action: none;
}

.touch-pan-x {
  touch-action: pan-x;
}

.touch-pan-y {
  touch-action: pan-y;
}

.touch-manipulation {
  touch-action: manipulation;
}

/* ========================================================================== */

/* ACCESSIBILITY & MEDIA QUERIES                                             */

/* ========================================================================== */

/**
 * High Contrast Mode Support
 *
 * Enhanced visibility for high contrast environments.
 */

@media (prefers-contrast: high) {
  .skip-link {
    border: 2px solid currentColor;
  }

  .mask-fade-top,
  .mask-fade-bottom,
  .mask-fade-left,
  .mask-fade-right,
  .mask-fade-short,
  .mask-fade-long {
    -webkit-mask-image: none;
    mask-image: none;
  }
}

/**
 * Reduced Motion Support
 *
 * Respects user preferences for reduced motion.
 */

@media (prefers-reduced-motion: reduce) {
  .skip-link {
    transition: none;
  }

  .will-change-transform,
  .will-change-opacity,
  .will-change-scroll {
    will-change: auto;
  }
}

/**
 * Print Media Support
 *
 * Optimized styles for print media.
 */

@media print {
  .skip-link {
    display: none;
  }

  .mask-fade-top,
  .mask-fade-bottom,
  .mask-fade-left,
  .mask-fade-right,
  .mask-fade-short,
  .mask-fade-long {
    -webkit-mask-image: none;
    mask-image: none;
  }

  .bleed-full,
  .bleed-column,
  .bleed-column-sm,
  .bleed-column-lg,
  .bleed-x,
  .bleed-y {
    margin: 0;
    width: auto;
  }
}

/* ========================================================================== */

/* USAGE EXAMPLES AND DOCUMENTATION                                          */

/* ========================================================================== */

/**
 * USAGE EXAMPLES:
 *
 * 1. Accessible skip link:
 *    <a href="#main" class="skip-link">Skip to main content</a>
 *
 * 2. Screen reader only text:
 *    <span class="sr-only">This text is only for screen readers</span>
 *
 * 3. Custom form element:
 *    <select class="appearance-none">...</select>
 *
 * 4. Disabled overlay:
 *    <div class="pointer-events-none">...</div>
 *
 * 5. Performance optimized element:
 *    <div class="will-change-transform">...</div>
 *
 * 6. Full bleed hero section:
 *    <section class="bleed-full">...</section>
 *
 * 7. Fade effect:
 *    <div class="mask-fade-bottom">...</div>
 *
 * 8. Custom scrollbar:
 *    <div class="scrollbar-custom overflow-auto">...</div>
 *
 * ACCESSIBILITY NOTES:
 * - Always include skip links for keyboard navigation
 * - Use .sr-only for descriptive text that aids screen readers
 * - Test with high contrast mode and reduced motion preferences
 * - Ensure touch targets are appropriately sized for mobile
 */

/* --mico-breakpoint-3xl: 1600px; Super large devices (wider screens) */

@media screen and (max-width: 1600px) {


  .text-left-3xl { text-align: left !important; }
  .text-center-3xl { text-align: center !important; }
  .text-right-3xl { text-align: right !important; }
  .text-justify-3xl { text-align: justify !important; }
  .text-start-3xl { text-align: left !important; }
  .text-end-3xl { text-align: right !important; }

  .mx-auto-3xl { margin-left: auto !important; margin-right: auto !important; }
  .my-auto-3xl { margin-top: auto !important; margin-bottom: auto !important; }

  .d-none-3xl { display: none !important; }
  .d-inline-3xl { display: inline !important; }
  .d-inline-block-3xl { display: inline-block !important; }
  .d-block-3xl { display: block !important; }
  .d-flex-3xl { display: flex !important; }
  .d-inline-flex-3xl { display: inline-flex !important; }
  .d-grid-3xl { display: grid !important; }

  .grid-cols-1-3xl { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-3xl { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-3xl { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-3xl { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-3xl { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-3xl { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-3xl { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-3xl { grid-column: span 1 !important; }
  .col-2-3xl { grid-column: span 2 !important; }
  .col-3-3xl { grid-column: span 3 !important; }
  .col-4-3xl { grid-column: span 4 !important; }
  .col-5-3xl { grid-column: span 5 !important; }
  .col-6-3xl { grid-column: span 6 !important; }
  .col-7-3xl { grid-column: span 7 !important; }
  .col-8-3xl { grid-column: span 8 !important; }
  .col-9-3xl { grid-column: span 9 !important; }
  .col-10-3xl { grid-column: span 10 !important; }
  .col-11-3xl { grid-column: span 11 !important; }
  .col-12-3xl { grid-column: span 12 !important; }


  /* Flexbox */
.flex-row-3xl { flex-direction: row !important; }
.flex-column-3xl { flex-direction: column !important; }
.flex-row-reverse-3xl { flex-direction: row-reverse !important; }
.flex-column-reverse-3xl { flex-direction: column-reverse !important; }


.flex-wrap-3xl { flex-wrap: wrap !important; }
.flex-nowrap-3xl { flex-wrap: nowrap !important; }
.flex-wrap-reverse-3xl { flex-wrap: wrap-reverse !important; }

.justify-content-start-3xl { justify-content: flex-start !important; }
.justify-content-end-3xl { justify-content: flex-end !important; }
.justify-content-center-3xl { justify-content: center !important; }
.justify-content-between-3xl { justify-content: space-between !important; }
.justify-content-around-3xl { justify-content: space-around !important; }
.justify-content-evenly-3xl { justify-content: space-evenly !important; }

.align-items-start-3xl { align-items: flex-start !important; }
.align-items-end-3xl { align-items: flex-end !important; }
.align-items-center-3xl { align-items: center !important; }
.align-items-baseline-3xl { align-items: baseline !important; }
.align-items-stretch-3xl { align-items: stretch !important; }

.align-content-start-3xl { align-content: flex-start !important; }
.align-content-end-3xl { align-content: flex-end !important; }
.align-content-center-3xl { align-content: center !important; }
.align-content-between-3xl { align-content: space-between !important; }
.align-content-around-3xl { align-content: space-around !important; }
.align-content-stretch-3xl { align-content: stretch !important; }

.align-self-auto-3xl { align-self: auto !important; }
.align-self-start-3xl { align-self: flex-start !important; }
.align-self-end-3xl { align-self: flex-end !important; }
.align-self-center-3xl { align-self: center !important; }
.align-self-baseline-3xl { align-self: baseline !important; }
.align-self-stretch-3xl { align-self: stretch !important; }

.flex-grow-0-3xl { flex-grow: 0 !important; }
.flex-grow-1-3xl { flex-grow: 1 !important; }
.flex-shrink-0-3xl { flex-shrink: 0 !important; }
.flex-shrink-1-3xl { flex-shrink: 1 !important; }

.flex-1-3xl { flex: 1 1 0% !important; }
.flex-auto-3xl { flex: 1 1 auto !important; }
.flex-initial-3xl { flex: 0 1 auto !important; }
.flex-none-3xl { flex: none !important; }


  /* Width and Height */
.w-none-3xl { width: none !important; }
.w-auto-3xl { width: auto !important; }
.w-screen-3xl { width: 100vw !important; }
.w-10p-3xl { width: 10% !important; }
.w-20p-3xl { width: 20% !important; }
.w-30p-3xl { width: 30% !important; }
.w-40p-3xl { width: 40% !important; }
.w-50p-3xl { width: 50% !important; }
.w-60p-3xl { width: 60% !important; }
.w-70p-3xl { width: 70% !important; }
.w-80p-3xl { width: 80% !important; }
.w-90p-3xl { width: 90% !important; }
.w-100p-3xl { width: 100% !important; }

.h-none-3xl { height: none !important; }
.h-auto-3xl { height: auto !important; }
.h-screen-3xl { height: 100vh !important; }
.h-10p-3xl { height: 10% !important; }
.h-20p-3xl { height: 20% !important; }
.h-30p-3xl { height: 30% !important; }
.h-40p-3xl { height: 40% !important; }
.h-50p-3xl { height: 50% !important; }
.h-60p-3xl { height: 60% !important; }
.h-70p-3xl { height: 70% !important; }
.h-80p-3xl { height: 80% !important; }
.h-90p-3xl { height: 90% !important; }
.h-100p-3xl { height: 100% !important; }

/* Block Button */
.btn-block-3xl {
  display: block;
  width: 100%;
}
}

/* --mico-breakpoint-2xl: 1440px; Ultra large devices (wide screens) */

@media screen and (max-width: 1440px) {



  .text-left-2xl { text-align: left !important; }
  .text-center-2xl { text-align: center !important; }
  .text-right-2xl { text-align: right !important; }
  .text-justify-2xl { text-align: justify !important; }
  .text-start-2xl { text-align: left !important; }
  .text-end-2xl { text-align: right !important; }

  .mx-auto-2xl { margin-left: auto !important; margin-right: auto !important; }
  .my-auto-2xl { margin-top: auto !important; margin-bottom: auto !important; }

  .d-none-2xl { display: none !important; }
  .d-inline-2xl { display: inline !important; }
  .d-inline-block-2xl { display: inline-block !important; }
  .d-block-2xl { display: block !important; }
  .d-flex-2xl { display: flex !important; }
  .d-inline-flex-2xl { display: inline-flex !important; }
  .d-grid-2xl { display: grid !important; }

  .grid-cols-1-2xl { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-2xl { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-2xl { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-2xl { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-2xl { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-2xl { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-2xl { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-2xl { grid-column: span 1 !important; }
  .col-2-2xl { grid-column: span 2 !important; }
  .col-3-2xl { grid-column: span 3 !important; }
  .col-4-2xl { grid-column: span 4 !important; }
  .col-5-2xl { grid-column: span 5 !important; }
  .col-6-2xl { grid-column: span 6 !important; }
  .col-7-2xl { grid-column: span 7 !important; }
  .col-8-2xl { grid-column: span 8 !important; }
  .col-9-2xl { grid-column: span 9 !important; }
  .col-10-2xl { grid-column: span 10 !important; }
  .col-11-2xl { grid-column: span 11 !important; }
  .col-12-2xl { grid-column: span 12 !important; }


  /* Flexbox */
.flex-row-2xl { flex-direction: row !important; }
.flex-column-2xl { flex-direction: column !important; }
.flex-row-reverse-2xl { flex-direction: row-reverse !important; }
.flex-column-reverse-2xl { flex-direction: column-reverse !important; }


.flex-wrap-2xl { flex-wrap: wrap !important; }
.flex-nowrap-2xl { flex-wrap: nowrap !important; }
.flex-wrap-reverse-2xl { flex-wrap: wrap-reverse !important; }

.justify-content-start-2xl { justify-content: flex-start !important; }
.justify-content-end-2xl { justify-content: flex-end !important; }
.justify-content-center-2xl { justify-content: center !important; }
.justify-content-between-2xl { justify-content: space-between !important; }
.justify-content-around-2xl { justify-content: space-around !important; }
.justify-content-evenly-2xl { justify-content: space-evenly !important; }

.align-items-start-2xl { align-items: flex-start !important; }
.align-items-end-2xl { align-items: flex-end !important; }
.align-items-center-2xl { align-items: center !important; }
.align-items-baseline-2xl { align-items: baseline !important; }
.align-items-stretch-2xl { align-items: stretch !important; }

.align-content-start-2xl { align-content: flex-start !important; }
.align-content-end-2xl { align-content: flex-end !important; }
.align-content-center-2xl { align-content: center !important; }
.align-content-between-2xl { align-content: space-between !important; }
.align-content-around-2xl { align-content: space-around !important; }
.align-content-stretch-2xl { align-content: stretch !important; }

.align-self-auto-2xl { align-self: auto !important; }
.align-self-start-2xl { align-self: flex-start !important; }
.align-self-end-2xl { align-self: flex-end !important; }
.align-self-center-2xl { align-self: center !important; }
.align-self-baseline-2xl { align-self: baseline !important; }
.align-self-stretch-2xl { align-self: stretch !important; }

.flex-grow-0-2xl { flex-grow: 0 !important; }
.flex-grow-1-2xl { flex-grow: 1 !important; }
.flex-shrink-0-2xl { flex-shrink: 0 !important; }
.flex-shrink-1-2xl { flex-shrink: 1 !important; }

.flex-1-2xl { flex: 1 1 0% !important; }
.flex-auto-2xl { flex: 1 1 auto !important; }
.flex-initial-2xl { flex: 0 1 auto !important; }
.flex-none-2xl { flex: none !important; }


  /* Width and Height */
.w-none-2xl { width: none !important; }
.w-auto-2xl { width: auto !important; }
.w-screen-2xl { width: 100vw !important; }
.w-10p-2xl { width: 10% !important; }
.w-20p-2xl { width: 20% !important; }
.w-30p-2xl { width: 30% !important; }
.w-40p-2xl { width: 40% !important; }
.w-50p-2xl { width: 50% !important; }
.w-60p-2xl { width: 60% !important; }
.w-70p-2xl { width: 70% !important; }
.w-80p-2xl { width: 80% !important; }
.w-90p-2xl { width: 90% !important; }
.w-100p-2xl { width: 100% !important; }

.h-none-2xl { height: none !important; }
.h-auto-2xl { height: auto !important; }
.h-screen-2xl { height: 100vh !important; }
.h-10p-2xl { height: 10% !important; }
.h-20p-2xl { height: 20% !important; }
.h-30p-2xl { height: 30% !important; }
.h-40p-2xl { height: 40% !important; }
.h-50p-2xl { height: 50% !important; }
.h-60p-2xl { height: 60% !important; }
.h-70p-2xl { height: 70% !important; }
.h-80p-2xl { height: 80% !important; }
.h-90p-2xl { height: 90% !important; }
.h-100p-2xl { height: 100% !important; }


/* Block Button */
.btn-block-2xl {
  display: block;
  width: 100%;
}
}

/* --mico-breakpoint-xl: 1280px; Extra large devices (large screens) */

@media screen and (max-width: 1280px) {



  .text-left-xl { text-align: left !important; }
  .text-center-xl { text-align: center !important; }
  .text-right-xl { text-align: right !important; }
  .text-justify-xl { text-align: justify !important; }
  .text-start-xl { text-align: left !important; }
  .text-end-xl { text-align: right !important; }

  .mx-auto-xl { margin-left: auto !important; margin-right: auto !important; }
  .my-auto-xl { margin-top: auto !important; margin-bottom: auto !important; }

  .d-none-xl { display: none !important; }
  .d-inline-xl { display: inline !important; }
  .d-inline-block-xl { display: inline-block !important; }
  .d-block-xl { display: block !important; }
  .d-flex-xl { display: flex !important; }
  .d-inline-flex-xl { display: inline-flex !important; }
  .d-grid-xl { display: grid !important; }

  .grid-cols-1-xl { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-xl { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-xl { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-xl { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-xl { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-xl { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-xl { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-xl { grid-column: span 1 !important; }
  .col-2-xl { grid-column: span 2 !important; }
  .col-3-xl { grid-column: span 3 !important; }
  .col-4-xl { grid-column: span 4 !important; }
  .col-5-xl { grid-column: span 5 !important; }
  .col-6-xl { grid-column: span 6 !important; }
  .col-7-xl { grid-column: span 7 !important; }
  .col-8-xl { grid-column: span 8 !important; }
  .col-9-xl { grid-column: span 9 !important; }
  .col-10-xl { grid-column: span 10 !important; }
  .col-11-xl { grid-column: span 11 !important; }
  .col-12-xl { grid-column: span 12 !important; }


  /* Flexbox */
.flex-row-xl { flex-direction: row !important; }
.flex-column-xl { flex-direction: column !important; }
.flex-row-reverse-xl { flex-direction: row-reverse !important; }
.flex-column-reverse-xl { flex-direction: column-reverse !important; }


.flex-wrap-xl { flex-wrap: wrap !important; }
.flex-nowrap-xl { flex-wrap: nowrap !important; }
.flex-wrap-reverse-xl { flex-wrap: wrap-reverse !important; }

.justify-content-start-xl { justify-content: flex-start !important; }
.justify-content-end-xl { justify-content: flex-end !important; }
.justify-content-center-xl { justify-content: center !important; }
.justify-content-between-xl { justify-content: space-between !important; }
.justify-content-around-xl { justify-content: space-around !important; }
.justify-content-evenly-xl { justify-content: space-evenly !important; }

.align-items-start-xl { align-items: flex-start !important; }
.align-items-end-xl { align-items: flex-end !important; }
.align-items-center-xl { align-items: center !important; }
.align-items-baseline-xl { align-items: baseline !important; }
.align-items-stretch-xl { align-items: stretch !important; }

.align-content-start-xl { align-content: flex-start !important; }
.align-content-end-xl { align-content: flex-end !important; }
.align-content-center-xl { align-content: center !important; }
.align-content-between-xl { align-content: space-between !important; }
.align-content-around-xl { align-content: space-around !important; }
.align-content-stretch-xl { align-content: stretch !important; }

.align-self-auto-xl { align-self: auto !important; }
.align-self-start-xl { align-self: flex-start !important; }
.align-self-end-xl { align-self: flex-end !important; }
.align-self-center-xl { align-self: center !important; }
.align-self-baseline-xl { align-self: baseline !important; }
.align-self-stretch-xl { align-self: stretch !important; }

.flex-grow-0-xl { flex-grow: 0 !important; }
.flex-grow-1-xl { flex-grow: 1 !important; }
.flex-shrink-0-xl { flex-shrink: 0 !important; }
.flex-shrink-1-xl { flex-shrink: 1 !important; }

.flex-1-xl { flex: 1 1 0% !important; }
.flex-auto-xl { flex: 1 1 auto !important; }
.flex-initial-xl { flex: 0 1 auto !important; }
.flex-none-xl { flex: none !important; }
  /* Width and Height */
.w-none-xl { width: none !important; }
.w-auto-xl { width: auto !important; }
.w-screen-xl { width: 100vw !important; }
.w--10p-xl { width: 10% !important; }
.w--20p-xl { width: 20% !important; }
.w--30p-xl { width: 30% !important; }
.w--40p-xl { width: 40% !important; }
.w--50p-xl { width: 50% !important; }
.w--60p-xl { width: 60% !important; }
.w--70p-xl { width: 70% !important; }
.w--80p-xl { width: 80% !important; }
.w--90p-xl { width: 90% !important; }
.w--100p-xl { width: 100% !important; }

.h-none-xl { height: none !important; }
.h-auto-xl { height: auto !important; }
.h-screen-xl { height: 100vh !important; }
.h-10p-xl { height: 10% !important; }
.h-20p-xl { height: 20% !important; }
.h-30p-xl { height: 30% !important; }
.h-40p-xl { height: 40% !important; }
.h-50p-xl { height: 50% !important; }
.h-60p-xl { height: 60% !important; }
.h-70p-xl { height: 70% !important; }
.h-80p-xl { height: 80% !important; }
.h-90p-xl { height: 90% !important; }
.h-100p-xl { height: 100% !important; }


/* Block Button */
.btn-block-xl {
  display: block;
  width: 100%;
}
}

/* --mico-breakpoint-lg: 992px; Extra large devices (large screens) */

@media screen and (max-width: 992px) {

  .text-left-lg { text-align: left !important; }
  .text-center-lg { text-align: center !important; }
  .text-right-lg { text-align: right !important; }
  .text-justify-lg { text-align: justify !important; }
  .text-start-lg { text-align: left !important; }
  .text-end-lg { text-align: right !important; }

  .mx-auto-lg { margin-left: auto !important; margin-right: auto !important; }
  .my-auto-lg { margin-top: auto !important; margin-bottom: auto !important; }

  .d-none-lg { display: none !important; }
  .d-inline-lg { display: inline !important; }
  .d-inline-block-lg { display: inline-block !important; }
  .d-inline-lg { display: inline !important; }
  .d-inline-block-lg { display: inline-block !important; }
  .d-block-lg { display: block !important; }
  .d-flex-lg { display: flex !important; }
  .d-inline-flex-lg { display: inline-flex !important; }
  .d-grid-lg { display: grid !important; }
  
  .grid-cols-1-lg { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-lg { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-lg { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-lg { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-lg { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-lg { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-lg { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-lg { grid-column: span 1 !important; }
  .col-2-lg { grid-column: span 2 !important; }
  .col-3-lg { grid-column: span 3 !important; }
  .col-4-lg { grid-column: span 4 !important; }
  .col-5-lg { grid-column: span 5 !important; }
  .col-6-lg { grid-column: span 6 !important; }
  .col-7-lg { grid-column: span 7 !important; }
  .col-8-lg { grid-column: span 8 !important; }
  .col-9-lg { grid-column: span 9 !important; }
  .col-10-lg { grid-column: span 10 !important; }
  .col-11-lg { grid-column: span 11 !important; }
  .col-12-lg { grid-column: span 12 !important; }


  /* Flexbox */
.flex-row-lg { flex-direction: row !important; }
.flex-column-lg { flex-direction: column !important; }
.flex-row-reverse-lg { flex-direction: row-reverse !important; }
.flex-column-reverse-lg { flex-direction: column-reverse !important; }


.flex-wrap-lg { flex-wrap: wrap !important; }
.flex-nowrap-lg { flex-wrap: nowrap !important; }
.flex-wrap-reverse-lg { flex-wrap: wrap-reverse !important; }

.justify-content-start-lg { justify-content: flex-start !important; }
.justify-content-end-lg { justify-content: flex-end !important; }
.justify-content-center-lg { justify-content: center !important; }
.justify-content-between-lg { justify-content: space-between !important; }
.justify-content-around-lg { justify-content: space-around !important; }
.justify-content-evenly-lg { justify-content: space-evenly !important; }

.align-items-start-lg { align-items: flex-start !important; }
.align-items-end-lg { align-items: flex-end !important; }
.align-items-center-lg { align-items: center !important; }
.align-items-baseline-lg { align-items: baseline !important; }
.align-items-stretch-lg { align-items: stretch !important; }

.align-content-start-lg { align-content: flex-start !important; }
.align-content-end-lg { align-content: flex-end !important; }
.align-content-center-lg { align-content: center !important; }
.align-content-between-lg { align-content: space-between !important; }
.align-content-around-lg { align-content: space-around !important; }
.align-content-stretch-lg { align-content: stretch !important; }

.align-self-auto-lg { align-self: auto !important; }
.align-self-start-lg { align-self: flex-start !important; }
.align-self-end-lg { align-self: flex-end !important; }
.align-self-center-lg { align-self: center !important; }
.align-self-baseline-lg { align-self: baseline !important; }
.align-self-stretch-lg { align-self: stretch !important; }

.flex-grow-0-lg { flex-grow: 0 !important; }
.flex-grow-1-lg { flex-grow: 1 !important; }
.flex-shrink-0-lg { flex-shrink: 0 !important; }
.flex-shrink-1-lg { flex-shrink: 1 !important; }

.flex-1-lg { flex: 1 1 0% !important; }
.flex-auto-lg { flex: 1 1 auto !important; }
.flex-initial-lg { flex: 0 1 auto !important; }
.flex-none-lg { flex: none !important; }


  /* Width and Height */
.w-none-lg { width: none !important; }
.w-auto-lg { width: auto !important; }
.w-screen-lg { width: 100vw !important; }
.w-10p-lg { width: 10% !important; }
.w-20p-lg { width: 20% !important; }
.w-30p-lg { width: 30% !important; }
.w-40p-lg { width: 40% !important; }
.w-50p-lg { width: 50% !important; }
.w-60p-lg { width: 60% !important; }
.w-70p-lg { width: 70% !important; }
.w-80p-lg { width: 80% !important; }
.w-90p-lg { width: 90% !important; }
.w-100p-lg { width: 100% !important; }

.h-none-lg { height: none !important; }
.h-auto-lg { height: auto !important; }
.h-screen-lg { height: 100vh !important; }
.h-10p-lg { height: 10% !important; }
.h-20p-lg { height: 20% !important; }
.h-30p-lg { height: 30% !important; }
.h-40p-lg { height: 40% !important; }
.h-50p-lg { height: 50% !important; }
.h-60p-lg { height: 60% !important; }
.h-70p-lg { height: 70% !important; }
.h-80p-lg { height: 80% !important; }
.h-90p-lg { height: 90% !important; }
.h-100p-lg { height: 100% !important; }


/* Block Button */
.btn-block-lg {
  display: block;
  width: 100%;
}
}

/*  --mico-breakpoint-md: 768px; Medium devices (tablets) */

@media screen and (max-width: 768px) {

  .text-left-md { text-align: left !important; }
  .text-center-md { text-align: center !important; }
  .text-right-md { text-align: right !important; }
  .text-justify-md { text-align: justify !important; }
  .text-start-md { text-align: left !important; }
  .text-end-md { text-align: right !important; }

  .mx-auto-md { margin-left: auto !important; margin-right: auto !important; }
  .my-auto-md { margin-top: auto !important; margin-bottom: auto !important; }

  .d-none-md { display: none !important; }
  .d-inline-md { display: inline !important; }
  .d-inline-block-md { display: inline-block !important; }
  .d-block-md { display: block !important; }
  .d-flex-md { display: flex !important; }
  .d-inline-flex-md { display: inline-flex !important; }
  .d-grid-md { display: grid !important; }
  
  .grid-cols-1-md { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-md { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-md { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-md { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-md { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-md { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-md { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-md { grid-column: span 1 !important; }
  .col-2-md { grid-column: span 2 !important; }
  .col-3-md { grid-column: span 3 !important; }
  .col-4-md { grid-column: span 4 !important; }
  .col-5-md { grid-column: span 5 !important; }
  .col-6-md { grid-column: span 6 !important; }
  .col-7-md { grid-column: span 7 !important; }
  .col-8-md { grid-column: span 8 !important; }
  .col-9-md { grid-column: span 9 !important; }
  .col-10-md { grid-column: span 10 !important; }
  .col-11-md { grid-column: span 11 !important; }
  .col-12-md { grid-column: span 12 !important; }


  /* Flexbox */
.flex-row-md { flex-direction: row !important; }
.flex-column-md { flex-direction: column !important; }
.flex-row-reverse-md { flex-direction: row-reverse !important; }
.flex-column-reverse-md { flex-direction: column-reverse !important; }


.flex-wrap-md { flex-wrap: wrap !important; }
.flex-nowrap-md { flex-wrap: nowrap !important; }
.flex-wrap-reverse-md { flex-wrap: wrap-reverse !important; }

.justify-content-start-md { justify-content: flex-start !important; }
.justify-content-end-md { justify-content: flex-end !important; }
.justify-content-center-md { justify-content: center !important; }
.justify-content-between-md { justify-content: space-between !important; }
.justify-content-around-md { justify-content: space-around !important; }
.justify-content-evenly-md { justify-content: space-evenly !important; }

.align-items-start-md { align-items: flex-start !important; }
.align-items-end-md { align-items: flex-end !important; }
.align-items-center-md { align-items: center !important; }
.align-items-baseline-md { align-items: baseline !important; }
.align-items-stretch-md { align-items: stretch !important; }

.align-content-start-md { align-content: flex-start !important; }
.align-content-end-md { align-content: flex-end !important; }
.align-content-center-md { align-content: center !important; }
.align-content-between-md { align-content: space-between !important; }
.align-content-around-md { align-content: space-around !important; }
.align-content-stretch-md { align-content: stretch !important; }

.align-self-auto-md { align-self: auto !important; }
.align-self-start-md { align-self: flex-start !important; }
.align-self-end-md { align-self: flex-end !important; }
.align-self-center-md { align-self: center !important; }
.align-self-baseline-md { align-self: baseline !important; }
.align-self-stretch-md { align-self: stretch !important; }

.flex-grow-0-md { flex-grow: 0 !important; }
.flex-grow-1-md { flex-grow: 1 !important; }
.flex-shrink-0-md { flex-shrink: 0 !important; }
.flex-shrink-1-md { flex-shrink: 1 !important; }

.flex-1-md { flex: 1 1 0% !important; }
.flex-auto-md { flex: 1 1 auto !important; }
.flex-initial-md { flex: 0 1 auto !important; }
.flex-none-md { flex: none !important; }

  /* Width and Height */
.w-none-md { width: none !important; }
.w-auto-md { width: auto !important; }
.w-screen-md { width: 100vw !important; }
.w-10p-md { width: 10% !important; }
.w-20p-md { width: 20% !important; }
.w-30p-md { width: 30% !important; }
.w-40p-md { width: 40% !important; }
.w-50p-md { width: 50% !important; }
.w-60p-md { width: 60% !important; }
.w-70p-md { width: 70% !important; }
.w-80p-md { width: 80% !important; }
.w-90p-md { width: 90% !important; }
.w-100p-md { width: 100% !important; }

.h-none-md { height: none !important; }
.h-auto-md { height: auto !important; }
.h-screen-md { height: 100vh !important; }
.h-10p-md { height: 10% !important; }
.h-20p-md { height: 20% !important; }
.h-30p-md { height: 30% !important; }
.h-40p-md { height: 40% !important; }
.h-50p-md { height: 50% !important; }
.h-60p-md { height: 60% !important; }
.h-70p-md { height: 70% !important; }
.h-80p-md { height: 80% !important; }
.h-90p-md { height: 90% !important; }
.h-100p-md { height: 100% !important; }


/* Block Button */
.btn-block-md {
  display: block;
  width: 100%;
}
}

/*  --mico-breakpoint-sm: 576px; Small devices (large phones, portrait tablets) */

@media screen and (max-width: 576px) {

  .text-left-sm { text-align: left !important; }
  .text-center-sm { text-align: center !important; }
  .text-right-sm { text-align: right !important; }
  .text-justify-sm { text-align: justify !important; }
  .text-start-sm { text-align: left !important; }
  .text-end-sm { text-align: right !important; }

  .mx-auto-sm { margin-left: auto !important; margin-right: auto !important; }
  .my-auto-sm { margin-top: auto !important; margin-bottom: auto !important; }

  .d-none-sm { display: none !important; }
  .d-inline-sm { display: inline !important; }
  .d-inline-block-sm { display: inline-block !important; }
  .d-block-sm { display: block !important; }
  .d-flex-sm { display: flex !important; }
  .d-inline-flex-sm { display: inline-flex !important; }
  .d-grid-sm { display: grid !important; }
  
  .grid-cols-1-sm { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-sm { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-sm { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-sm { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-sm { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-sm { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-sm { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-sm { grid-column: span 1 !important; }
  .col-2-sm { grid-column: span 2 !important; }
  .col-3-sm { grid-column: span 3 !important; }
  .col-4-sm { grid-column: span 4 !important; }
  .col-5-sm { grid-column: span 5 !important; }
  .col-6-sm { grid-column: span 6 !important; }
  .col-7-sm { grid-column: span 7 !important; }
  .col-8-sm { grid-column: span 8 !important; }
  .col-9-sm { grid-column: span 9 !important; }
  .col-10-sm { grid-column: span 10 !important; }
  .col-11-sm { grid-column: span 11 !important; }
  .col-12-sm { grid-column: span 12 !important; }


/* Flexbox */
.flex-row-sm { flex-direction: row !important; }
.flex-column-sm { flex-direction: column !important; }
.flex-row-reverse-sm { flex-direction: row-reverse !important; }
.flex-column-reverse-sm { flex-direction: column-reverse !important; }


.flex-wrap-sm { flex-wrap: wrap !important; }
.flex-nowrap-sm { flex-wrap: nowrap !important; }
.flex-wrap-reverse-sm { flex-wrap: wrap-reverse !important; }

.justify-content-start-sm { justify-content: flex-start !important; }
.justify-content-end-sm { justify-content: flex-end !important; }
.justify-content-center-sm { justify-content: center !important; }
.justify-content-between-sm { justify-content: space-between !important; }
.justify-content-around-sm { justify-content: space-around !important; }
.justify-content-evenly-sm { justify-content: space-evenly !important; }

.align-items-start-sm { align-items: flex-start !important; }
.align-items-end-sm { align-items: flex-end !important; }
.align-items-center-sm { align-items: center !important; }
.align-items-baseline-sm { align-items: baseline !important; }
.align-items-stretch-sm { align-items: stretch !important; }

.align-content-start-sm { align-content: flex-start !important; }
.align-content-end-sm { align-content: flex-end !important; }
.align-content-center-sm { align-content: center !important; }
.align-content-between-sm { align-content: space-between !important; }
.align-content-around-sm { align-content: space-around !important; }
.align-content-stretch-sm { align-content: stretch !important; }

.align-self-auto-sm { align-self: auto !important; }
.align-self-start-sm { align-self: flex-start !important; }
.align-self-end-sm { align-self: flex-end !important; }
.align-self-center-sm { align-self: center !important; }
.align-self-baseline-sm { align-self: baseline !important; }
.align-self-stretch-md { align-self: stretch !important; }

.flex-grow-0-sm { flex-grow: 0 !important; }
.flex-grow-1-sm { flex-grow: 1 !important; }
.flex-shrink-0-sm { flex-shrink: 0 !important; }
.flex-shrink-1-sm { flex-shrink: 1 !important; }

.flex-1-sm { flex: 1 1 0% !important; }
.flex-auto-sm { flex: 1 1 auto !important; }
.flex-initial-sm { flex: 0 1 auto !important; }
.flex-none-sm { flex: none !important; }

  /* Width and Height */
.w-none-sm { width: none !important; }
.w-auto-sm { width: auto !important; }
.w-screen-sm { width: 100vw !important; }
.w-10p-sm { width: 10% !important; }
.w-20p-sm { width: 20% !important; }
.w-30p-sm { width: 30% !important; }
.w-40p-sm { width: 40% !important; }
.w-50p-sm { width: 50% !important; }
.w-60p-sm { width: 60% !important; }
.w-70p-sm { width: 70% !important; }
.w-80p-sm { width: 80% !important; }
.w-90p-sm { width: 90% !important; }
.w-100p-sm { width: 100% !important; }

.h-none-sm { height: none !important; }
.h-auto-sm { height: auto !important; }
.h-screen-sm { height: 100vh !important; }
.h-10p-sm { height: 10% !important; }
.h-20p-sm { height: 20% !important; }
.h-30p-sm { height: 30% !important; }
.h-40p-sm { height: 40% !important; }
.h-50p-sm { height: 50% !important; }
.h-60p-sm { height: 60% !important; }
.h-70p-sm { height: 70% !important; }
.h-80p-sm { height: 80% !important; }
.h-90p-sm { height: 90% !important; }
.h-100p-sm { height: 100% !important; }


/* Block Button */
.btn-block-sm {
  display: block;
  width: 100%;
}
}

/*  --mico-breakpoint-xs: 320px; * Extra small devices (phones) */

@media screen and (max-width: 320px) {
  
/* ====================================================================== */
/* TYPOGRAPHY UTILITIES                                                    */
/* ====================================================================== */

/**
 * Text Alignment
 */

  .text-left-xs { text-align: left !important; }
  .text-center-xs { text-align: center !important; }
  .text-right-xs { text-align: right !important; }
  .text-justify-xs { text-align: justify !important; }
  .text-start-xs { text-align: left !important; }
  .text-end-xs { text-align: right !important; }

  .mx-auto-xs { margin-left: auto !important; margin-right: auto !important; }
  .my-auto-xs { margin-top: auto !important; margin-bottom: auto !important; }

  .d-none-xs { display: none !important; }
  .d-inline-xs { display: inline !important; }
  .d-inline-block-xs { display: inline-block !important; }
  .d-block-xs { display: block !important; }
  .d-flex-xs { display: flex !important; }
  .d-inline-flex-xs { display: inline-flex !important; }
  .d-grid-xs { display: grid !important; }
  
  .grid-cols-1-xs { grid-template-columns: repeat(1, 1fr) !important; }
  .grid-cols-2-xs { grid-template-columns: repeat(2, 1fr) !important; }
  .grid-cols-3-xs { grid-template-columns: repeat(3, 1fr) !important; }
  .grid-cols-4-xs { grid-template-columns: repeat(4, 1fr) !important; }
  .grid-cols-5-xs { grid-template-columns: repeat(5, 1fr) !important; }
  .grid-cols-6-xs { grid-template-columns: repeat(6, 1fr) !important; }
  .grid-cols-12-xs { grid-template-columns: repeat(12, 1fr) !important; }
  
  .col-1-xs { grid-column: span 1 !important; }
  .col-2-xs { grid-column: span 2 !important; }
  .col-3-xs { grid-column: span 3 !important; }
  .col-4-xs { grid-column: span 4 !important; }
  .col-5-xs { grid-column: span 5 !important; }
  .col-6-xs { grid-column: span 6 !important; }
  .col-7-xs { grid-column: span 7 !important; }
  .col-8-xs { grid-column: span 8 !important; }
  .col-9-xs { grid-column: span 9 !important; }
  .col-10-xs { grid-column: span 10 !important; }
  .col-11-xs { grid-column: span 11 !important; }
  .col-12-xs { grid-column: span 12 !important; }


  /* Flexbox */
.flex-row-xs { flex-direction: row !important; }
.flex-column-xs { flex-direction: column !important; }
.flex-row-reverse-xs { flex-direction: row-reverse !important; }
.flex-column-reverse-xs { flex-direction: column-reverse !important; }


.flex-wrap-xs { flex-wrap: wrap !important; }
.flex-nowrap-xs { flex-wrap: nowrap !important; }
.flex-wrap-reverse-xs { flex-wrap: wrap-reverse !important; }

.justify-content-start-xs { justify-content: flex-start !important; }
.justify-content-end-xs { justify-content: flex-end !important; }
.justify-content-center-xs { justify-content: center !important; }
.justify-content-between-xs { justify-content: space-between !important; }
.justify-content-around-xs { justify-content: space-around !important; }
.justify-content-evenly-xs { justify-content: space-evenly !important; }

.justify-items-start-xs { justify-items: flex-start !important; }
.justify-items-end-xs { justify-items: flex-end !important; }
.justify-items-center-xs { justify-items: center !important; }

.align-items-start-xs { align-items: flex-start !important; }
.align-items-end-xs { align-items: flex-end !important; }
.align-items-center-xs { align-items: center !important; }
.align-items-baseline-xs { align-items: baseline !important; }
.align-items-stretch-xs { align-items: stretch !important; }

.align-content-start-xs { align-content: flex-start !important; }
.align-content-end-xs { align-content: flex-end !important; }
.align-content-center-xs { align-content: center !important; }
.align-content-between-xs { align-content: space-between !important; }
.align-content-around-xs { align-content: space-around !important; }
.align-content-stretch-xs { align-content: stretch !important; }

.align-self-auto-xs { align-self: auto !important; }
.align-self-start-xs { align-self: flex-start !important; }
.align-self-end-xs { align-self: flex-end !important; }
.align-self-center-xs { align-self: center !important; }
.align-self-baseline-xs { align-self: baseline !important; }
.align-self-stretch-xs { align-self: stretch !important; }

.flex-grow-0-xs { flex-grow: 0 !important; }
.flex-grow-1-xs { flex-grow: 1 !important; }
.flex-shrink-0-xs { flex-shrink: 0 !important; }
.flex-shrink-1-xs { flex-shrink: 1 !important; }

.flex-1-xs { flex: 1 1 0% !important; }
.flex-auto-xs { flex: 1 1 auto !important; }
.flex-initial-xs { flex: 0 1 auto !important; }
.flex-none-xs { flex: none !important; }

  /* Width and Height */
.w-none-xs { width: none !important; }
.w-auto-xs { width: auto !important; }
.w-screen-xs { width: 100vw !important; }
.w-10p-xs { width: 10% !important; }
.w-20p-xs { width: 20% !important; }
.w-30p-xs { width: 30% !important; }
.w-40p-xs { width: 40% !important; }
.w-50p-xs { width: 50% !important; }
.w-60p-xs { width: 60% !important; }
.w-70p-xs { width: 70% !important; }
.w-80p-xs { width: 80% !important; }
.w-90p-xs { width: 90% !important; }
.w-100p-xs { width: 100% !important; }

.h-none-xs { height: none !important; }
.h-auto-xs { height: auto !important; }
.h-screen-xs { height: 100vh !important; }
.h-10p-xs { height: 10% !important; }
.h-20p-xs { height: 20% !important; }
.h-30p-xs { height: 30% !important; }
.h-40p-xs { height: 40% !important; }
.h-50p-xs { height: 50% !important; }
.h-60p-xs { height: 60% !important; }
.h-70p-xs { height: 70% !important; }
.h-80p-xs { height: 80% !important; }
.h-90p-xs { height: 90% !important; }
.h-100p-xs { height: 100% !important; }


/* Block Button */
.btn-block-xs {
  display: block;
  width: 100%;
}

}

/* Presets */

@media (max-width: 1440px) {
    #div_block-26-2,#div_block-92-2 {
        flex-wrap: nowrap;
    }
}

@media (max-width: 1280px) {
    #div_block-26-2,#div_block-92-2 {
        flex-wrap: wrap;
    }
}

@media (max-width: 992px) {

#div_block-45-2{
margin-top: 0 !important;
}
}

/* Accessibility */

/* Mico Accessibility Styles */

/* These styles ensure that the framework meets WCAG 2.1 AA standards */

/* Screen reader only text - hides content visually but keeps it accessible to screen readers */

.sr-only,
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Make the element visible when it's focused */

.sr-only-focusable:not(:focus),
.visually-hidden-focusable:not(:focus) {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip link - allows keyboard users to skip to main content */

.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background: rgb(236, 125, 0);
  background: color(display-p3 0.86926 0.51255 0.09455);
  color: rgb(237, 238, 242);
  padding: 8px;
  z-index: 100;
  transition: all .11s ease;
}

.skip-link:focus {
  top: 0;
}

/* Focus styles */

:focus {
  outline: 1px solid rgb(236, 125, 0);
  outline: 1px solid color(display-p3 0.86926 0.51255 0.09455);
  outline-offset: 2px;
  transition: all .11s ease;
}

.focusable:focus {
  outline: 1px solid rgb(236, 125, 0);
  outline: 1px solid color(display-p3 0.86926 0.51255 0.09455);
  outline-offset: 2px;
  transition: all .11s ease;
}

/* Remove focus outline for mouse users, but keep for keyboard navigation */

.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
}

/* High contrast mode adjustments */

@media (prefers-contrast: high) {
  :root {
    --mico-color-primary: rgb(20, 22, 27);
    --mico-color-secondary: rgb(20, 22, 27);
    --mico-color-accent: rgb(20, 22, 27);
    --mico-color-background: rgb(247, 248, 252);
    --mico-color-text: rgb(20, 22, 27);
  }

  /* Ensure all text has sufficient contrast */
  body {
    color: rgb(3, 3, 4);
    background-color: rgb(237, 238, 242);
  }

  /* Enhance borders for better visibility */
  button,
  input,
  select,
  textarea,
  .btn,
  .card,
  .alert {
    border: 1px solid rgb(3, 3, 4) !important;
  }

  /* Ensure links are underlined */
  a {
    -webkit-text-decoration: underline !important;
    text-decoration: underline !important;
  }

  /* Enhance focus states */
  :focus {
    outline: 3px solid rgb(3, 3, 4) !important;
    outline-offset: 3px !important;
  }
}

/* Reduced motion preferences */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate,
  .animate-fade-in,
  .animate-slide-in,
  .animate-pulse,
  [class*="animate-"] {
    animation: none !important;
    transition: none !important;
  }
}

/* Ensure proper color contrast for form elements */

input,
select,
textarea {
  background-color: rgb(237, 238, 242);
  color: rgb(3, 3, 4);
  border: 1px solid rgb(43, 46, 52); /* WCAG AA compliant */
}

/* Ensure form labels are visible */

label {
  display: inline-block;
  margin-bottom: 0.5rem;
}

/* Ensure error messages are properly styled for accessibility */

.error,
[aria-invalid="true"] {
  border-color: rgb(215, 71, 69) !important;
}

.error-message {
  color: rgb(215, 71, 69);
  margin-top: 0.25rem;
  font-size: 0.875rem;
}

/* Ensure proper focus for interactive elements */

button:focus,
input:focus,
select:focus,
textarea:focus,
[role="button"]:focus,
[role="checkbox"]:focus,
[role="radio"]:focus,
[role="tab"]:focus,
[role="menuitem"]:focus {
  outline: 1px solid rgb(236, 125, 0);
  outline: 1px solid color(display-p3 0.86926 0.51255 0.09455);
  outline-offset: 2px;
  z-index: 1;
}

/* Ensure proper styling for disabled elements */

button:disabled,
input:disabled,
select:disabled,
textarea:disabled,
[aria-disabled="true"] {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Ensure proper ARIA states are visually represented */

[aria-expanded="true"] .icon-expand {
  transform: rotate(180deg);
}

[aria-selected="true"] {
  background-color: rgb(103, 170, 237);
  color: rgb(0, 8, 44);
  color: color(display-p3 0 0.02366 0.17799);
}

[aria-current="page"] {
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

/* Ensure proper keyboard focus indication for custom controls */

[role="button"],
[role="checkbox"],
[role="radio"],
[role="tab"],
[role="menuitem"] {
  cursor: pointer;
}