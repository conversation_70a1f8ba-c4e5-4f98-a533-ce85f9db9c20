:root{--mico-shadow-hover-primary:0px 4.5px 4px -3.8px hsl(from var(--mico-color-primary) h calc(s * 0.9) l)!important;--mico-shadow-hover-secondary:0px 4.5px 4px -3.8px hsl(from var(--mico-color-secondary) h calc(s * 0.9) l)!important;--mico-shadow-hover-accent:0px 4.5px 4px -3.8px hsl(from var(--mico-color-accent) h calc(s * 0.9) l)!important;--mico-shadow-focus-primary:0px 4.6px 4px -3.8px hsl(from var(--mico-color-primary) h calc(s * 0.8) l)!important;--mico-shadow-focus-secondary:0px 4.6px 4px -3.8px hsl(from var(--mico-color-secondary) h calc(s * 0.8) l)!important;--mico-shadow-focus-accent:0px 4.6px 4px -3.8px hsl(from var(--mico-color-accent) h calc(s * 0.8) l)!important;--mico-shadow-active-primary:0px 4.8px 4px -2.8px hsl(from var(--mico-color-primary) h calc(s * 0.7) l)!important;--mico-shadow-active-secondary:0px 4.8px 4px -2.8px hsl(from var(--mico-color-secondary) h calc(s * 0.7) l)!important;--mico-shadow-active-accent:0px 4.8px 4px -2.8px hsl(from var(--mico-color-accent) h calc(s * 0.7) l)!important;--mico-breakpoint-xs:320px;--mico-breakpoint-sm:576px;--mico-breakpoint-md:768px;--mico-breakpoint-lg:992px;--mico-breakpoint-xl:1280px;--mico-breakpoint-2xl:1440px;--mico-breakpoint-3xl:1600px;--mico-value-auto:auto;--mico-value-none:none;--mico-value-normal:normal;--mico-value-inherit:inherit;--mico-value-initial:initial;--mico-value-unset:unset;--mico-value-current:currentColor;--mico-value-transparent:transparent;--mico-value-0:0;--mico-fs-xs:max(0.995rem,min(1.5vw,0.975rem));--mico-fs-sm:max(0.980rem,min(2vw,1.2rem));--mico-fs-md:max(1.051rem,min(2.5vw,1.30rem));--mico-fs-lg:max(1.25rem,min(3vw,1.675rem));--mico-fs-xl:max(1.5rem,min(4vw,2rem));--mico-fs-2xl:max(1.875rem,min(5vw,3rem));--mico-fs-3xl:max(2.25rem,min(5.5vw,3.5rem));--mico-fs-4xl:max(2.5rem,min(6vw,4rem));--mico-fs-5xl:max(3rem,min(6.5vw,4.5rem));--mico-fs-6xl:max(3.5rem,min(7vw,5rem));--mico-fs-7xl:max(3.75rem,min(7.5vw,5.5rem));--mico-fs-8xl:max(4rem,min(8vw,6rem));--mico-fs-9xl:max(4.5rem,min(9vw,7rem));--mico-fw-100:100;--mico-fw-200:200;--mico-fw-300:300;--mico-fw-400:400;--mico-fw-500:500;--mico-fw-600:600;--mico-fw-700:700;--mico-fw-800:800;--mico-fw-900:900;--mico-font-stretch-ultra-condensed:ultra-condensed;--mico-font-stretch-extra-condensed:extra-condensed;--mico-font-stretch-condensed:condensed;--mico-font-stretch-semi-condensed:semi-condensed;--mico-font-stretch-normal:var(--mico-value-normal);--mico-font-stretch-semi-expanded:semi-expanded;--mico-font-stretch-expanded:expanded;--mico-font-stretch-extra-expanded:extra-expanded;--mico-font-stretch-ultra-expanded:ultra-expanded;--mico-font-style-normal:var(--mico-value-normal);--mico-font-style-italic:italic;--mico-font-variant-numeric-normal:var(--mico-value-normal);--mico-font-variant-numeric-ordinal:ordinal;--mico-font-variant-numeric-slashed-zero:slashed-zero;--mico-font-variant-numeric-lining-nums:lining-nums;--mico-font-variant-numeric-oldstyle-nums:oldstyle-nums;--mico-font-variant-numeric-proportional-nums:proportional-nums;--mico-font-variant-numeric-tabular-nums:tabular-nums;--mico-font-variant-numeric-diagonal-fractions:diagonal-fractions;--mico-font-variant-numeric-stacked-fractions:stacked-fractions;--mico-font-variant-ligatures-common:common-ligatures;--mico-font-variant-ligatures-no-common:no-common-ligatures;--mico-font-variant-ligatures-discretionary:discretionary-ligatures;--mico-font-variant-ligatures-no-discretionary:no-discretionary-ligatures;--mico-font-variant-ligatures-historical:historical-ligatures;--mico-font-variant-ligatures-no-historical:no-historical-ligatures;--mico-font-variant-ligatures-contextual:contextual;--mico-font-variant-ligatures-no-contextual:no-contextual;--mico-font-variant-caps-normal:var(--mico-value-normal);--mico-font-variant-caps-small-caps:small-caps;--mico-font-variant-caps-all-small-caps:all-small-caps;--mico-font-variant-caps-petite-caps:petite-caps;--mico-font-variant-caps-all-petite-caps:all-petite-caps;--mico-font-variant-caps-unicase:unicase;--mico-font-variant-caps-titling-caps:titling-caps;--mico-lh-xs:1;--mico-lh-sm:1.25;--mico-lh-md:1.5;--mico-lh-lg:1.625;--mico-lh-xl:2;--mico-lh-2xl:0.75rem;--mico-lh-3xl:1rem;--mico-lh-4xl:1.25rem;--mico-lh-5xl:1.5rem;--mico-lh-6xl:1.75rem;--mico-lh-7xl:2rem;--mico-lh-8xl:2.25rem;--mico-lh-9xl:2.5rem;--mico-ls-xs:-0.05em;--mico-ls-sm:-0.025em;--mico-ls-md:0em;--mico-ls-lg:0.025em;--mico-ls-xl:0.05em;--mico-ls-2xl:0.1em;--mico-underline-offset-auto:var(--mico-value-auto);--mico-underline-offset-0:var(--mico-value-0);--mico-underline-offset-1:1px;--mico-underline-offset-2:2px;--mico-underline-offset-4:4px;--mico-underline-offset-8:8px;--mico-underline-offset:0.15em;--mico-decoration-thickness-auto:var(--mico-value-auto);--mico-decoration-thickness-from-font:from-font;--mico-decoration-thickness-0:0px;--mico-decoration-thickness-1:1px;--mico-decoration-thickness-2:2px;--mico-decoration-thickness-4:4px;--mico-decoration-thickness-8:8px;--mico-underline-thickness:0.05em;--mico-decoration-style-solid:solid;--mico-decoration-style-double:double;--mico-decoration-style-dotted:dotted;--mico-decoration-style-dashed:dashed;--mico-decoration-style-wavy:wavy;--mico-underline-position-auto:var(--mico-value-auto);--mico-underline-position-under:under;--mico-underline-position-left:left;--mico-underline-position-right:right;--mico-text-transform-uppercase:uppercase;--mico-text-transform-lowercase:lowercase;--mico-text-transform-capitalize:capitalize;--mico-text-transform-none:var(--mico-value-none);--mico-text-align-left:left;--mico-text-align-center:center;--mico-text-align-right:right;--mico-text-align-justify:justify;--mico-text-align-start:start;--mico-text-align-end:end;--mico-text-overflow-ellipsis:ellipsis;--mico-text-overflow-clip:clip;--mico-whitespace-normal:var(--mico-value-normal);--mico-whitespace-nowrap:nowrap;--mico-whitespace-pre:pre;--mico-whitespace-pre-line:pre-line;--mico-whitespace-pre-wrap:pre-wrap;--mico-whitespace-break-spaces:break-spaces;--mico-indent-0:var(--mico-value-0);--mico-indent-xs:1px;--mico-indent-sm:0.25rem;--mico-indent-md:0.5rem;--mico-indent-lg:1rem;--mico-indent-xl:2rem;--mico-text-shadow-none:var(--mico-value-none);--mico-text-shadow-xs:1px 1px 2px rgba(0,0,0,.1);--mico-text-shadow-sm:2px 2px 4px rgba(0,0,0,.1);--mico-text-shadow-md:4px 4px 6px rgba(0,0,0,.1);--mico-text-shadow-lg:6px 6px 8px rgba(0,0,0,.15);--mico-text-stroke-xs:1px;--mico-text-stroke-sm:2px;--mico-text-stroke-md:4px;--mico-list-style-type-none:var(--mico-value-none);--mico-list-style-type-disc:disc;--mico-list-style-type-decimal:decimal;--mico-list-style-type-square:square;--mico-list-style-type-upper-roman:upper-roman;--mico-list-style-type-lower-roman:lower-roman;--mico-list-style-type-upper-alpha:upper-alpha;--mico-list-style-type-lower-alpha:lower-alpha;--mico-list-style-position-inside:inside;--mico-list-style-position-outside:outside;--mico-text-direction-ltr:ltr;--mico-text-direction-rtl:rtl;--mico-writing-mode-horizontal-tb:horizontal-tb;--mico-writing-mode-vertical-rl:vertical-rl;--mico-writing-mode-vertical-lr:vertical-lr;--mico-text-orientation-mixed:mixed;--mico-text-orientation-upright:upright;--mico-text-orientation-sideways:sideways;--mico-hyphens-none:var(--mico-value-none);--mico-hyphens-manual:manual;--mico-hyphens-auto:var(--mico-value-auto);--mico-text-align-last-auto:var(--mico-value-auto);--mico-text-align-last-start:start;--mico-text-align-last-end:end;--mico-text-align-last-left:left;--mico-text-align-last-right:right;--mico-text-align-last-center:center;--mico-text-align-last-justify:justify;--mico-text-justify-auto:var(--mico-value-auto);--mico-text-justify-inter-word:inter-word;--mico-text-justify-inter-character:inter-character;--mico-text-justify-none:var(--mico-value-none);--mico-user-select-none:var(--mico-value-none);--mico-user-select-text:text;--mico-user-select-all:all;--mico-user-select-auto:var(--mico-value-auto);--mico-word-break-normal:var(--mico-value-normal);--mico-word-break-break-all:break-all;--mico-word-break-keep-all:keep-all;--mico-overflow-wrap-normal:var(--mico-value-normal);--mico-overflow-wrap-break-word:break-word;--mico-size-unit:4px;--mico-size-0:0;--mico-size-1:1px;--mico-size-2:2px;--mico-size-4:calc(var(--mico-size-unit) * 1);--mico-size-8:calc(var(--mico-size-unit) * 2);--mico-size-12:calc(var(--mico-size-unit) * 3);--mico-size-16:calc(var(--mico-size-unit) * 4);--mico-size-20:calc(var(--mico-size-unit) * 5);--mico-size-24:calc(var(--mico-size-unit) * 6);--mico-size-28:calc(var(--mico-size-unit) * 7);--mico-size-32:calc(var(--mico-size-unit) * 8);--mico-size-36:calc(var(--mico-size-unit) * 9);--mico-size-40:calc(var(--mico-size-unit) * 10);--mico-size-44:calc(var(--mico-size-unit) * 11);--mico-size-48:calc(var(--mico-size-unit) * 12);--mico-size-52:calc(var(--mico-size-unit) * 13);--mico-size-56:calc(var(--mico-size-unit) * 14);--mico-size-60:calc(var(--mico-size-unit) * 15);--mico-size-64:calc(var(--mico-size-unit) * 16);--mico-size-72:calc(var(--mico-size-unit) * 18);--mico-size-80:calc(var(--mico-size-unit) * 20);--mico-size-96:calc(var(--mico-size-unit) * 24);--mico-size-100:calc(var(--mico-size-unit) * 25);--mico-size-112:calc(var(--mico-size-unit) * 28);--mico-size-128:calc(var(--mico-size-unit) * 32);--mico-size-144:calc(var(--mico-size-unit) * 36);--mico-size-160:calc(var(--mico-size-unit) * 40);--mico-size-176:calc(var(--mico-size-unit) * 44);--mico-size-192:calc(var(--mico-size-unit) * 48);--mico-size-208:calc(var(--mico-size-unit) * 52);--mico-size-224:calc(var(--mico-size-unit) * 56);--mico-size-240:calc(var(--mico-size-unit) * 60);--mico-size-256:calc(var(--mico-size-unit) * 64);--mico-size-260:calc(var(--mico-size-unit) * 68);--mico-size-264:calc(var(--mico-size-unit) * 72);--mico-size-268:calc(var(--mico-size-unit) * 76);--mico-size-272:calc(var(--mico-size-unit) * 80);--mico-size-276:calc(var(--mico-size-unit) * 84);--mico-size-280:calc(var(--mico-size-unit) * 88);--mico-size-284:calc(var(--mico-size-unit) * 92);--mico-size-288:calc(var(--mico-size-unit) * 96);--mico-size-292:calc(var(--mico-size-unit) * 100);--mico-size-296:calc(var(--mico-size-unit) * 104);--mico-size-300:calc(var(--mico-size-unit) * 108);--mico-size-304:calc(var(--mico-size-unit) * 112);--mico-size-308:calc(var(--mico-size-unit) * 116);--mico-size-312:calc(var(--mico-size-unit) * 120);--mico-size-316:calc(var(--mico-size-unit) * 124);--mico-size-320:calc(var(--mico-size-unit) * 128);--mico-size-324:calc(var(--mico-size-unit) * 132);--mico-size-328:calc(var(--mico-size-unit) * 136);--mico-size-332:calc(var(--mico-size-unit) * 140);--mico-size-336:calc(var(--mico-size-unit) * 144);--mico-size-340:calc(var(--mico-size-unit) * 148);--mico-size-344:calc(var(--mico-size-unit) * 152);--mico-size-348:calc(var(--mico-size-unit) * 156);--mico-size-352:calc(var(--mico-size-unit) * 160);--mico-size-356:calc(var(--mico-size-unit) * 164);--mico-size-360:calc(var(--mico-size-unit) * 168);--mico-size-364:calc(var(--mico-size-unit) * 172);--mico-size-368:calc(var(--mico-size-unit) * 176);--mico-size-372:calc(var(--mico-size-unit) * 180);--mico-size-376:calc(var(--mico-size-unit) * 184);--mico-size-380:calc(var(--mico-size-unit) * 188);--mico-size-384:calc(var(--mico-size-unit) * 192);--mico-size-388:calc(var(--mico-size-unit) * 196);--mico-size-392:calc(var(--mico-size-unit) * 200);--mico-size-396:calc(var(--mico-size-unit) * 204);--mico-size-400:calc(var(--mico-size-unit) * 208);--mico-size-fluid-xs:max(var(--mico-size-16),min(3vw,var(--mico-size-32)));--mico-size-fluid-sm:max(var(--mico-size-16),min(4vw,var(--mico-size-56)));--mico-size-fluid-md:max(var(--mico-size-16),min(6vw,var(--mico-size-80)));--mico-size-fluid-lg:max(var(--mico-size-16),min(8vw,var(--mico-size-100)));--mico-size-fluid-xl:max(var(--mico-size-16),min(10vw,var(--mico-size-128)));--mico-size-fluid-2xl:max(var(--mico-size-16),min(12vw,var(--mico-size-192)));--mico-radius-none:0;--mico-radius-xs:1px;--mico-radius-sm:2px;--mico-radius-md:4px;--mico-radius-lg:8px;--mico-radius-xl:12px;--mico-radius-2xl:16px;--mico-radius-full:9999px;--mico-border-none:none;--mico-border-solid:solid;--mico-border-dashed:dashed;--mico-border-dotted:dotted;--mico-border-double:double;--mico-border-groove:groove;--mico-border-ridge:ridge;--mico-border-inset:inset;--mico-border-outset:outset;--mico-border-width-0:0px;--mico-border-width-1:1px;--mico-border-width-2:2px;--mico-border-width-4:4px;--mico-border-width-8:8px;--mico-outline-width-0:0px;--mico-outline-width-1:1px;--mico-outline-width-2:2px;--mico-outline-width-4:4px;--mico-outline-width-8:8px;--mico-outline-style-none:none;--mico-outline-style-solid:solid;--mico-outline-style-dashed:dashed;--mico-outline-style-dotted:dotted;--mico-outline-style-double:double;--mico-outline-offset-0:0px;--mico-outline-offset-1:1px;--mico-outline-offset-2:2px;--mico-outline-offset-4:4px;--mico-outline-offset-8:8px;--mico-divide-width-0:0px;--mico-divide-width-1:1px;--mico-divide-width-2:2px;--mico-divide-width-4:4px;--mico-divide-width-8:8px;--mico-shadow-primary:0px 4.5px 4px -2.8px hsl(from var(--mico-color-primary) h calc(s * 0.55) l);--mico-shadow-secondary:0px 4.5px 4px -2.8px hsl(from var(--mico-color-secondary) h calc(s * 0.55) l);--mico-shadow-accent:0px 4.5px 4px -2.8px hsl(from var(--mico-color-accent) h calc(s * 0.55) l);--mico-shadow-success:0px 4.5px 4px -2.8px hsl(from var(--mico-color-success) h calc(s * 0.55) l);--mico-shadow-error:0px 4.5px 4px -2.8px hsl(from var(--mico-color-error) h calc(s * 0.55) l);--mico-shadow-warning:0px 4.5px 4px -2.8px hsl(from var(--mico-color-warning) h calc(s * 0.55) l);--mico-shadow-info:0px 4.5px 4px -2.8px hsl(from var(--mico-color-info) h calc(s * 0.55) l);--mico-shadow-none-light:var(--mico-value-none);--mico-shadow-xs-light:0 2px 2px rgba(0,0,0,.05);--mico-shadow-sm-light:0 2px 2px rgba(0,0,0,.05);--mico-shadow-md-light:0 4px 6px rgba(0,0,0,.1);--mico-shadow-lg-light:0 10px 15px rgba(0,0,0,.1);--mico-shadow-xl-light:0 20px 25px rgba(0,0,0,.15);--mico-shadow-2xl-light: ;--mico-shadow-3xl-light: ;--mico-shadow-inset-none-light:inset var(--mico-value-none);--mico-shadow-inset-xs-light:inset 0 2px 2px rgba(0,0,0,.05);--mico-shadow-inset-sm-light:inset 0 1px 2px rgba(0,0,0,.05);--mico-shadow-inset-md-light:inset 0 4px 6px rgba(0,0,0,.1);--mico-shadow-inset-lg-light:inset 0 20px 25px rgba(0,0,0,.15);--mico-shadow-inset-xl-light:inset 0 20px 25px rgba(0,0,0,.2);--mico-shadow-inset-2xl-light:inset;--mico-shadow-inset-3xl-light:inset;--mico-shadow-focus:0 0 0 3px rgba(66,153,225,.5);--mico-position-static:static;--mico-position-relative:relative;--mico-position-absolute:absolute;--mico-position-fixed:fixed;--mico-position-sticky:sticky;--mico-display-block:block;--mico-display-inline:inline;--mico-display-inline-block:inline-block;--mico-display-flex:flex;--mico-display-inline-flex:inline-flex;--mico-display-grid:grid;--mico-display-none:none;--mico-box-sizing-border:border-box;--mico-box-sizing-content:content-box;--mico-box-decoration-slice:slice;--mico-box-decoration-clone:clone;--mico-overflow-auto:auto;--mico-overflow-hidden:hidden;--mico-overflow-visible:visible;--mico-overflow-scroll:scroll;--mico-overscroll-auto:auto;--mico-overscroll-contain:contain;--mico-overscroll-none:none;--mico-aspect-ratio-square:1/1;--mico-aspect-ratio-video:16/9;--mico-aspect-ratio-portrait:3/4;--mico-aspect-ratio-landscape:4/3;--mico-aspect-ratio-widescreen:21/9;--mico-aspect-ratio-golden:1.618/1;--mico-float-left:left;--mico-float-right:right;--mico-float-none:none;--mico-clear-left:left;--mico-clear-right:right;--mico-clear-both:both;--mico-object-fit-contain:contain;--mico-object-fit-cover:cover;--mico-object-fit-fill:fill;--mico-object-fit-scale-down:scale-down;--mico-object-position-center:center;--mico-visibility-visible:visible;--mico-visibility-hidden:hidden;--mico-visibility-collapse:collapse;--mico-isolation-isolate:isolate;--mico-isolation-auto:auto;--mico-inset-0:0;--mico-inset-auto:auto;--mico-flex-row:row;--mico-flex-row-reverse:row-reverse;--mico-flex-col:column;--mico-flex-col-reverse:column-reverse;--mico-flex-wrap:wrap;--mico-flex-nowrap:nowrap;--mico-flex-wrap-reverse:wrap-reverse;--mico-justify-start:flex-start;--mico-justify-end:flex-end;--mico-justify-center:center;--mico-justify-between:space-between;--mico-justify-around:space-around;--mico-justify-evenly:space-evenly;--mico-items-start:flex-start;--mico-items-end:flex-end;--mico-items-center:center;--mico-items-baseline:baseline;--mico-items-stretch:stretch;--mico-grid-auto-fit:auto-fit;--mico-grid-auto-fill:auto-fill;--mico-place-items-start:start;--mico-place-items-end:end;--mico-place-items-center:center;--mico-place-items-stretch:stretch;--mico-place-content-start:start;--mico-place-content-end:end;--mico-place-content-center:center;--mico-place-content-stretch:stretch;--mico-place-content-around:space-around;--mico-place-content-between:space-between;--mico-place-content-evenly:space-evenly;--mico-grid-column-count:12;--mico-grid-min-column-width:200px;--mico-grid-row-count:1;--mico-grid-min-row-height:100px;--mico-column-span:1;--mico-row-span:1;--mico-min-column-width:0;--mico-max-column-width:1fr;--mico-min-row-height:0;--mico-max-row-height:1fr;--mico-grid-cols:repeat(var(--mico-grid-column-count,12),minmax(0,1fr));--mico-grid-cols-auto-fit:repeat(auto-fit,minmax(var(--mico-grid-min-column-width,200px),1fr));--mico-grid-rows:repeat(var(--mico-grid-row-count,1),minmax(0,1fr));--mico-col-span:span var(--mico-column-span,1);--mico-row-span:span var(--mico-row-span,1);--mico-grid-flow-row:row;--mico-grid-flow-col:column;--mico-grid-flow-dense:dense;--mico-auto-cols:minmax(var(--mico-min-column-width,0),var(--mico-max-column-width,1fr));--mico-auto-rows:minmax(var(--mico-min-row-height,0),var(--mico-max-row-height,1fr));--mico-gap-xs:var(--mico-size-4);--mico-gap-sm:var(--mico-size-8);--mico-gap-md:var(--mico-size-16);--mico-gap-lg:var(--mico-size-24);--mico-gap-xl:var(--mico-size-32);--mico-bg-none:none;--mico-bg-repeat:repeat;--mico-bg-no-repeat:no-repeat;--mico-bg-repeat-x:repeat-x;--mico-bg-repeat-y:repeat-y;--mico-bg-fixed:fixed;--mico-bg-local:local;--mico-bg-scroll:scroll;--mico-bg-clip-border:border-box;--mico-bg-clip-padding:padding-box;--mico-bg-clip-content:content-box;--mico-filter-blur:blur(8px);--mico-filter-brightness:brightness(1.5);--mico-filter-contrast:contrast(1.2);--mico-filter-grayscale:grayscale(100%);--mico-filter-hue-rotate:hue-rotate(90deg);--mico-filter-invert:invert(100%);--mico-filter-saturate:saturate(2);--mico-filter-sepia:sepia(100%);--mico-opacity-0p:0;--mico-opacity-10p:0.10;--mico-opacity-20p:0.20;--mico-opacity-30p:0.30;--mico-opacity-40p:0.40;--mico-opacity-50p:0.5;--mico-opacity-60p:0.60;--mico-opacity-70p:0.70;--mico-opacity-80p:0.80;--mico-opacity-90p:0.90;--mico-opacity-100p:1;--mico-scale-100:scale(1);--mico-scale-75:scale(0.75);--mico-scale-50:scale(0.5);--mico-rotate-45:rotate(45deg);--mico-rotate-90:rotate(90deg);--mico-translate-x-full:translateX(100%);--mico-translate-y-full:translateY(100%);--mico-table-auto:auto;--mico-table-fixed:fixed;--mico-fill-current:currentColor;--mico-stroke-current:currentColor;--mico-btn-padding-xs:var(--mico-size-8) var(--mico-size-12);--mico-btn-padding-sm:var(--mico-size-12) var(--mico-size-16);--mico-btn-padding-md:var(--mico-size-16) var(--mico-size-24);--mico-btn-padding-lg:var(--mico-size-20) var(--mico-size-32);--mico-btn-padding-xl:var(--mico-size-24) var(--mico-size-40);--mico-btn-font-size-xs:var(--mico-fs-xs);--mico-btn-font-size-sm:var(--mico-fs-sm);--mico-btn-font-size-md:var(--mico-fs-md);--mico-btn-font-size-lg:var(--mico-fs-lg);--mico-btn-font-size-xl:var(--mico-fs-xl);--mico-btn-radius-square:var(--mico-radius-none);--mico-btn-radius-sm:var(--mico-radius-sm);--mico-btn-radius-md:var(--mico-radius-md);--mico-btn-radius-lg:var(--mico-radius-lg);--mico-btn-radius-pill:var(--mico-radius-full);--mico-btn-radius-circle:var(--mico-radius-full);--mico-btn-shadow-none:none;--mico-btn-shadow-sm:var(--mico-shadow-sm);--mico-btn-shadow-md:var(--mico-shadow-md);--mico-btn-shadow-lg:var(--mico-shadow-lg);--mico-btn-icon-gap:var(--mico-size-8);--mico-btn-icon-only-size:var(--mico-size-40);--mico-cursor-auto:auto;--mico-cursor-default:default;--mico-cursor-pointer:pointer;--mico-cursor-wait:wait;--mico-cursor-text:text;--mico-cursor-move:move;--mico-cursor-not-allowed:not-allowed;--mico-cursor-grab:grab;--mico-cursor-grabbing:grabbing;--mico-cursor-help:help;--mico-appearance-none:none;--mico-appearance-auto:auto;--mico-pointer-events-none:none;--mico-pointer-events-auto:auto;--mico-will-change-auto:auto;--mico-will-change-transform:transform;--mico-will-change-opacity:opacity;--mico-will-change-scroll:scroll-position;--mico-bleed-offset-sm:10vw;--mico-bleed-offset-md:20vw;--mico-bleed-offset-lg:30vw;--mico-mask-fade-to-top:linear-gradient(0deg,#000 50%,transparent);--mico-mask-fade-to-bottom:linear-gradient(180deg,#000 50%,transparent);--mico-mask-fade-to-left:linear-gradient(270deg,#000 50%,transparent);--mico-mask-fade-to-right:linear-gradient(90deg,#000 50%,transparent);--mico-mask-fade-short:linear-gradient(180deg,#000 80%,transparent);--mico-mask-fade-long:linear-gradient(180deg,#000 20%,transparent);--mico-fit-content:fit-content;--mico-min-content:min-content;--mico-max-content:max-content;--mico-width-full:100%;--mico-height-full:100%;--mico-width-half:50%;--mico-height-half:50%;--mico-width-quarter:25%;--mico-height-quarter:25%;--mico-width-third:33.33%;--mico-height-third:33.33%;--mico-width-screen:100vw;--mico-height-screen:100vh;--mico-min-width-0:0;--mico-min-height-0:0;--mico-max-width-full:100%;--mico-max-height-full:100%;--mico-align-baseline:baseline;--mico-align-top:top;--mico-align-middle:middle;--mico-align-bottom:bottom;--mico-align-text-top:text-top;--mico-align-text-bottom:text-bottom;--mico-vertical-align-sub:sub;--mico-vertical-align-super:super;--mico-duration-xs:0.2s;--mico-duration-sm:0.5s;--mico-duration-md:0.8s;--mico-duration-lg:1.2s;--mico-duration-xl:2s;--mico-delay-xs:0.1s;--mico-delay-sm:0.2s;--mico-delay-md:0.4s;--mico-delay-lg:0.6s;--mico-delay-xl:0.8s;--mico-anim-default-duration:var(--mico-duration-md);--mico-anim-default-timing:ease-out;--mico-anim-default-fill-mode:both;--mico-ripple-bg-default:hsla(0,0%,100%,.3);--mico-ripple-bg-dark:rgba(0,0,0,.2);--mico-typewriter-cursor-color:currentColor;--mico-animation-none:none;--mico-animation-spin:spin 1s linear infinite;--mico-animation-ping:ping 1s cubic-bezier(0,0,0.2,1) infinite;--mico-animation-pulse:pulse 2s cubic-bezier(0.4,0,0.6,1) infinite;--mico-animation-bounce:bounce 1s infinite;--mico-ease:cubic-bezier(0.25,0.1,0.25,1.0);--mico-ease-in:cubic-bezier(0.42,0,1.0,1.0);--mico-ease-out:cubic-bezier(0,0,0.58,1.0);--mico-ease-in-out:cubic-bezier(0.42,0,0.58,1.0);--mico-ease-elastic:cubic-bezier(0.68,-0.55,0.265,1.55);--mico-ease-bounce:cubic-bezier(0.175,0.885,0.32,1.275);--mico-ease-back:cubic-bezier(0.68,-0.55,0.265,1.55);--mico-ease-spring:cubic-bezier(0.5,0.1,0.1,1);--mico-ease-gravity:cubic-bezier(0.175,0.885,0.32,1.275);--mico-ease-snappy:cubic-bezier(0.1,0.9,0.2,1);--mico-transition-duration-fast:150ms;--mico-transition-duration-normal:300ms;--mico-transition-duration-slow:500ms;--mico-transition-all:all .4s var(--mico-ease);--mico-transition-color:color .4s var(--mico-ease);--mico-transition-background:background .4s var(--mico-ease);--mico-transition-border:border .4s var(--mico-ease);--mico-transition-opacity:opacity .4s var(--mico-ease);--mico-transition-transform:transform .4s var(--mico-ease);--mico-transition-box-shadow:box-shadow .4s var(--mico-ease);--mico-color-primary-2xlight:oklch(from var(--mico-color-primary) calc(l + (1 - l) * 0.3) calc(c * 0.8) h);--mico-color-primary-3xlight:oklch(from var(--mico-color-primary) calc(l + (1 - l) * 0.5) calc(c * 0.6) h);--mico-color-primary-4xlight:oklch(from var(--mico-color-primary) calc(l + (1 - l) * 0.7) calc(c * 0.4) h);--mico-color-primary-5xlight:oklch(from var(--mico-color-primary) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);--mico-color-primary-2xdark:oklch(from var(--mico-color-primary) calc(l * 0.8) calc(c * 1.1) h);--mico-color-primary-3xdark:oklch(from var(--mico-color-primary) calc(l * 0.6) calc(c * 1.2) h);--mico-color-primary-4xdark:oklch(from var(--mico-color-primary) calc(l * 0.4) calc(c * 1.3) h);--mico-color-primary-5xdark:oklch(from var(--mico-color-primary) calc(l * 0.25) calc(c * 1.4) h);--mico-color-secondary-2xlight:oklch(from var(--mico-color-secondary) calc(l + (1 - l) * 0.3) calc(c * 0.8) h);--mico-color-secondary-3xlight:oklch(from var(--mico-color-secondary) calc(l + (1 - l) * 0.5) calc(c * 0.6) h);--mico-color-secondary-4xlight:oklch(from var(--mico-color-secondary) calc(l + (1 - l) * 0.7) calc(c * 0.4) h);--mico-color-secondary-5xlight:oklch(from var(--mico-color-secondary) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);--mico-color-secondary-2xdark:oklch(from var(--mico-color-secondary) calc(l * 0.8) calc(c * 1.1) h);--mico-color-secondary-3xdark:oklch(from var(--mico-color-secondary) calc(l * 0.6) calc(c * 1.2) h);--mico-color-secondary-4xdark:oklch(from var(--mico-color-secondary) calc(l * 0.4) calc(c * 1.3) h);--mico-color-secondary-5xdark:oklch(from var(--mico-color-secondary) calc(l * 0.25) calc(c * 1.4) h);--mico-color-accent-2xlight:oklch(from var(--mico-color-accent) calc(l + (1 - l) * 0.3) calc(c * 0.8) h);--mico-color-accent-3xlight:oklch(from var(--mico-color-accent) calc(l + (1 - l) * 0.5) calc(c * 0.6) h);--mico-color-accent-4xlight:oklch(from var(--mico-color-accent) calc(l + (1 - l) * 0.7) calc(c * 0.4) h);--mico-color-accent-5xlight:oklch(from var(--mico-color-accent) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);--mico-color-accent-2xdark:oklch(from var(--mico-color-accent) calc(l * 0.8) calc(c * 1.1) h);--mico-color-accent-3xdark:oklch(from var(--mico-color-accent) calc(l * 0.6) calc(c * 1.2) h);--mico-color-accent-4xdark:oklch(from var(--mico-color-accent) calc(l * 0.4) calc(c * 1.3) h);--mico-color-accent-5xdark:oklch(from var(--mico-color-accent) calc(l * 0.25) calc(c * 1.4) h);--mico-color-success:#3aa85b;--mico-color-success-2xlight:oklch(from var(--mico-color-success) calc(l + (1 - l) * 0.3) calc(c * 0.8) h);--mico-color-success-3xlight:oklch(from var(--mico-color-success) calc(l + (1 - l) * 0.5) calc(c * 0.6) h);--mico-color-success-4xlight:oklch(from var(--mico-color-success) calc(l + (1 - l) * 0.7) calc(c * 0.4) h);--mico-color-success-5xlight:oklch(from var(--mico-color-success) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);--mico-color-success-2xdark:oklch(from var(--mico-color-success) calc(l * 0.8) calc(c * 1.1) h);--mico-color-success-3xdark:oklch(from var(--mico-color-success) calc(l * 0.6) calc(c * 1.2) h);--mico-color-success-4xdark:oklch(from var(--mico-color-success) calc(l * 0.4) calc(c * 1.3) h);--mico-color-success-5xdark:oklch(from var(--mico-color-success) calc(l * 0.25) calc(c * 1.4) h);--mico-color-warning:#dfa11a;--mico-color-warning-2xlight:oklch(from var(--mico-color-warning) calc(l + (1 - l) * 0.3) calc(c * 0.8) h);--mico-color-warning-3xlight:oklch(from var(--mico-color-warning) calc(l + (1 - l) * 0.5) calc(c * 0.6) h);--mico-color-warning-4xlight:oklch(from var(--mico-color-warning) calc(l + (1 - l) * 0.7) calc(c * 0.4) h);--mico-color-warning-5xlight:oklch(from var(--mico-color-warning) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);--mico-color-warning-2xdark:oklch(from var(--mico-color-warning) calc(l * 0.8) calc(c * 1.1) h);--mico-color-warning-3xdark:oklch(from var(--mico-color-warning) calc(l * 0.6) calc(c * 1.2) h);--mico-color-warning-4xdark:oklch(from var(--mico-color-warning) calc(l * 0.4) calc(c * 1.3) h);--mico-color-warning-5xdark:oklch(from var(--mico-color-warning) calc(l * 0.25) calc(c * 1.4) h);--mico-color-error:#d74745;--mico-color-error-2xlight:oklch(from var(--mico-color-error) calc(l + (1 - l) * 0.3) calc(c * 0.8) h);--mico-color-error-3xlight:oklch(from var(--mico-color-error) calc(l + (1 - l) * 0.5) calc(c * 0.6) h);--mico-color-error-4xlight:oklch(from var(--mico-color-error) calc(l + (1 - l) * 0.7) calc(c * 0.4) h);--mico-color-error-5xlight:oklch(from var(--mico-color-error) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);--mico-color-error-2xdark:oklch(from var(--mico-color-error) calc(l * 0.8) calc(c * 1.1) h);--mico-color-error-3xdark:oklch(from var(--mico-color-error) calc(l * 0.6) calc(c * 1.2) h);--mico-color-error-4xdark:oklch(from var(--mico-color-error) calc(l * 0.4) calc(c * 1.3) h);--mico-color-error-5xdark:oklch(from var(--mico-color-error) calc(l * 0.25) calc(c * 1.4) h);--mico-color-info:#2784d5;--mico-color-info-2xlight:oklch(from var(--mico-color-info) calc(l + (1 - l) * 0.3) calc(c * 0.8) h);--mico-color-info-3xlight:oklch(from var(--mico-color-info) calc(l + (1 - l) * 0.5) calc(c * 0.6) h);--mico-color-info-4xlight:oklch(from var(--mico-color-info) calc(l + (1 - l) * 0.7) calc(c * 0.4) h);--mico-color-info-5xlight:oklch(from var(--mico-color-info) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);--mico-color-info-2xdark:oklch(from var(--mico-color-info) calc(l * 0.8) calc(c * 1.1) h);--mico-color-info-3xdark:oklch(from var(--mico-color-info) calc(l * 0.6) calc(c * 1.2) h);--mico-color-info-4xdark:oklch(from var(--mico-color-info) calc(l * 0.4) calc(c * 1.3) h);--mico-color-info-5xdark:oklch(from var(--mico-color-info) calc(l * 0.25) calc(c * 1.4) h);--mico-color-visited:#8059bb;--mico-color-visited-2xlight:oklch(from var(--mico-color-visited) calc(l + (1 - l) * 0.3) calc(c * 0.8) h);--mico-color-visited-3xlight:oklch(from var(--mico-color-visited) calc(l + (1 - l) * 0.5) calc(c * 0.6) h);--mico-color-visited-4xlight:oklch(from var(--mico-color-visited) calc(l + (1 - l) * 0.7) calc(c * 0.4) h);--mico-color-visited-5xlight:oklch(from var(--mico-color-visited) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);--mico-color-visited-2xdark:oklch(from var(--mico-color-visited) calc(l * 0.8) calc(c * 1.1) h);--mico-color-visited-3xdark:oklch(from var(--mico-color-visited) calc(l * 0.6) calc(c * 1.2) h);--mico-color-visited-4xdark:oklch(from var(--mico-color-visited) calc(l * 0.4) calc(c * 1.3) h);--mico-color-visited-5xdark:oklch(from var(--mico-color-visited) calc(l * 0.25) calc(c * 1.4) h);--mico-color-black:#030304;--mico-color-black-2xlight:oklch(from var(--mico-color-black) calc(l + (1 - l) * 0.3) calc(c * 0.8) h);--mico-color-black-3xlight:oklch(from var(--mico-color-black) calc(l + (1 - l) * 0.5) calc(c * 0.6) h);--mico-color-black-4xlight:oklch(from var(--mico-color-black) calc(l + (1 - l) * 0.7) calc(c * 0.4) h);--mico-color-black-5xlight:oklch(from var(--mico-color-black) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);--mico-color-black-2xdark:oklch(from var(--mico-color-black) calc(l * 0.8) calc(c * 1.1) h);--mico-color-black-3xdark:oklch(from var(--mico-color-black) calc(l * 0.6) calc(c * 1.2) h);--mico-color-black-4xdark:oklch(from var(--mico-color-black) calc(l * 0.4) calc(c * 1.3) h);--mico-color-black-5xdark:oklch(from var(--mico-color-black) calc(l * 0.25) calc(c * 1.4) h);--mico-color-gray:#616369;--mico-color-gray-2xlight:oklch(from var(--mico-color-gray) calc(l + (1 - l) * 0.3) calc(c * 0.8) h);--mico-color-gray-3xlight:oklch(from var(--mico-color-gray) calc(l + (1 - l) * 0.5) calc(c * 0.6) h);--mico-color-gray-4xlight:oklch(from var(--mico-color-gray) calc(l + (1 - l) * 0.7) calc(c * 0.4) h);--mico-color-gray-5xlight:oklch(from var(--mico-color-gray) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);--mico-color-gray-2xdark:oklch(from var(--mico-color-gray) calc(l * 0.8) calc(c * 1.1) h);--mico-color-gray-3xdark:oklch(from var(--mico-color-gray) calc(l * 0.6) calc(c * 1.2) h);--mico-color-gray-4xdark:oklch(from var(--mico-color-gray) calc(l * 0.4) calc(c * 1.3) h);--mico-color-gray-5xdark:oklch(from var(--mico-color-gray) calc(l * 0.25) calc(c * 1.4) h);--mico-color-white:#edeef2;--mico-color-white-2xlight:oklch(from var(--mico-color-white) calc(l + (1 - l) * 0.3) calc(c * 0.8) h);--mico-color-white-3xlight:oklch(from var(--mico-color-white) calc(l + (1 - l) * 0.5) calc(c * 0.6) h);--mico-color-white-4xlight:oklch(from var(--mico-color-white) calc(l + (1 - l) * 0.7) calc(c * 0.4) h);--mico-color-white-5xlight:oklch(from var(--mico-color-white) calc(l + (1 - l) * 0.85) calc(c * 0.2) h);--mico-color-white-2xdark:oklch(from var(--mico-color-white) calc(l * 0.8) calc(c * 1.1) h);--mico-color-white-3xdark:oklch(from var(--mico-color-white) calc(l * 0.6) calc(c * 1.2) h);--mico-color-white-4xdark:oklch(from var(--mico-color-white) calc(l * 0.4) calc(c * 1.3) h);--mico-color-white-5xdark:oklch(from var(--mico-color-white) calc(l * 0.25) calc(c * 1.4) h);--mico-color-black-whisper:oklch(from var(--mico-color-black) l c h/0.1);--mico-color-black-breath:oklch(from var(--mico-color-black) l c h/0.2);--mico-color-black-mist:oklch(from var(--mico-color-black) l c h/0.3);--mico-color-black-veil:oklch(from var(--mico-color-black) l c h/0.4);--mico-color-black-shadow:oklch(from var(--mico-color-black) l c h/0.5);--mico-color-black-shroud:oklch(from var(--mico-color-black) l c h/0.6);--mico-color-black-cloak:oklch(from var(--mico-color-black) l c h/0.7);--mico-color-black-eclipse:oklch(from var(--mico-color-black) l c h/0.8);--mico-color-black-void:oklch(from var(--mico-color-black) l c h/0.9);--mico-color-gray-whisper:oklch(from var(--mico-color-gray) l c h/0.1);--mico-color-gray-breath:oklch(from var(--mico-color-gray) l c h/0.2);--mico-color-gray-mist:oklch(from var(--mico-color-gray) l c h/0.3);--mico-color-gray-veil:oklch(from var(--mico-color-gray) l c h/0.4);--mico-color-gray-shadow:oklch(from var(--mico-color-gray) l c h/0.5);--mico-color-gray-shroud:oklch(from var(--mico-color-gray) l c h/0.6);--mico-color-gray-cloak:oklch(from var(--mico-color-gray) l c h/0.7);--mico-color-gray-eclipse:oklch(from var(--mico-color-gray) l c h/0.8);--mico-color-gray-void:oklch(from var(--mico-color-gray) l c h/0.9);--mico-color-white-whisper:oklch(from var(--mico-color-white) l c h/0.1);--mico-color-white-breath:oklch(from var(--mico-color-white) l c h/0.2);--mico-color-white-mist:oklch(from var(--mico-color-white) l c h/0.3);--mico-color-white-veil:oklch(from var(--mico-color-white) l c h/0.4);--mico-color-white-shadow:oklch(from var(--mico-color-white) l c h/0.5);--mico-color-white-shroud:oklch(from var(--mico-color-white) l c h/0.6);--mico-color-white-cloak:oklch(from var(--mico-color-white) l c h/0.7);--mico-color-white-eclipse:oklch(from var(--mico-color-white) l c h/0.8);--mico-color-white-void:oklch(from var(--mico-color-white) l c h/0.9);--mico-color-primary-whisper:oklch(from var(--mico-color-primary) l c h/0.1);--mico-color-primary-breath:oklch(from var(--mico-color-primary) l c h/0.2);--mico-color-primary-mist:oklch(from var(--mico-color-primary) l c h/0.3);--mico-color-primary-veil:oklch(from var(--mico-color-primary) l c h/0.4);--mico-color-primary-shadow:oklch(from var(--mico-color-primary) l c h/0.5);--mico-color-secondary-whisper:oklch(from var(--mico-color-secondary) l c h/0.1);--mico-color-secondary-breath:oklch(from var(--mico-color-secondary) l c h/0.2);--mico-color-secondary-mist:oklch(from var(--mico-color-secondary) l c h/0.3);--mico-color-secondary-veil:oklch(from var(--mico-color-secondary) l c h/0.4);--mico-color-secondary-shadow:oklch(from var(--mico-color-secondary) l c h/0.5);--mico-color-accent-whisper:oklch(from var(--mico-color-accent) l c h/0.1);--mico-color-accent-breath:oklch(from var(--mico-color-accent) l c h/0.2);--mico-color-accent-mist:oklch(from var(--mico-color-accent) l c h/0.3);--mico-color-accent-veil:oklch(from var(--mico-color-accent) l c h/0.4);--mico-color-accent-shadow:oklch(from var(--mico-color-accent) l c h/0.5);--mico-color-red:#d40c1a;--mico-color-red-2xlight:oklch(from var(--mico-color-red) calc(l + (1 - l) * 0.4) calc(c * 0.7) h);--mico-color-red-3xlight:oklch(from var(--mico-color-red) calc(l + (1 - l) * 0.6) calc(c * 0.5) h);--mico-color-red-4xlight:oklch(from var(--mico-color-red) calc(l + (1 - l) * 0.75) calc(c * 0.3) h);--mico-color-red-5xlight:oklch(from var(--mico-color-red) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);--mico-color-red-2xdark:oklch(from var(--mico-color-red) calc(l * 0.85) calc(c * 1.05) h);--mico-color-red-3xdark:oklch(from var(--mico-color-red) calc(l * 0.7) calc(c * 1.1) h);--mico-color-red-4xdark:oklch(from var(--mico-color-red) calc(l * 0.5) calc(c * 1.15) h);--mico-color-red-5xdark:oklch(from var(--mico-color-red) calc(l * 0.35) calc(c * 1.2) h);--mico-color-yellow:#fffc2e;--mico-color-yellow-2xlight:oklch(from var(--mico-color-yellow) calc(l + (1 - l) * 0.4) calc(c * 0.7) h);--mico-color-yellow-3xlight:oklch(from var(--mico-color-yellow) calc(l + (1 - l) * 0.6) calc(c * 0.5) h);--mico-color-yellow-4xlight:oklch(from var(--mico-color-yellow) calc(l + (1 - l) * 0.75) calc(c * 0.3) h);--mico-color-yellow-5xlight:oklch(from var(--mico-color-yellow) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);--mico-color-yellow-2xdark:oklch(from var(--mico-color-yellow) calc(l * 0.85) calc(c * 1.05) calc(h - 5));--mico-color-yellow-3xdark:oklch(from var(--mico-color-yellow) calc(l * 0.7) calc(c * 1.1) calc(h - 8));--mico-color-yellow-4xdark:oklch(from var(--mico-color-yellow) calc(l * 0.55) calc(c * 1.15) calc(h - 10));--mico-color-yellow-5xdark:oklch(from var(--mico-color-yellow) calc(l * 0.4) calc(c * 1.2) calc(h - 12));--mico-color-green:#3bb430;--mico-color-green-2xlight:oklch(from var(--mico-color-green) calc(l + (1 - l) * 0.4) calc(c * 0.7) h);--mico-color-green-3xlight:oklch(from var(--mico-color-green) calc(l + (1 - l) * 0.6) calc(c * 0.5) h);--mico-color-green-4xlight:oklch(from var(--mico-color-green) calc(l + (1 - l) * 0.75) calc(c * 0.3) h);--mico-color-green-5xlight:oklch(from var(--mico-color-green) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);--mico-color-green-2xdark:oklch(from var(--mico-color-green) calc(l * 0.85) calc(c * 1.05) h);--mico-color-green-3xdark:oklch(from var(--mico-color-green) calc(l * 0.7) calc(c * 1.1) h);--mico-color-green-4xdark:oklch(from var(--mico-color-green) calc(l * 0.5) calc(c * 1.15) h);--mico-color-green-5xdark:oklch(from var(--mico-color-green) calc(l * 0.35) calc(c * 1.2) h);--mico-color-blue:#005fdc;--mico-color-blue-2xlight:oklch(from var(--mico-color-blue) calc(l + (1 - l) * 0.4) calc(c * 0.7) h);--mico-color-blue-3xlight:oklch(from var(--mico-color-blue) calc(l + (1 - l) * 0.6) calc(c * 0.5) h);--mico-color-blue-4xlight:oklch(from var(--mico-color-blue) calc(l + (1 - l) * 0.75) calc(c * 0.3) h);--mico-color-blue-5xlight:oklch(from var(--mico-color-blue) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);--mico-color-blue-2xdark:oklch(from var(--mico-color-blue) calc(l * 0.85) calc(c * 1.05) h);--mico-color-blue-3xdark:oklch(from var(--mico-color-blue) calc(l * 0.7) calc(c * 1.1) h);--mico-color-blue-4xdark:oklch(from var(--mico-color-blue) calc(l * 0.5) calc(c * 1.15) h);--mico-color-blue-5xdark:oklch(from var(--mico-color-blue) calc(l * 0.35) calc(c * 1.2) h);--mico-color-indigo:#505ac8;--mico-color-indigo-2xlight:oklch(from var(--mico-color-indigo) calc(l + (1 - l) * 0.4) calc(c * 0.7) h);--mico-color-indigo-3xlight:oklch(from var(--mico-color-indigo) calc(l + (1 - l) * 0.6) calc(c * 0.5) h);--mico-color-indigo-4xlight:oklch(from var(--mico-color-indigo) calc(l + (1 - l) * 0.75) calc(c * 0.3) h);--mico-color-indigo-5xlight:oklch(from var(--mico-color-indigo) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);--mico-color-indigo-2xdark:oklch(from var(--mico-color-indigo) calc(l * 0.85) calc(c * 1.05) h);--mico-color-indigo-3xdark:oklch(from var(--mico-color-indigo) calc(l * 0.7) calc(c * 1.1) h);--mico-color-indigo-4xdark:oklch(from var(--mico-color-indigo) calc(l * 0.5) calc(c * 1.15) h);--mico-color-indigo-5xdark:oklch(from var(--mico-color-indigo) calc(l * 0.35) calc(c * 1.2) h);--mico-color-purple:#7f28b6;--mico-color-purple-2xlight:oklch(from var(--mico-color-purple) calc(l + (1 - l) * 0.4) calc(c * 0.7) h);--mico-color-purple-3xlight:oklch(from var(--mico-color-purple) calc(l + (1 - l) * 0.6) calc(c * 0.5) h);--mico-color-purple-4xlight:oklch(from var(--mico-color-purple) calc(l + (1 - l) * 0.75) calc(c * 0.3) h);--mico-color-purple-5xlight:oklch(from var(--mico-color-purple) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);--mico-color-purple-2xdark:oklch(from var(--mico-color-purple) calc(l * 0.85) calc(c * 1.05) h);--mico-color-purple-3xdark:oklch(from var(--mico-color-purple) calc(l * 0.7) calc(c * 1.1) h);--mico-color-purple-4xdark:oklch(from var(--mico-color-purple) calc(l * 0.5) calc(c * 1.15) h);--mico-color-purple-5xdark:oklch(from var(--mico-color-purple) calc(l * 0.35) calc(c * 1.2) h);--mico-color-pink:#e7688b;--mico-color-pink-2xlight:oklch(from var(--mico-color-pink) calc(l + (1 - l) * 0.4) calc(c * 0.7) h);--mico-color-pink-3xlight:oklch(from var(--mico-color-pink) calc(l + (1 - l) * 0.6) calc(c * 0.5) h);--mico-color-pink-4xlight:oklch(from var(--mico-color-pink) calc(l + (1 - l) * 0.75) calc(c * 0.3) h);--mico-color-pink-5xlight:oklch(from var(--mico-color-pink) calc(l + (1 - l) * 0.88) calc(c * 0.15) h);--mico-color-pink-2xdark:oklch(from var(--mico-color-pink) calc(l * 0.85) calc(c * 1.05) h);--mico-color-pink-3xdark:oklch(from var(--mico-color-pink) calc(l * 0.7) calc(c * 1.1) h);--mico-color-pink-4xdark:oklch(from var(--mico-color-pink) calc(l * 0.5) calc(c * 1.15) h);--mico-color-pink-5xdark:oklch(from var(--mico-color-pink) calc(l * 0.35) calc(c * 1.2) h)}@media (prefers-color-scheme:dark){:root{--mico-shadow-none-dark:var(--mico-value-none);--mico-shadow-xs-dark:0 1px 2px hsla(0,0%,100%,.05);--mico-shadow-sm-dark:0 1px 2px hsla(0,0%,100%,.05);--mico-shadow-md-dark:0 4px 6px hsla(0,0%,100%,.1);--mico-shadow-lg-dark:0 10px 15px hsla(0,0%,100%,.1);--mico-shadow-xl-dark:0 20px 25px hsla(0,0%,100%,.15);--mico-shadow-2xl-light: ;--mico-shadow-3xl-light: ;--mico-shadow-inset-none-dark:inset var(--mico-value-none);--mico-shadow-inset-xs-dark:inset 0 1px 2px hsla(0,0%,100%,.05);--mico-shadow-inset-sm-dark:inset 0 1px 2px hsla(0,0%,100%,.05);--mico-shadow-inset-md-dark:inset 0 4px 6px hsla(0,0%,100%,.1);--mico-shadow-inset-lg-light:inset 0 20px 25px rgba(0,0,0,.15);--mico-shadow-inset-xl-light:inset 0 20px 25px rgba(0,0,0,.2);--mico-shadow-inset-2xl-light:inset;--mico-shadow-inset-3xl-light:inset;--mico-shadow-focus:0 0 0 3px rgba(191,219,254,.6)}}@media (prefers-contrast:high){:root{--mico-shadow-focus:0 0 0 4px #000;--mico-shadow-sm-contrast:0 0 0 1px currentColor;--mico-shadow-md-contrast:0 0 0 2px currentColor;--mico-shadow-lg-contrast:0 0 0 3px currentColor;--mico-shadow-xl-contrast:0 0 0 4px currentColor}}